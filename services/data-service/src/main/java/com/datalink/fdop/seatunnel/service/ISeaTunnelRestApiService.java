package com.datalink.fdop.seatunnel.service;

import com.datalink.fdop.seatunnel.api.domain.SeaTunnelSubmitConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.Job;
import com.datalink.fdop.seatunnel.api.restapi.domain.JobInfo;

import java.util.List;

public interface ISeaTunnelRestApiService {

    /**
     * 提交 SeaTunnel任务
     * @param modelConfig 数据模型配置
     * @return  SeaTunnel 任务信息
     */
    JobInfo submitJob(SeaTunnelSubmitConfig modelConfig);

    /**
     * 获取执行中的任务情况（如果任务执行完成，接口会查询不到）
     * @param jobId JobId
     * @return 任务执行信息
     */
    Job getJob(String jobId);


    /**
     * 停止正在运行中的任务
     * @param jobId JobId
     * @return JobId
     */
    Job stopJob(String jobId);


    /**
     * 查询所有已完成的任务
     * @param state 根据任务状态查询
     * @return 任务集合信息
     */
    List<Job> finishedJobs(String state);

}
