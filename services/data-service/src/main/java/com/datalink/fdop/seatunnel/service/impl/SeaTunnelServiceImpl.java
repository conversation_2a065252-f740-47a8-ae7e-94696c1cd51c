package com.datalink.fdop.seatunnel.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.DataModelType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.element.api.RemoteEntityStrureService;
import com.datalink.fdop.element.api.RemoteEntityTableService;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.param.api.RemoteParamService;
import com.datalink.fdop.seatunnel.api.domain.DataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.FileModelOptions;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelEnv;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;
import com.datalink.fdop.seatunnel.factory.SeaTunnelConnectorFactory;
import com.datalink.fdop.seatunnel.service.ISeaTunnelService;
import com.datalink.fdop.seatunnel.utils.SQLParse;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SeaTunnelServiceImpl implements ISeaTunnelService {

    @Autowired
    private RemoteJdbcService remoteJdbcService;

    @Autowired
    private RemoteDriveService remoteDriveService;

    @Autowired
    private RemoteEntityTableService remoteEntityTableService;

    @Autowired
    private RemoteEntityStrureService remoteEntityStrureService;

    @Autowired
    private RemoteParamService remoteParamService;

    @Override
    public List<SeaTunnelField> getDataModelOptionsFields(DataModelOptions options) {
        List<SeaTunnelField> seaTunnelFields = new ArrayList<>();
        if (options.getModelType() == DataModelType.DATASOURCE) {
            List<Field> tableFields = this.getRemoteDataBaseTableFields(options.getId(), options.getDatabaseName(), options.getTableName());
            for (Field field : tableFields) {
                SeaTunnelField seaTunnelField = new SeaTunnelField(IdWorker.getId(), field.getFieldName(), field.getFieldType(), field.getLength(), field.getDecimalLength(), field.getIsPk());
                seaTunnelFields.add(seaTunnelField);
            }
        } else if (options.getModelType() == DataModelType.ENTITY) {
            List<DataEntityStructureVo> dataEntityFields = this.getRemoteDataEntityFields(options.getTenantId(), options.getId());
            for (DataEntityStructureVo field : dataEntityFields) {
                // 不需要映射内置列
                if (field.getEntityInsertType() == EntityInsertType.BUILTIN) {
                    continue;
                }
                SeaTunnelField seaTunnelField = new SeaTunnelField(field.getId(), field.getCode(), field.getFieldType().getDesc(), field.getLength(), field.getDecimalLength(), field.getIsPk());
                seaTunnelFields.add(seaTunnelField);
            }
        } else if (options.getModelType() == DataModelType.SQL) {
            // 动态解析sql中的列 字段类型
            try {
                seaTunnelFields = SQLParse.extractColumns(options.getQuerySql());
            } catch (JSQLParserException e) {
                throw new ServiceException("动态SQL列解析失败" + e.getMessage());
            }
        }
        return seaTunnelFields;
    }

    @Override
    public String generateSeaTunnelRunConfig(SeaTunnelEnv env, DataModelOptions sourceOptions, DataModelOptions sinkOptions) {
        // 初始化运行环境、源、目标的配置信息
        initializeConfigurationInformation(env, sourceOptions, sinkOptions);
        StringBuilder sTConfigBuilder = new StringBuilder();
        // 否需要ST Transform 校验属性列中是否包含特殊字符，如果包含特殊字符并且未开启upsert的就不启用ST Transform （因为只有在 INSERT INTO的时候才会不需要Transform做源表列与目标表列的映射）
        boolean enableTransform = false;
        // 读取系统配置是否开启Transform（全局设置）
        // 获取SeaTunnel系统参数配置
        JSONObject seaTunnelConfig = getSeaTunnelConfig();
        if (seaTunnelConfig != null) {
            // 读取系统配置是否开启Transform（全局设置）
            if (seaTunnelConfig.getBoolean("enableTransform") != null && seaTunnelConfig.getBoolean("enableTransform")) {
                enableTransform = true;
            }
        }
        // 开启全局transform 则按照具体任务系统配置来
        if (enableTransform) {
            enableTransform = isEnableTransform(sourceOptions.getSeaTunnelFields(), sinkOptions.getIsUpsert());
        }
        // 预先加载配置，防止一些对象的值没被更新进去
        String envConfig = this.generateSeaTunnelRunEnvConfig(env);
        // 配置文件敏感信息加密方式
        String shadeIdentifier = env.getShadeIdentifier();
        String sourceConfig = this.generateSeaTunnelSourceConfig(shadeIdentifier, sourceOptions, sinkOptions, enableTransform);
        //log.info("sinkOptions info {}",JSON.toJSONString(sinkOptions));
        String sinkConfig = this.generateSeaTunnelSinkConfig(shadeIdentifier, sinkOptions);
        // 初始化 ST env
        sTConfigBuilder.append(envConfig).append("\n");
        // 初始化 ST source
        sTConfigBuilder.append(sourceConfig).append("\n");
        if (enableTransform) {
            //初始化 ST transform
            sTConfigBuilder.append(this.generateSeaTunnelTransformConfig(sourceOptions, sinkOptions)).append("\n");
        }
        //sTConfigBuilder.append(this.generateSeaTunnelTransformConfig(sourceOptions, sinkOptions)).append("\n");
        // 初始化 ST sink
        sTConfigBuilder.append(sinkConfig).append("\n");
        return sTConfigBuilder.toString();
    }


    @Override
    public String generateSeaTunnelRunConfigSinkFile(SeaTunnelEnv env, DataModelOptions sourceOptions, FileModelOptions sinkOptions) {
        StringBuilder sTConfigBuilder = new StringBuilder();
        // 文件任务不需要checkpoints
        env.setCheckpointInterval(null);
        // 预先加载配置，防止一些对象的值没被更新进去
        String envConfig = this.generateSeaTunnelRunEnvConfig(env);
        // 配置文件敏感信息加密方式
        String shadeIdentifier = env.getShadeIdentifier();
        // 读取源数据库连接信息
        DataSource sourceDataSource = this.getDataSourceInfoById(sourceOptions);
        // 添加源数据模型数据源属性
        sourceOptions.setDataSourceBasicInfo(sourceDataSource.getDataSourceBasicInfo());
        sourceOptions.setSourceDbType(sourceDataSource.getType());
        // 更新源字段列映射
        List<SeaTunnelField> sourceFields = this.getEntityFieldsMappingFields(sourceOptions);
        sourceOptions.setSeaTunnelFields(sourceFields);
        String sourceConfig = this.generateSeaTunnelSourceConfig(shadeIdentifier, sourceOptions, null, false);
        // 初始化 ST env
        sTConfigBuilder.append(envConfig).append("\n");
        // 初始化 ST source
        sTConfigBuilder.append(sourceConfig).append("\n");
        // 初始化 ST sink File
        sTConfigBuilder.append(this.generateSeaTunnelSinkConfig(shadeIdentifier, sinkOptions)).append("\n");
        return sTConfigBuilder.toString();
    }

    @Override
    public List<SeaTunnelField> getEntityFieldsMappingFields(DataModelOptions options) {
        if (options.getModelType() == DataModelType.ENTITY) {
            // 获取实体内置列
            List<DataEntityStructureVo> entityFields = this.getRemoteDataEntityFields(options.getTenantId(), options.getId());
            if (options.getSeaTunnelFields() == null) {
                List<SeaTunnelField> entitySeatunnelFieldList = entityFields.stream().filter(field -> field.getEntityInsertType() != EntityInsertType.BUILTIN).map(field -> {
                    SeaTunnelField seaTunnelField = new SeaTunnelField(field.getId(), field.getCode(), field.getFieldType().getDesc(), field.getLength(), field.getDecimalLength(), field.getIsPk());
                    return seaTunnelField;
                }).collect(Collectors.toList());
                options.setSeaTunnelFields(entitySeatunnelFieldList);
            }
            // 查询实体字段与表字段映射关系
            List<DataEntityTableMapping> tableMappings = this.getRemoteEntityTableMapping(options.getTenantId(), options.getId());
            List<SeaTunnelField> seaTunnelFields = options.getSeaTunnelFields().stream().map(field -> {
                for (DataEntityTableMapping mapping : tableMappings) {
                    // 更新为实体映射字段
                    if (field.getFieldName().equals(mapping.getEntityFieldCode())) {
                        field.setFieldName(mapping.getFieldName());
                        break;
                    }
                }
                return field;
            }).collect(Collectors.toList());
            // 内置列
            List<SeaTunnelField> builtInFields = entityFields.stream().filter(field -> field.getEntityInsertType() == EntityInsertType.BUILTIN).map(field -> {
                String builtInFieldName = null;
                for (DataEntityTableMapping mapping : tableMappings) {
                    // 更新为实体映射字段
                    if (field.getCode().equals(mapping.getEntityFieldCode())) {
                        builtInFieldName = mapping.getFieldName();
                        break;
                    }
                }
                if (builtInFieldName == null) {
                    builtInFieldName = field.getCode();
                }
                return new SeaTunnelField(field.getId(), builtInFieldName, field.getFieldType().getDesc(), field.getLength(), field.getDecimalLength(), field.getIsPk());
            }).collect(Collectors.toList());
            options.setBuiltInFields(builtInFields);
            return seaTunnelFields;
        }
        return options.getSeaTunnelFields();
    }

    @Override
    public DataSource getDataModelOptionsDataSource(DataModelOptions options) {
        return this.getDataSourceInfoById(options);
    }

    /**
     * 生成ST源配置
     *
     * @param sourceOptions 源信息
     * @return ST源配置
     */
    private String generateSeaTunnelSourceConfig(String shadeIdentifier, DataModelOptions sourceOptions, DataModelOptions sinkOptions, boolean enableTransform) {
//        JSONObject sourceConfig = new JSONObject();
//        JSONObject source = new JSONObject();
//        // 设置数据源
//        DataSource dataSource = this.getDataSourceInfoById(sourceOptions);
//        sourceOptions.setDataSourceBasicInfo(dataSource.getDataSourceBasicInfo());
//        // 获取数据模型对应的ST Connector Source
//        SeaTunnelConnector sourceInstance = SeaTunnelConnectorFactory.getSourceInstance(dataSource.getType(), sourceOptions);
//        source.put(sourceInstance.getConnectorType().getCode(), sourceInstance.getConnectorInfo());
//        sourceConfig.put("source", source);
        // 获取数据模型对应的ST Connector Sink
        String sourceConnectorInfo = SeaTunnelConnectorFactory.getSourceInstance(shadeIdentifier, sourceOptions.getSourceDbType(), sourceOptions, sinkOptions, enableTransform);
        StringBuilder sourceBuilder = new StringBuilder();
        sourceBuilder.append("source {\n");
        sourceBuilder.append(sourceConnectorInfo);
        sourceBuilder.append("}\n");
        return sourceBuilder.toString();
    }


    private String generateSeaTunnelTransformConfig(DataModelOptions sourceOptions, DataModelOptions sinkOptions) {
        // transform支持很多种类型，目前我们只需要实现sql，具体参考连接 https://seatunnel.apache.org/docs/2.3.3/transform-v2/sql/
        StringBuilder transformBuilder = new StringBuilder();
        transformBuilder.append("transform {\n");
        // transform FieldMapper 这里只是把某个列变成新的列，并不是列字段一一映射
        // transformBuilder.append(this.generateSeaTunnelTransformFieldMapperConfig(sourceOptions, sinkOptions));
        // sql 默认需要transform sql
        transformBuilder.append(SeaTunnelConnectorFactory.getTransformSqlInstance(sourceOptions, sinkOptions));
        transformBuilder.append("}\n");
        return transformBuilder.toString();
    }


    /**
     * 生成ST目标配置
     *
     * @param sinkOptions 目标信息
     * @return ST目标配置
     */
    private String generateSeaTunnelSinkConfig(String shadeIdentifier, DataModelOptions sinkOptions) {
//        JSONObject sinkConfig = new JSONObject();
//        JSONObject sink = new JSONObject();
//        // 设置数据源
//        DataSource dataSource = this.getDataSourceInfoById(sinkOptions);
//        sinkOptions.setDataSourceBasicInfo(dataSource.getDataSourceBasicInfo());
//        // 获取数据模型对应的ST Connector Sink
//        SeaTunnelConnector sinkInstance = SeaTunnelConnectorFactory.getSinkInstance(dataSource.getType(), sinkOptions);
//        sink.put(sinkInstance.getConnectorType().getCode(),sinkInstance.getConnectorInfo());
//        sinkConfig.put("sink",sink);
//        return this.jsonFormat(sinkConfig);
        // 获取数据模型对应的ST Connector Sink
        String sinkConnectorInfo = SeaTunnelConnectorFactory.getSinkInstance(shadeIdentifier, sinkOptions.getSinkDbType(), sinkOptions);
        StringBuilder sinkBuilder = new StringBuilder();
        sinkBuilder.append("sink {\n");
        sinkBuilder.append(sinkConnectorInfo);
        sinkBuilder.append("}\n");
        return sinkBuilder.toString();
    }

    private String generateSeaTunnelSinkConfig(String shadeIdentifier, FileModelOptions sinkOptions) {
        // 获取数据模型对应的ST Connector Sink File
        String sinkConnectorInfo = SeaTunnelConnectorFactory.getFileSinkInstance(shadeIdentifier, sinkOptions);
        StringBuilder sinkBuilder = new StringBuilder();
        sinkBuilder.append("sink {\n");
        sinkBuilder.append(sinkConnectorInfo);
        sinkBuilder.append("}\n");
        return sinkBuilder.toString();
    }

    /**
     * 获取数据模型对应的数据源类型
     *
     * @param options 数据模型对象
     * @return 数据源类型
     */
    @Override
    public DataSource getDataSourceInfoById(DataModelOptions options) {
        if (options.getModelType() == DataModelType.DATASOURCE || options.getModelType() == DataModelType.SQL) {
            //数据源默认会选择库表 不需要更新库表信息
            return this.getRemoteDataSourceInfoById(options.getTenantId(), options.getId());
        } else if (options.getModelType() == DataModelType.ENTITY) {
            DataEntityTable dataEntityTable = this.getRemoteDataEntityTable(options.getTenantId(), options.getId());
            // 更新实体映射的库表信息
            options.setDatabaseName(dataEntityTable.getDatabaseName());
            options.setTableName(dataEntityTable.getTableName());
            return this.getRemoteDataSourceInfoById(options.getTenantId(), dataEntityTable.getDataSourceId());
        }
        return null;
    }

    @Override
    public String transformPlaceholderSeaTunnelRunConfig(SeaTunnelEnv env, DataModelOptions sourceOptions, DataModelOptions sinkOptions, String submitConfig) {
        // 提取加密方式
        String shadeIdentifier = extractConfigValue(submitConfig, "shade.identifier");
        // 获取已定义的通配符集合
        Map<String, String> placeholders = definePlaceholders(env, sourceOptions, sinkOptions, shadeIdentifier);
        // 使用正则表达式和替换逻辑来遍历并替换所有占位符
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(submitConfig);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String value = placeholders.getOrDefault(matcher.group(), "");
            if (StringUtils.isNotEmpty(value)) {
                matcher.appendReplacement(result, Matcher.quoteReplacement(value));
            }
        }
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 定义通配符
     */
    private Map<String, String> definePlaceholders(SeaTunnelEnv env, DataModelOptions sourceOptions, DataModelOptions sinkOptions, String shadeIdentifier) {
        // source
        // jdbc sourceJdbcUrl sourceDriverClassName sourceUsername sourcePassword
        // iceberg sourceCatalogName sourceMetastoreUri sourceWarehouse sourceHadoopConfPath sourceKerberosPrincipal sourceKerberosKeytabPath sourceKerberosKrb5ConfPath
        // doris sourceFeNodes sourceUsername sourcePassword
        // hive sourceMetastoreUri sourceWarehouse sourceHdfsSitePath sourceHiveSitePath sourceKerberosPrincipal sourceKerberosKeytabPath sourceKerberosKrb5ConfPath
        // sink
        // jdbc sinkJdbcUrl sinkDriverClassName sinkUsername sinkPassword
        // iceberg sinkCatalogName sinkMetastoreUri sinkWarehouse sinkHadoopConfPath sinkKerberosPrincipal sinkKerberosKeytabPath sinkKerberosKrb5ConfPath
        // doris sinkFeNodes sinkUsername sinkPassword
        // hive sinkMetastoreUri sinkWarehouse sinkHdfsSitePath sinkHiveSitePath sinkKerberosPrincipal sinkKerberosKeytabPath sinkKerberosKrb5ConfPath
        // 初始化运行环境、源、目标的配置信息
        initializeConfigurationInformation(env, sourceOptions, sinkOptions);
        DataSourceBasicInfo source = new DataSourceBasicInfo();
        if (sourceOptions.getDataSourceBasicInfo() != null) {
            source = JSONObject.parseObject(sourceOptions.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        }
        DataSourceBasicInfo sink = new DataSourceBasicInfo();
        if (sinkOptions.getDataSourceBasicInfo() != null) {
            sink = JSONObject.parseObject(sinkOptions.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        }
        String sourcePassword = source.getPassword();
        String sinkPassword = sink.getPassword();
        // 密码脱敏
        if (StringUtils.isNotBlank(shadeIdentifier)) {
            sourcePassword = SeaTunnelConnectorFactory.desensitization(shadeIdentifier, sourcePassword);
            sinkPassword = SeaTunnelConnectorFactory.desensitization(shadeIdentifier, sinkPassword);
        }
        // source
        String sourceCatalogName = "iceberg";
        if (StringUtils.isNotEmpty(source.getCatalog())) {
            sourceCatalogName = source.getCatalog();
        }
        String sourceMetastoreUri = String.format("thrift://%s:%s", source.getMetastoreHost(), source.getMetastorePort());
        String sourceHadoopConfPath = "";
        // source kerberos 认证
        if (StringUtils.isNotEmpty(source.getHdfsSitePath())) {
            // 转换为Path对象
            Path path = Paths.get(source.getHdfsSitePath().split(",")[0]);
            // 获取父目录
            Path directory = path.getParent();
            // Hadoop conf 目录 需要包含 "core-site.xml", "hdfs-site.xml", "hive-site.xml"
            sourceHadoopConfPath = directory.toString();
        }
        String sourceFeNodes = String.format("%s:%s", source.getHost(), source.getBePort());
        // sink
        String sinkCatalogName = "iceberg";
        if (StringUtils.isNotEmpty(sink.getCatalog())) {
            sinkCatalogName = sink.getCatalog();
        }
        String sinkMetastoreUri = String.format("thrift://%s:%s", sink.getMetastoreHost(), sink.getMetastorePort());
        String sinkHadoopConfPath = "";
        // sink kerberos 认证
        if (StringUtils.isNotEmpty(sink.getHdfsSitePath())) {
            // 转换为Path对象
            Path path = Paths.get(sink.getHdfsSitePath().split(",")[0]);
            // 获取父目录
            Path directory = path.getParent();
            // Hadoop conf 目录 需要包含 "core-site.xml", "hdfs-site.xml", "hive-site.xml"
            sinkHadoopConfPath = directory.toString();
        }
        String sinkFeNodes = String.format("%s:%s", sink.getHost(), sink.getBePort());
        Map<String, String> placeholders = new HashMap<>();
        // 为 source 配置项添加占位符
        placeholders.put("${sourceJdbcUrl}", source.getJdbcUrl());
        placeholders.put("${sourceDriverClassName}", source.getDriverClassName());
        placeholders.put("${sourceUsername}", source.getUsername());
        placeholders.put("${sourcePassword}", sourcePassword); // 假设这是安全的
        placeholders.put("${sourceCatalogName}", sourceCatalogName);
        placeholders.put("${sourceMetastoreUri}", sourceMetastoreUri);
        placeholders.put("${sourceWarehouse}", source.getWarehouse());
        placeholders.put("${sourceHadoopConfPath}", sourceHadoopConfPath);
        placeholders.put("${sourceHdfsSitePath}", source.getHdfsSitePath());
        placeholders.put("${sourceHiveSitePath}", source.getHiveSitePath());
        placeholders.put("${sourceKerberosPrincipal}", source.getKerberosPrincipal());
        placeholders.put("${sourceKerberosKeytabPath}", source.getKerberosKeytabPath());
        placeholders.put("${sourceKerberosKrb5ConfPath}", source.getKerberosConfPath());
        placeholders.put("${sourceFeNodes}", sourceFeNodes);
        placeholders.put("${sourceQueryPort}", String.valueOf(source.getPort()));
        placeholders.put("${sourceFsS3aAwsCredentialsProvider}", String.valueOf(source.getFsS3aAwsCredentialsProvider()));
        placeholders.put("${sourceFsS3aEndpoint}", String.valueOf(source.getFsS3aEndpoint()));
        placeholders.put("${sourceFsS3aAccessKey}", String.valueOf(source.getFsS3aAccessKey()));
        placeholders.put("${sourceFsS3aSecretKey}", String.valueOf(source.getFsS3aSecretKey()));
        placeholders.put("${sourceFsDefaultFS}", String.valueOf(source.getFsDefaultFS()));
        // 文件类型
        placeholders.put("${sourceFileFormatType}", sourceOptions.getDatabaseName());
        // 文件地址
        placeholders.put("${sourceFilePath}", sourceOptions.getTableName());

        // 为 sink 配置项添加占位符
        placeholders.put("${sinkJdbcUrl}", sink.getJdbcUrl());
        placeholders.put("${sinkDriverClassName}", sink.getDriverClassName());
        placeholders.put("${sinkUsername}", sink.getUsername());
        placeholders.put("${sinkPassword}", sinkPassword); // 假设这是安全的
        placeholders.put("${sinkCatalogName}", sinkCatalogName);
        placeholders.put("${sinkMetastoreUri}", sinkMetastoreUri);
        placeholders.put("${sinkWarehouse}", sink.getWarehouse());
        placeholders.put("${sinkHadoopConfPath}", sinkHadoopConfPath);
        placeholders.put("${sinkHdfsSitePath}", sink.getHdfsSitePath());
        placeholders.put("${sinkHiveSitePath}", sink.getHiveSitePath());
        placeholders.put("${sinkKerberosPrincipal}", sink.getKerberosPrincipal());
        placeholders.put("${sinkKerberosKeytabPath}", sink.getKerberosKeytabPath());
        placeholders.put("${sinkKerberosKrb5ConfPath}", sink.getKerberosConfPath());
        placeholders.put("${sinkFeNodes}", sinkFeNodes);
        placeholders.put("${sinkQueryPort}", String.valueOf(sink.getPort()));
        placeholders.put("${sinkFsS3aAwsCredentialsProvider}", String.valueOf(sink.getFsS3aAwsCredentialsProvider()));
        placeholders.put("${sinkFsS3aEndpoint}", String.valueOf(sink.getFsS3aEndpoint()));
        placeholders.put("${sinkFsS3aAccessKey}", String.valueOf(sink.getFsS3aAccessKey()));
        placeholders.put("${sinkFsS3aSecretKey}", String.valueOf(sink.getFsS3aSecretKey()));
        placeholders.put("${sinkFsDefaultFS}", String.valueOf(sink.getFsDefaultFS()));
        return placeholders;
    }

    /**
     * 提取配置文件中的关键字值信息
     *
     * @param config 配置文件
     * @param key    提取关键字
     * @return 返回值
     */
    private static String extractConfigValue(String config, String key) {
        String patternString = String.format("%s\\s*=\\s*\"([^\"]*)\"", Pattern.quote(key));
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(config);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 生成ST运行环境
     *
     * @param env 环境对象信息
     * @return 环境配置
     */
    private String generateSeaTunnelRunEnvConfig(SeaTunnelEnv env) {
        StringBuilder envBuilder = new StringBuilder();
        envBuilder.append("env {\n");
        // 默认通配符替换
        String jobName = "${jobName}";
        if (StringUtils.isNotEmpty(env.getJobName())) {
            jobName = env.getJobName();
        }
        envBuilder.append("  job.name = ").append("\"").append(jobName).append("\"\n");
        envBuilder.append("  job.mode = ").append("\"").append(env.getJobMode().getCode()).append("\"\n");
        if (env.getCheckpointInterval() != null && env.getCheckpointInterval() != 0) {
            envBuilder.append("  checkpoint.interval = ").append(env.getCheckpointInterval()).append("\n");
        }
        if (env.getParallelism() != null && env.getParallelism() != 0) {
            envBuilder.append("  execution.parallelism = ").append(env.getParallelism()).append("\n");
        }
        if (StringUtils.isNotEmpty(env.getShadeIdentifier())) {
            envBuilder.append("  shade.identifier = ").append("\"").append(env.getShadeIdentifier()).append("\"\n");
        }
        envBuilder.append("}\n");
//        JSONObject envConfig = new JSONObject();
//        envConfig.put("job.name", env.getJobName());
//        envConfig.put("job.mode", env.getJobMode().getCode());
//        if (env.getCheckpointInterval() != null && env.getCheckpointInterval() != 0){
//            envConfig.put("checkpoint.interval", env.getCheckpointInterval());
//        }
//        if (env.getParallelism() != null && env.getParallelism() != 0){
//            envConfig.put("parallelism", env.getParallelism());
//        }
//        if (StringUtils.isNotEmpty(env.getShadeIdentifier())){
//            envConfig.put("shade.identifier", env.getShadeIdentifier());
//        }
        return envBuilder.toString();
    }

    /**
     * 获取数据库表列属性
     *
     * @param id           数据源ID
     * @param databaseName 数据库名
     * @param tableName    表名
     * @return 列属性集合
     */
    private List<Field> getRemoteDataBaseTableFields(Long id, String databaseName, String tableName) {
        R<List<Field>> r = remoteJdbcService.getFields(id, databaseName, tableName);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    /**
     * 获取实体中的列属性
     *
     * @param tenantId 租户ID
     * @param id       实体ID
     * @return 实体属性集合
     */
    private List<DataEntityStructureVo> getRemoteDataEntityFields(Long tenantId, Long id) {
        R<List<DataEntityStructureVo>> r = null;
        // 是否根据租户ID查询数据源信息(兼容之前版本)
        if (tenantId == null || tenantId == 0L) {
            r = remoteEntityStrureService.selectStructureById(id);
        } else {
            r = remoteEntityStrureService.selectStructureByIdAndTenantId(tenantId, id);
        }
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }


    /**
     * 获取数据源信息
     *
     * @param tenantId 租户ID
     * @param id       数据源ID
     * @return 数据源信息
     */
    private DataSource getRemoteDataSourceInfoById(Long tenantId, Long id) {
        R<DataSource> r = null;
        // 是否根据租户ID查询数据源信息(兼容之前版本)
        if (tenantId == null || tenantId == 0L) {
            r = remoteDriveService.queryDataSource(id);
        } else {
            r = remoteDriveService.queryDataSourceByTenantId(tenantId, id);
        }
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    /**
     * 获取实体底表映射信息
     *
     * @param tenantId 租户ID
     * @param id       实体ID
     * @return 实体底表映射信息
     */
    private DataEntityTable getRemoteDataEntityTable(Long tenantId, Long id) {
        // 是否根据租户ID查询数据源信息(兼容之前版本)
        R<DataEntityTable> r = null;
        if (tenantId == null || tenantId == 0L) {
            r = remoteEntityTableService.selectById(id);
        } else {
            r = remoteEntityTableService.selectByIdAndTenantId(id, tenantId);
        }
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    /**
     * 获取实体列映射信息
     *
     * @param id 实体ID
     * @return 实体列映射信息
     */
    private List<DataEntityTableMapping> getRemoteEntityTableMapping(Long tenantId, Long id) {
        // 是否根据租户ID查询数据源信息(兼容之前版本)
        R<List<DataEntityTableMapping>> r = null;
        if (tenantId == null || tenantId == 0L) {
            r = remoteEntityTableService.selectEntityTableMapping(id);
        } else {
            r = remoteEntityTableService.selectEntityTableMappingByTenantId(id, tenantId);
        }
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    /**
     * 是否需要 ST Transform（源表列中选则转译列则需要Transform）
     *
     * @param seaTunnelFields 源表列
     * @return true/false
     */
    private boolean isSeaTunnelTransform(List<SeaTunnelField> seaTunnelFields) {
        boolean isTransform = false;
        for (SeaTunnelField field : seaTunnelFields) {
            if (StringUtils.isNotEmpty(field.getTransformFieldType())) {
                isTransform = true;
                break;
            }
        }
        return isTransform;
    }


    /**
     * JSON 字符串格式化，自带换行效果
     *
     * @param obj JSON对象
     * @return 格式化后的JSON字符串
     */
    private String jsonFormat(JSONObject obj) {
        return JSON.toJSONString(obj, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat);
    }

    /**
     * 是否需要ST Transform 校验属性列中是否包含特殊字符，如果包含特殊字符并且未开启upsert的就不启用ST Transform
     *
     * @param fields 列信息
     * @return true/false
     */
    private boolean isEnableTransform(List<SeaTunnelField> fields, boolean upsert) {
        boolean isTransform = true;
        for (SeaTunnelField field : fields) {
            String fieldName = field.getFieldName();
            // 使用Matcher查找并替换特殊字符
            // 创建Pattern对象
            Pattern pattern = Pattern.compile(SeaTunnelConnectorFactory.SQL_REGEX);
            // 正则匹配
            Matcher matcher = pattern.matcher(fieldName);
            if (matcher.find() && !upsert) {
                isTransform = false;
                break;
            }
        }
        return isTransform;
    }

    /**
     * 获取 SeaTunnel 配置
     *
     * @return SeaTunnel 配置
     */
    private JSONObject getSeaTunnelConfig() {
        R<Object> r = remoteParamService.selectValueByCode("SEATUNNEL_CONFIG");
        if (r.getCode() == Status.SUCCESS.getCode()) {
            return JSON.parseObject((String) r.getData());
        }
        return null;
    }

    /**
     * 初始化运行环境、源、目标的配置信息
     *
     * @param env           运行环境
     * @param sourceOptions 源
     * @param sinkOptions   目标
     */
    private void initializeConfigurationInformation(SeaTunnelEnv env, DataModelOptions sourceOptions, DataModelOptions sinkOptions) {
        // 获取SeaTunnel系统参数配置
        JSONObject seaTunnelConfig = getSeaTunnelConfig();
        if (seaTunnelConfig != null) {
            // SeaTunnel 版本号定义到每个模块
            String seaTunnelVersion = seaTunnelConfig.getString("seaTunnelVersion");
            env.setVersion(seaTunnelVersion);
            sourceOptions.setVersion(seaTunnelVersion);
            sinkOptions.setVersion(seaTunnelVersion);
            // 脱敏规则定义
            JSONObject shadeIdentifierRule = seaTunnelConfig.getJSONObject("shadeIdentifierRule");
            Boolean enableEncryption = shadeIdentifierRule.getBoolean("enableEncryption");
            if (enableEncryption) {
                env.setShadeIdentifier(shadeIdentifierRule.getString("shadeIdentifier"));
            }
        }
        // 更新字段列映射
        List<SeaTunnelField> sourceFields = this.getEntityFieldsMappingFields(sourceOptions);
        sourceOptions.setSeaTunnelFields(sourceFields);
        List<SeaTunnelField> sinkFields = this.getEntityFieldsMappingFields(sinkOptions);
        sinkOptions.setSeaTunnelFields(sinkFields);
        // 绑定设置目标的内置列，在源中要默认拼接上去
        sourceOptions.setBuiltInFields(sinkOptions.getBuiltInFields());
        // 读取源数据库连接信息
        DataSource sourceDataSource = this.getDataSourceInfoById(sourceOptions);
        // 读取目标数据库连接信息
        DataSource sinkDataSource = this.getDataSourceInfoById(sinkOptions);
        if (sourceDataSource != null) {
            // 添加源数据模型数据源属性
            sourceOptions.setDataSourceBasicInfo(sourceDataSource.getDataSourceBasicInfo());
            sourceOptions.setSourceDbType(sourceDataSource.getType());
            sinkOptions.setSourceDbType(sourceDataSource.getType());
        }
        if (sinkDataSource != null) {
            // 添加目标数据模型数据源属性
            sourceOptions.setSinkDbType(sinkDataSource.getType());
            sinkOptions.setDataSourceBasicInfo(sinkDataSource.getDataSourceBasicInfo());
            sinkOptions.setSinkDbType(sinkDataSource.getType());
        }
        // 源或者目标数据源是否包含Doris 临时方案 等 Doris的connector修复好之后 可以删除
        boolean isContainsDoris = (sourceDataSource != null && "doris".equals(sourceDataSource.getType())) || (sinkDataSource != null && "doris".equals(sinkDataSource.getType()));
        if (isContainsDoris) {
            // 如果不包含Doris数据源 可以使用base64加密 tips 目前Doris的配置文件加密还存在问题
            env.setShadeIdentifier(null);
        }
    }

}
