package com.datalink.fdop.seatunnel.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdMenu;

import java.util.Date;


public class DomainAgeUtils {

    public static String getSeaTunnelCmdAgeStr(SeaTunnelCmd seaTunnelCmd) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (seaTunnelCmd.getId() != null ? seaTunnelCmd.getId() : IdWorker.getId()))
                .append(", pid: " + (seaTunnelCmd.getPid() != null ? seaTunnelCmd.getPid() : -1))
                .append((StringUtils.isNotEmpty(seaTunnelCmd.getCode()) ? ", code: '" + seaTunnelCmd.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(seaTunnelCmd.getName()) ? ", name: '" + seaTunnelCmd.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(seaTunnelCmd.getDescription()) ? ", description: '" + seaTunnelCmd.getDescription() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(seaTunnelCmd.getCreateBy()) ? seaTunnelCmd.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (seaTunnelCmd.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(seaTunnelCmd.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getSeaTunnelCmdMenuAgeStr(SeaTunnelCmdMenu seaTunnelCmdMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (seaTunnelCmdMenu.getId() != null ? seaTunnelCmdMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (seaTunnelCmdMenu.getPid() != null ? seaTunnelCmdMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(seaTunnelCmdMenu.getCode()) ? ", code: '" + seaTunnelCmdMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(seaTunnelCmdMenu.getName()) ? ", name: '" + seaTunnelCmdMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(seaTunnelCmdMenu.getDescription()) ? ", description: '" + seaTunnelCmdMenu.getDescription() + "'" : ""))
                .append(", serialNumber: " + (seaTunnelCmdMenu.getSerialNumber() != null ? seaTunnelCmdMenu.getSerialNumber() : 0))
                .append(", createBy: '" + (StringUtils.isNotEmpty(seaTunnelCmdMenu.getCreateBy()) ? seaTunnelCmdMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (seaTunnelCmdMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(seaTunnelCmdMenu.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

}
