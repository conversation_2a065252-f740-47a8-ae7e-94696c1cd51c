package com.datalink.fdop.seatunnel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdMenu;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface SeaTunnelCmdMenuMapper extends BaseMapper<SeaTunnelCmdMenu> {

    int createSeaTunnelCmdMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertSeaTunnelCmdMenu(@Param("seaTunnelCmdMenu") SeaTunnelCmdMenu seaTunnelCmdMenu);

    int updateById(SeaTunnelCmdMenu seaTunnelCmdMenu);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteSeaTunnelCmdMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<SeaTunnelCmdMenu> selectById(Long id);

    VlabelItem<SeaTunnelCmdMenu> selectByCode(String code);

    VlabelItem<SeaTunnelCmdMenu> selectByPid(Long pid);

    VlabelItem<SeaTunnelCmdMenu> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    IPage<SeaTunnelCmd> overview(@Param("page") Page<SeaTunnelCmd> page, @Param("pid") Long pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<SeaTunnelCmdTree> selectMenuTree(@Param("sort") String sort, @Param("code") String code);

    Integer querySerialNumber();

}
