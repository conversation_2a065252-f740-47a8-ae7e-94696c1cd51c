package com.datalink.fdop.seatunnel.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdMenu;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdTree;

import java.util.List;

/**
 * SeaTunnelCmdMenuService
 */
public interface ISeaTunnelCmdMenuService {

    int create(SeaTunnelCmdMenu seaTunnelCmdMenu);

    int update(SeaTunnelCmdMenu seaTunnelCmdMenu);

    int delete(List<Long> ids);

    List<SeaTunnelCmdTree> tree(String sort, String code, Boolean isQueryNode);

    PageDataInfo<SeaTunnelCmd> overview(Long pid, String sort, SearchVo searchVo);

    /**
     * 查询最大序号
     * @param menuFlag 是否为菜单
     * @return
     */
    Integer querySerialNumber(Boolean menuFlag);

}
