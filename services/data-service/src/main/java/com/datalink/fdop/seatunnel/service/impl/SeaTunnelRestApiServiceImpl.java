package com.datalink.fdop.seatunnel.service.impl;

import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.seatunnel.api.domain.DataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelSubmitConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.*;
import com.datalink.fdop.seatunnel.factory.SeaTunnelConnectorFactory;
import com.datalink.fdop.seatunnel.service.ISeaTunnelRestApiService;
import com.datalink.fdop.seatunnel.service.ISeaTunnelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * SeaTunnel RestApi 实现
 */
@Service
@Slf4j
public class SeaTunnelRestApiServiceImpl implements ISeaTunnelRestApiService {


    @Autowired
    private ISeaTunnelService seaTunnelService;

    /**
     * SeaTunnel 集群地址
     */
    @Value("${dwms.seatunnel.host}")
    private String seaTunnelHost;

    @Override
    public JobInfo submitJob(SeaTunnelSubmitConfig modelConfig) {
        // 获取数据模型
        DataModelOptions sourceOptions = modelConfig.getSourceOptions();
        DataModelOptions sinkOptions = modelConfig.getSinkOptions();
        // 更新字段列映射
        List<SeaTunnelField> sourceFields = seaTunnelService.getEntityFieldsMappingFields(sourceOptions);
        sourceOptions.setSeaTunnelFields(sourceFields);
        List<SeaTunnelField> sinkFields = seaTunnelService.getEntityFieldsMappingFields(sinkOptions);
        sinkOptions.setSeaTunnelFields(sinkFields);
        // 绑定设置目标的内置列，在源中要默认拼接上去
        sourceOptions.setBuiltInFields(sinkOptions.getBuiltInFields());
        // 初始化源集合
        List<SourceConfig> sourceList = new ArrayList<>();
        // 初始化目标集合
        List<SinkConfig> sinkList = new ArrayList<>();
        DataSource sourceDataSource = seaTunnelService.getDataSourceInfoById(sourceOptions);
        DataSource sinkDataSource = seaTunnelService.getDataSourceInfoById(sinkOptions);
        // 添加源数据模型数据源属性
        sourceOptions.setDataSourceBasicInfo(sourceDataSource.getDataSourceBasicInfo());
        sourceOptions.setSourceDbType(sourceDataSource.getType());
        sinkOptions.setSinkDbType(sinkDataSource.getType());
        // 添加目标数据模型数据源属性
        sinkOptions.setDataSourceBasicInfo(sinkDataSource.getDataSourceBasicInfo());
        sourceOptions.setSourceDbType(sourceDataSource.getType());
        sinkOptions.setSinkDbType(sinkDataSource.getType());
        SourceConfig sourceConfig = SeaTunnelConnectorFactory.getSourceDomainInstance(sourceDataSource.getType(), sourceOptions);
        sourceList.add(sourceConfig);
        SinkConfig sinkConfig = SeaTunnelConnectorFactory.getSinkDomainInstance(sinkDataSource.getType(), sinkOptions);
        sinkList.add(sinkConfig);
        // SeaTunnel job 提交 SeaTunnel 任务
        JobConfiguration job = new JobConfiguration(modelConfig.getEnv(), sourceList, null, sinkList);
        SeaTunnelAPI api = SeaTunnelAPI.build(seaTunnelHost);
        return api.submitJob(job);
    }

    @Override
    public Job getJob(String jobId) {
        if (StringUtils.isEmpty(jobId)){
            throw new ServiceException("JobId不能为空");
        }
        SeaTunnelAPI api = SeaTunnelAPI.build(seaTunnelHost);
        return api.getJob(jobId);
    }

    @Override
    public Job stopJob(String jobId) {
        return null;
    }

    @Override
    public List<Job> finishedJobs(String state) {
        if (StringUtils.isEmpty(state)){
            return new ArrayList<>();
        }
        SeaTunnelAPI api = SeaTunnelAPI.build(seaTunnelHost);
        return api.finishedJobs(state);
    }


}
