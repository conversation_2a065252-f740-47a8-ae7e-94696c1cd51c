package com.datalink.fdop.seatunnel.factory;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.DataModelType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.SpringUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.api.enums.OracleType;
import com.datalink.fdop.element.api.enums.BuiltinField;
import com.datalink.fdop.param.api.RemoteParamService;
import com.datalink.fdop.seatunnel.api.domain.CdcDataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.DataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.FileModelOptions;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;
import com.datalink.fdop.seatunnel.api.enums.SeaTunnelCastType;
import com.datalink.fdop.seatunnel.api.enums.SeaTunnelConnectorType;
import com.datalink.fdop.seatunnel.api.restapi.domain.SinkConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.SourceConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.doris.DorisSinkConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.doris.DorisSourceConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.iceberg.IcebergSinkConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.iceberg.IcebergSourceConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.jdbc.JdbcSinkConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.jdbc.JdbcSourceConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class SeaTunnelConnectorFactory {

    /**
     * JDBC 表名格式化 库名.表名
     */
    private static String JDBC_TABLE_FMT = "%s.%s";

    /**
     * 定义一个正则表达式，匹配任何非字母、非数字、非下划线或横杠的字符
     */
    public static String SQL_REGEX = "[^a-zA-Z0-9_-]+";

    /**
     * 定义SQL关键字词库，避免因为关键字导致语法查询错误
     */
    public static List<String> SQL_KEYWORDS = Arrays.asList("select", "from", "where", "group", "by", "order", "limit", "having", "join", "on", "and", "or", "not", "in", "is", "null", "between", "like",
            "exists", "union", "all", "distinct", "asc", "desc", "case", "when", "then", "else", "end", "into", "create", "table", "as", "insert", "into", "values", "update", "set", "delete", "from", "where",
            "having", "group", "by", "order", "limit", "union", "all", "select", "into", "create", "table", "as", "insert", "into", "values", "update", "set", "delete", "from", "where", "having", "group", "by",
            "order", "limit", "union", "all", "date", "false", "true", "number", "comment");

    /**
     * 参数Feign服务
     */
    private static RemoteParamService remoteParamService = SpringUtils.getBean(RemoteParamService.class);

    /**
     * SeaTunnel 串通source transform sink 的全局视图名称（这里需要统一用同一个）
     */
    private static String ST_GLOBAL_VIEW_NAME = "ST_GLOBAL_VIEW_NAME";

    /**
     * Base64 加密
     */
    private static final Base64.Encoder ENCODER = Base64.getEncoder();

    /**
     * 获取SeaTunnel Source 实例
     *
     * @param shadeIdentifier 配置文件敏感信息加密方式
     * @param dbType          数据类型
     * @param options         源数据实体
     * @return SeaTunnel Source 实例
     */
    public static String getSourceInstance(String shadeIdentifier, String dbType, DataModelOptions options, DataModelOptions sinkOptions, boolean enableTransform) {
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(options.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        // 正则匹配 转义表名
        String tableName = escapeSql(options.getTableName());
        // 正则匹配 转义schema 其实有的会是数据库，但在这里这里统称为schema
        String schemaName = escapeSql(options.getDatabaseName());
        // 正则匹配 转义库名
        String dataBaseName = escapeSql(options.getDatabaseName());
        switch (dbType) {
            case "hana":
            case "mysql":
            case "oracle":
            case "dm":
            case "postgresql":
            case "sqlserver":
            case "risingwave":
            case "trino":
                StringBuilder jdbcBuilder = new StringBuilder();
                jdbcBuilder.append("  ").append(SeaTunnelConnectorType.JDBC.getCode()).append(" {\n");
                jdbcBuilder.append("    result_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                jdbcBuilder.append("    url = ").append("\"").append("${sourceJdbcUrl}").append("\"\n");
                jdbcBuilder.append("    driver = ").append("\"").append("${sourceDriverClassName}").append("\"\n");
                jdbcBuilder.append("    user = ").append("\"").append("${sourceUsername}").append("\"\n");
                jdbcBuilder.append("    password = ").append("\"").append("${sourcePassword}").append("\"\n");
                if (options.getModelType() == DataModelType.SQL) {
                    String querySql = options.getQuerySql();
                    if (StringUtils.isNotEmpty(querySql)) {
                        // 去除换行符和制表符
                        querySql = querySql.replace("\r", " ").replace("\n", " ").replace("\t", " ");
                        // 去除多余的空格
                        querySql = querySql.trim().replaceAll(" +", " ");
                        // 转义双引号
                        querySql = querySql.replace("\"", "\\\"");
                    }
                    jdbcBuilder.append("    query = ").append("\"").append(querySql).append("\"\n");
                } else {
                    jdbcBuilder.append("    query = ").append("\"").append(SeaTunnelConnectorFactory.buildQuerySqlStr(options, sinkOptions, false, enableTransform)).append("\"\n");
                }
                jdbcBuilder.append("  }\n");
//                JSONObject jdbc = new JSONObject();
//                jdbc.put("url", dataSourceBasicInfo.getJdbcUrl());
//                jdbc.put("driver", dataSourceBasicInfo.getDriverClassName());
//                jdbc.put("user", dataSourceBasicInfo.getUsername());
//                jdbc.put("password", dataSourceBasicInfo.getPassword());
//                jdbc.put("query", selectBuilder.toString());
                return jdbcBuilder.toString();
            case "iceberg":
                StringBuilder icebergBuilder = new StringBuilder();
                icebergBuilder.append("  ").append(SeaTunnelConnectorType.ICEBERG.getCode()).append(" {\n");
                String catalogName = "${sourceCatalogName}";
                // 用老版本配置
                if (StringUtils.isEmpty(options.getVersion())) {
                    icebergBuilder.append("    result_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHdfsSitePath())) {
                        // hdfs sit xml path
                        icebergBuilder.append("    hdfs_site_path = ").append("\"").append("${sourceHdfsSitePath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHiveSitePath())) {
                        // hive site xml path
                        icebergBuilder.append("    hive_site_path = ").append("\"").append("${sourceHiveSitePath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosPrincipal())) {
                        icebergBuilder.append("    kerberos_principal = ").append("\"").append("${sourceKerberosPrincipal}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosKeytabPath())) {
                        icebergBuilder.append("    kerberos_keytab_path = ").append("\"").append("${sourceKerberosKeytabPath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosConfPath())) {
                        icebergBuilder.append("    kerberos_krb5_conf_path = ").append("\"").append("${sourceKerberosKrb5ConfPath}").append("\"\n");
                    }
                    icebergBuilder.append("    catalog_name = ").append("\"").append(catalogName).append("\"\n");
                    icebergBuilder.append("    catalog_type = ").append("\"").append("hive").append("\"\n");
                    icebergBuilder.append("    uri = ").append("\"").append("${sourceMetastoreUri}").append("\"\n");
                    icebergBuilder.append("    warehouse = ").append("\"").append("${sourceWarehouse}").append("\"\n");
                    icebergBuilder.append("    namespace = ").append("\"").append(dataBaseName).append("\"\n");
                    icebergBuilder.append("    table = ").append("\"").append(tableName).append("\"\n");
                    icebergBuilder.append("  }\n");
                } else {
                    String catalogType = dataSourceBasicInfo.getCatalogType();
                    if ("hive-minio".equalsIgnoreCase(dataSourceBasicInfo.getCatalogType())) {
                        catalogType = "hive";
                    }
                    icebergBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                    icebergBuilder.append("    catalog_name = ").append("\"").append(catalogName).append("\"\n");
                    icebergBuilder.append("    iceberg.catalog.config = ").append("{\n");
                    icebergBuilder.append("        type = ").append("\"").append(catalogType).append("\"\n");
                    if ("hive".equals(dataSourceBasicInfo.getCatalogType()) || "hive-minio".equals(dataSourceBasicInfo.getCatalogType())) {
                        icebergBuilder.append("        uri = ").append("\"").append("${sourceMetastoreUri}").append("\"\n");
                    }
                    icebergBuilder.append("        warehouse = ").append("\"").append("${sourceWarehouse}").append("\"\n");
                    icebergBuilder.append("    }\n");
                    if ("hadoop".equals(dataSourceBasicInfo.getCatalogType()) || "hive-minio".equals(dataSourceBasicInfo.getCatalogType())) {
                        icebergBuilder.append("    hadoop.config = ").append("{\n");
                        icebergBuilder.append("        fs.s3a.aws.credentials.provider = ").append("\"").append("${sourceFsS3aAwsCredentialsProvider}").append("\"\n");
                        icebergBuilder.append("        fs.s3a.endpoint = ").append("\"").append("${sourceFsS3aEndpoint}").append("\"\n");
                        icebergBuilder.append("        fs.s3a.access.key = ").append("\"").append("${sourceFsS3aAccessKey}").append("\"\n");
                        icebergBuilder.append("        fs.s3a.secret.key = ").append("\"").append("${sourceFsS3aSecretKey}").append("\"\n");
                        if (StringUtils.isNotEmpty(dataSourceBasicInfo.getFsDefaultFS())) {
                            icebergBuilder.append("        fs.defaultFS = ").append("\"").append("${sourceFsDefaultFS}").append("\"\n");
                        }
                        icebergBuilder.append("    }\n");
                    }
                    // kerberos 认证
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHdfsSitePath())) {
                        // 转换为Path对象
                        Path path = Paths.get(dataSourceBasicInfo.getHdfsSitePath().split(",")[0]);
                        // 获取父目录
                        Path directory = path.getParent();
                        // Hadoop conf 目录 需要包含 "core-site.xml", "hdfs-site.xml", "hive-site.xml"
                        icebergBuilder.append("    iceberg.hadoop-conf-path = ").append("\"").append("${sourceHadoopConfPath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosPrincipal())) {
                        icebergBuilder.append("    kerberos_principal = ").append("\"").append("${sourceKerberosPrincipal}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosKeytabPath())) {
                        icebergBuilder.append("    kerberos_keytab_path = ").append("\"").append("${sourceKerberosKeytabPath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosConfPath())) {
                        icebergBuilder.append("    kerberos_krb5_conf_path = ").append("\"").append("${sourceKerberosKrb5ConfPath}").append("\"\n");
                    }
                    icebergBuilder.append("    namespace = ").append("\"").append(dataBaseName).append("\"\n");
                    icebergBuilder.append("    table = ").append("\"").append(tableName).append("\"\n");
                    icebergBuilder.append("    table_data_filter = ").append("\"").append(options.getWhereSql()).append("\"\n");
                    icebergBuilder.append("  }\n");
                }
                return icebergBuilder.toString();
            case "doris":
                // doris sink details url https://seatunnel.apache.org/docs/connector-v2/sink/Doris
                StringBuilder dorisBuilder = new StringBuilder();
                dorisBuilder.append("  ").append(SeaTunnelConnectorType.DORIS.getCode()).append(" {\n");
                dorisBuilder.append("    result_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                dorisBuilder.append("    fenodes = ").append("\"").append("${sourceFeNodes}").append("\"\n");
                dorisBuilder.append("    query-port = ").append("\"").append("${sourceQueryPort}").append("\"\n");
                dorisBuilder.append("    username = ").append("\"").append("${sourceUsername}").append("\"\n");
                dorisBuilder.append("    password = ").append("\"").append("${sourcePassword}").append("\"\n");
                dorisBuilder.append("    database = ").append("\"").append(schemaName).append("\"\n");
                dorisBuilder.append("    table = ").append("\"").append(tableName).append("\"\n");
                dorisBuilder.append("  }\n");
                return dorisBuilder.toString();
            case "hive2":
            case "hive3":
                StringBuilder hiveBuilder = new StringBuilder();
                hiveBuilder.append("  ").append(SeaTunnelConnectorType.HIVE.getCode()).append(" {\n");
                hiveBuilder.append("    result_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                tableName = String.format("%s.%s", dataBaseName, tableName);
                hiveBuilder.append("    table_name = ").append("\"").append(tableName).append("\"\n");
                hiveBuilder.append("    metastore_uri = ").append("\"").append("${sourceMetastoreUri}").append("\"\n");
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHdfsSitePath())) {
                    // hdfs sit xml path
                    hiveBuilder.append("    hdfs_site_path = ").append("\"").append("${sourceHdfsSitePath}").append("\"\n");
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHiveSitePath())) {
                    // hive site xml path
                    hiveBuilder.append("    hive_site_path = ").append("\"").append("${sourceHiveSitePath}").append("\"\n");
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosPrincipal())) {
                    hiveBuilder.append("    kerberos_principal = ").append("\"").append("${sourceKerberosPrincipal}").append("\"\n");
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosKeytabPath())) {
                    hiveBuilder.append("    kerberos_keytab_path = ").append("\"").append("${sourceKerberosKeytabPath}").append("\"\n");
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosConfPath())) {
                    hiveBuilder.append("    kerberos_krb5_conf_path = ").append("\"").append("${sourceKerberosKrb5ConfPath}").append("\"\n");
                }
                hiveBuilder.append("  }\n");
                return hiveBuilder.toString();
            case "LocalFile":
                /**
                 * LocalFile {
                 *   schema {
                 *     fields {
                 *       name = string
                 *       age = int
                 *     }
                 *   }
                 *   path = "/apps/hive/demo/student"
                 *   file_format_type = "json"
                 * }
                 */
                StringBuilder localFileBuilder = new StringBuilder();
                localFileBuilder.append("  ").append(SeaTunnelConnectorType.LocalFile.getCode()).append(" {\n");
                localFileBuilder.append("    result_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                localFileBuilder.append("    schema {\n");
                localFileBuilder.append("      fields {\n");
                for (SeaTunnelField field : options.getSeaTunnelFields()) {
                    localFileBuilder.append("        ").append(field.getFieldName()).append(" = ").append(field.getFieldType()).append("\n");
                }
                localFileBuilder.append("      }\n");
                localFileBuilder.append("    }\n");
                localFileBuilder.append("    path = ").append("\"").append("${sourceFilePath}").append("\"\n");
                localFileBuilder.append("    file_format_type = ").append("\"").append("${sourceFileFormatType}").append("\"\n");
                localFileBuilder.append("  }\n");
                return localFileBuilder.toString();
            default:
                throw new RuntimeException(String.format("数据集成源数据暂不支[%s]类型", dbType));
        }
    }

    /**
     * 获取SeaTunnel Sink 实例
     *
     * @param shadeIdentifier 配置文件敏感信息加密方式
     * @param dbType          数据类型
     * @param options         源数据实体
     * @return SeaTunnel Sink 实例
     */
    public static String getSinkInstance(String shadeIdentifier, String dbType, DataModelOptions options) {
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(options.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        // 正则匹配 转义表名
        String tableName = escapeSql(options.getTableName());
        // 正则匹配 转义schema 其实有的会是数据库，但在这里这里统称为schema
        String schemaName = escapeSql(options.getDatabaseName());
        // 正则匹配 转义库名
        String dataBaseName = escapeSql(options.getDatabaseName());
        switch (dbType) {
            case "hana":
            case "mysql":
            case "oracle":
            case "dm":
            case "postgresql":
            case "sqlserver":
            case "risingwave":
            case "trino":
                // 密码脱敏
                String password = dataSourceBasicInfo.getPassword();
                if (StringUtils.isNotBlank(shadeIdentifier)) {
                    password = desensitization(shadeIdentifier, password);
                }
                StringBuilder jdbcBuilder = new StringBuilder();
                jdbcBuilder.append("  ").append(SeaTunnelConnectorType.JDBC.getCode()).append(" {\n");
                jdbcBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                jdbcBuilder.append("    url = ").append("\"").append(dataSourceBasicInfo.getJdbcUrl()).append("\"\n");
                jdbcBuilder.append("    driver = ").append("\"").append(dataSourceBasicInfo.getDriverClassName()).append("\"\n");
                jdbcBuilder.append("    user = ").append("\"").append(dataSourceBasicInfo.getUsername()).append("\"\n");
                jdbcBuilder.append("    password = ").append("\"").append(password).append("\"\n");
                if (options.getIsUpsert()) {
                    // 开启 upsert
                    jdbcBuilder.append("    support_upsert_by_query_primary_key_exist = true").append("\n");
                    // 自动生成 sink insert sql
                    jdbcBuilder.append("    generate_sink_sql = true").append("\n");
                    // 主键列集合
                    List<String> primaryKeyList = SeaTunnelConnectorFactory.getSinkPrimaryKeys(options);
                    if (!primaryKeyList.isEmpty()) {
                        // 主键列拼接
                        String primaryKeys = SeaTunnelConnectorFactory.getSinkPrimaryKeys(options).stream().collect(Collectors.joining("\",\"", "\"", "\""));
                        jdbcBuilder.append("    primary_keys = ").append("[").append(primaryKeys).append("]").append("\n");
                    }
                    // 数据库名称 这里这样做是因为 pg sqlserver这些数据库 是有schema的，所以数据库会必填，其他的jdbc类型的数据库数据库非必填，如果为空就取参数中传过来的数据库名称
                    String dbName = dataSourceBasicInfo.getDatabase();
                    if (StringUtils.isEmpty(dbName)) {
                        dbName = options.getDatabaseName();
                    }
                    // 正则匹配 转义库名
                    //dbName = escapeSql(dbName);
                    // 数据库
                    jdbcBuilder.append("    database = ").append("\"").append(dbName).append("\"\n");
                    // 表 库名.表名
                    jdbcBuilder.append("    table = ").append("\"").append(tableName).append("\"\n");
                } else {
                    jdbcBuilder.append("    query = ").append("\"").append(SeaTunnelConnectorFactory.buildInsertSqlStr(options)).append("\"\n");
                }
                jdbcBuilder.append("  }\n");
                return jdbcBuilder.toString();
            case "doris":
                // doris sink details url https://seatunnel.apache.org/docs/connector-v2/sink/Doris
                StringBuilder dorisBuilder = new StringBuilder();
                dorisBuilder.append("  ").append(SeaTunnelConnectorType.DORIS.getCode()).append(" {\n");
                dorisBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                dorisBuilder.append("    sink.enable-2pc = ").append("\"false\"\n");
                dorisBuilder.append("    fenodes = ").append("\"").append("${sinkFeNodes}").append("\"\n");
                dorisBuilder.append("    query-port = ").append("\"").append("${sinkQueryPort}").append("\"\n");
                dorisBuilder.append("    username = ").append("\"").append("${sinkUsername}").append("\"\n");
                dorisBuilder.append("    password = ").append("\"").append("${sinkPassword}").append("\"\n");
                dorisBuilder.append("    table.identifier = ").append("\"").append(String.format(JDBC_TABLE_FMT, schemaName, tableName)).append("\"\n");
                dorisBuilder.append("    sink.label-prefix = ").append("\"").append("sink").append("\"\n");
                dorisBuilder.append("    sink.buffer-size = 5242880 ").append("\n");
                dorisBuilder.append("    doris.batch.size = 10240 ").append("\n");
                dorisBuilder.append("    doris.config {\n");
                dorisBuilder.append("      format = \"json\"\n");
                dorisBuilder.append("      read_json_by_line = \"true\"\n");
                dorisBuilder.append("    }\n");
                dorisBuilder.append("  }\n");
                return dorisBuilder.toString();
            case "iceberg":
                StringBuilder icebergBuilder = new StringBuilder();
                icebergBuilder.append("  ").append(SeaTunnelConnectorType.ICEBERG.getCode()).append(" {\n");
                String catalogName = "${sinkCatalogName}";
                // 用老版本配置
                if (StringUtils.isEmpty(options.getVersion())) {
                    icebergBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHdfsSitePath())) {
                        // hdfs sit xml path
                        icebergBuilder.append("    hdfs_site_path = ").append("\"").append("${sinkHdfsSitePath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHiveSitePath())) {
                        // hive site xml path
                        icebergBuilder.append("    hive_site_path = ").append("\"").append("${sinkHiveSitePath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosPrincipal())) {
                        icebergBuilder.append("    kerberos_principal = ").append("\"").append("${sinkKerberosPrincipal}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosKeytabPath())) {
                        icebergBuilder.append("    kerberos_keytab_path = ").append("\"").append("${sinkKerberosKeytabPath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosConfPath())) {
                        icebergBuilder.append("    kerberos_krb5_conf_path = ").append("\"").append("${sinkKerberosKrb5ConfPath}").append("\"\n");
                    }
                    icebergBuilder.append("    catalog_name = ").append("\"").append(catalogName).append("\"\n");
                    icebergBuilder.append("    catalog_type = ").append("\"").append("hive").append("\"\n");
                    icebergBuilder.append("    uri = ").append("\"").append("${sinkMetastoreUri}").append("\"\n");
                    icebergBuilder.append("    warehouse = ").append("\"").append("${sinkWarehouse}").append("\"\n");
                    icebergBuilder.append("    namespace = ").append("\"").append(dataBaseName).append("\"\n");
                    icebergBuilder.append("    table = ").append("\"").append(tableName).append("\"\n");
                    icebergBuilder.append("  }\n");
                } else {
                    String catalogType = dataSourceBasicInfo.getCatalogType();
                    if ("hive-minio".equalsIgnoreCase(dataSourceBasicInfo.getCatalogType())) {
                        catalogType = "hive";
                    }
                    icebergBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                    icebergBuilder.append("    catalog_name = ").append("\"").append(catalogName).append("\"\n");
                    icebergBuilder.append("    iceberg.catalog.config = ").append("{\n");
                    icebergBuilder.append("        type = ").append("\"").append(catalogType).append("\"\n");
                    if ("hive".equals(dataSourceBasicInfo.getCatalogType()) || "hive-minio".equals(dataSourceBasicInfo.getCatalogType())) {
                        icebergBuilder.append("        uri = ").append("\"").append("${sinkMetastoreUri}").append("\"\n");
                    }
                    icebergBuilder.append("        warehouse = ").append("\"").append("${sinkWarehouse}").append("\"\n");
                    icebergBuilder.append("    }\n");
                    if ("hadoop".equals(dataSourceBasicInfo.getCatalogType())) {
                        icebergBuilder.append("    hadoop.config = ").append("{\n");
                        icebergBuilder.append("        fs.s3a.aws.credentials.provider = ").append("\"").append("${sinkFsS3aAwsCredentialsProvider}").append("\"\n");
                        icebergBuilder.append("        fs.s3a.endpoint = ").append("\"").append("${sinkFsS3aEndpoint}").append("\"\n");
                        icebergBuilder.append("        fs.s3a.access.key = ").append("\"").append("${sinkFsS3aAccessKey}").append("\"\n");
                        icebergBuilder.append("        fs.s3a.secret.key = ").append("\"").append("${sinkFsS3aSecretKey}").append("\"\n");
                        if (StringUtils.isNotEmpty(dataSourceBasicInfo.getFsDefaultFS())) {
                            icebergBuilder.append("        fs.defaultFS = ").append("\"").append("${sinkFsDefaultFS}").append("\"\n");
                        }
                        icebergBuilder.append("    }\n");
                    }
                    // kerberos 认证
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHdfsSitePath())) {
                        // Hadoop conf 目录 需要包含 "core-site.xml", "hdfs-site.xml", "hive-site.xml"
                        icebergBuilder.append("    iceberg.hadoop-conf-path = ").append("\"").append("${sinkHadoopConfPath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosPrincipal())) {
                        icebergBuilder.append("    kerberos_principal = ").append("\"").append("${sinkKerberosPrincipal}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosKeytabPath())) {
                        icebergBuilder.append("    kerberos_keytab_path = ").append("\"").append("${sinkKerberosKeytabPath}").append("\"\n");
                    }
                    if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosConfPath())) {
                        icebergBuilder.append("    kerberos_krb5_conf_path = ").append("\"").append("${sinkKerberosKrb5ConfPath}").append("\"\n");
                    }
                    icebergBuilder.append("    namespace = ").append("\"").append(dataBaseName).append("\"\n");
                    icebergBuilder.append("    table = ").append("\"").append(tableName).append("\"\n");
                    if (options.getIcebergTableWriteProps() != null && !options.getIcebergTableWriteProps().isEmpty()) {
                        icebergBuilder.append("    iceberg.table.write-props = ").append("{\n");
                        options.getIcebergTableWriteProps().forEach((key, val) -> {
                            if (StringUtils.isNotEmpty(key) && val != null) {
                                icebergBuilder.append("        ").append(key).append(" = ");
                                if (val instanceof String) {
                                    icebergBuilder.append("\"").append(val).append("\"\n");
                                } else {
                                    icebergBuilder.append(val).append("\n");
                                }
                            }

                        });
                        icebergBuilder.append("    }\n");
                    }
                    // 是否开启 upsert模式，数据覆盖模式下开启
                    String primaryKey = getPrimaryKeyStr(options);
                    if (StringUtils.isNotEmpty(primaryKey) && options.getIsUpsert()) {
                        icebergBuilder.append("    iceberg.table.upsert-mode-enabled = ").append("true").append("\n");
                        icebergBuilder.append("    iceberg.table.primary-keys = ").append("\"").append(primaryKey).append("\"\n");
                    }
                    // 大小写敏感
                    icebergBuilder.append("    case_sensitive = ").append("true").append("\n");
                    icebergBuilder.append("  }\n");
                }
                return icebergBuilder.toString();
            case "hive2":
            case "hive3":
                StringBuilder hiveBuilder = new StringBuilder();
                hiveBuilder.append("  ").append(SeaTunnelConnectorType.HIVE.getCode()).append(" {\n");
                hiveBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                // 正则匹配 转义表名
                tableName = escapeSql(options.getTableName());
                tableName = String.format("%s.%s", dataBaseName, tableName);
                hiveBuilder.append("    table_name = ").append("\"").append(tableName).append("\"\n");
                hiveBuilder.append("    metastore_uri = ").append("\"").append("${sinkMetastoreUri}").append("\"\n");
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHdfsSitePath())) {
                    // hdfs sit xml path
                    hiveBuilder.append("    hdfs_site_path = ").append("\"").append("${sinkHdfsSitePath}").append("\"\n");
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHiveSitePath())) {
                    // hive site xml path
                    hiveBuilder.append("    hive_site_path = ").append("\"").append("${sinkHiveSitePath}").append("\"\n");
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosPrincipal())) {
                    hiveBuilder.append("    kerberos_principal = ").append("\"").append("${sinkKerberosPrincipal}").append("\"\n");
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosKeytabPath())) {
                    hiveBuilder.append("    kerberos_keytab_path = ").append("\"").append("${sinkKerberosKeytabPath}").append("\"\n");
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosConfPath())) {
                    hiveBuilder.append("    kerberos_krb5_conf_path = ").append("\"").append("${sinkKerberosKrb5ConfPath}").append("\"\n");
                }
                hiveBuilder.append("  }\n");
                return hiveBuilder.toString();
            default:
                throw new RuntimeException(String.format("数据集成目标数据源暂不支[%s]类型", dbType));
        }
    }

    /**
     * 获取SeaTunnel目标端文件运行配置
     *
     * @param shadeIdentifier 配置文件敏感信息加密方式
     * @param options         文件数据模型
     * @return
     */
    public static String getFileSinkInstance(String shadeIdentifier, FileModelOptions options) {
        switch (options.getModelType().getCode()) {
            case "SFTP":
                // 密码脱敏
                String password = options.getServerPassword();
                if (StringUtils.isNotBlank(shadeIdentifier)) {
                    password = desensitization(shadeIdentifier, password);
                }
                StringBuilder sftpBuilder = new StringBuilder();
                sftpBuilder.append("  ").append(SeaTunnelConnectorType.SFTP.getCode()).append(" {\n");
                sftpBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                sftpBuilder.append("    host = ").append("\"").append(options.getServerHost()).append("\"\n");
                sftpBuilder.append("    port = ").append("\"").append(options.getServerPort()).append("\"\n");
                sftpBuilder.append("    user = ").append("\"").append(options.getServerUser()).append("\"\n");
                sftpBuilder.append("    password = ").append("\"").append(password).append("\"\n");
                sftpBuilder.append("    path = ").append("\"").append(options.getFilePath()).append("\"\n");
                sftpBuilder.append("    file_name_expression = ").append("\"").append(options.getFileName()).append("\"\n");
                sftpBuilder.append("    file_format_type = ").append("\"").append(options.getFileType()).append("\"\n");
                // 数据写入的临时目录，seatuunel底层默认是在ftp/sftp的 /tmp/seatuunel目录下，如果账户没有/tmp目录权限则默认拼接在当前path下面去新建/tmp目录
                String fileTmpPath = options.getFilePath() + "/tmp";
                if (StringUtils.isNotEmpty(options.getTmpFilePath())) {
                    fileTmpPath = options.getTmpFilePath();
                }
                sftpBuilder.append("    tmp_path = ").append("\"").append(fileTmpPath).append("\"\n");
                if (StringUtils.isNotEmpty(options.getFileNameTimeFormat())) {
                    sftpBuilder.append("    filename_time_format = ").append("\"").append(options.getFileNameTimeFormat()).append("\"\n");
                }
                // 是否写入表头 SeaTunnel 2.3.4 支持的新特性
                if ("csv".equals(options.getFileType()) || "text".equals(options.getFileType())) {
                    sftpBuilder.append("    enable_header_write = \"true\" \n");
                    sftpBuilder.append("    field_delimiter = ").append("\"").append(options.getFieldDelimiter()).append("\"\n");
                    sftpBuilder.append("    row_delimiter = ").append("\"").append(options.getRowDelimiter()).append("\"\n");
                }
                // 分区格式
                sftpBuilder.append("    partition_dir_expression = \"${k0}=${v0}\" \n");
                // 开启分区
                sftpBuilder.append("    is_partition_field_write_in_file = true \n");
                // 是否开启事务
                sftpBuilder.append("    is_enable_transaction = false \n");
                // 自定义文件名称
                sftpBuilder.append("    custom_filename = true \n");
                sftpBuilder.append("  }\n");
                return sftpBuilder.toString();
            case "FTP":
                // 密码脱敏
                password = options.getServerPassword();
                if (StringUtils.isNotBlank(shadeIdentifier)) {
                    password = desensitization(shadeIdentifier, password);
                }
                StringBuilder ftpBuilder = new StringBuilder();
                ftpBuilder.append("  ").append(SeaTunnelConnectorType.FTP.getCode()).append(" {\n");
                ftpBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
                ftpBuilder.append("    host = ").append("\"").append(options.getServerHost()).append("\"\n");
                ftpBuilder.append("    port = ").append("\"").append(options.getServerPort()).append("\"\n");
                ftpBuilder.append("    user = ").append("\"").append(options.getServerUser()).append("\"\n");
                ftpBuilder.append("    password = ").append("\"").append(password).append("\"\n");
                ftpBuilder.append("    path = ").append("\"").append(options.getFilePath()).append("\"\n");
                ftpBuilder.append("    file_name_expression = ").append("\"").append(options.getFileName()).append("\"\n");
                ftpBuilder.append("    file_format_type = ").append("\"").append(options.getFileType()).append("\"\n");
                // 数据写入的临时目录，seatuunel底层默认是在ftp/sftp的 /tmp/seatuunel目录下，如果账户没有/tmp目录权限则默认拼接在当前path下面去新建/tmp目录
                ftpBuilder.append("    tmp_path = ").append("\"").append(options.getFilePath() + "/tmp").append("\"\n");
                if (StringUtils.isNotEmpty(options.getFileNameTimeFormat())) {
                    ftpBuilder.append("    filename_time_format = ").append("\"").append(options.getFileNameTimeFormat()).append("\"\n");
                }
                // 是否写入表头 SeaTunnel 2.3.4 支持的新特性
                if ("csv".equals(options.getFileType()) || "text".equals(options.getFileType())) {
                    ftpBuilder.append("    enable_header_write = \"true\" \n");
                    ftpBuilder.append("    field_delimiter = ").append("\"").append(options.getFieldDelimiter()).append("\"\n");
                    ftpBuilder.append("    row_delimiter = ").append("\"").append(options.getRowDelimiter()).append("\"\n");
                }
                // 分区格式
                ftpBuilder.append("    partition_dir_expression = \"${k0}=${v0}\" \n");
                // 开启分区
                ftpBuilder.append("    is_partition_field_write_in_file = true \n");
                // 是否开启事务
                ftpBuilder.append("    is_enable_transaction = false \n");
                // 自定义文件名称
                ftpBuilder.append("    custom_filename = true \n");
                ftpBuilder.append("  }\n");
                return ftpBuilder.toString();
            default:
                throw new RuntimeException(String.format("数据集成目标文件服务器暂不支[%s]类型", options.getModelType()));
        }
    }


//    private static String getFieldsStr(DataModelOptions options, boolean useJsonFormat) {
//        // SeaTunnel 属性列
//        List<SeaTunnelField> seaTunnelFields = options.getSeaTunnelFields();
//        List<String> fields = new ArrayList<>();
//        List<String> sqlKeywordsList = getSqlKeywordsConfig();
//        for (SeaTunnelField field : seaTunnelFields) {
//            String fieldName = field.getFieldName();
//            if (StringUtils.isEmpty(fieldName)) {
//                continue;
//            }
//            // 处理fieldName如果等于SQL关键字查询SQL查询会语法错误的问题
//            fieldName = fieldName.replaceAll(SQL_REGEX, "_");
//            // 检查字段是否为关键字
//            boolean isKeyword = false;
//            if (CollectionUtils.isNotEmpty(sqlKeywordsList)) {
//                if (sqlKeywordsList.contains(fieldName)) {
//                    isKeyword = true;
//                }
//            } else {
//                if (SQL_KEYWORDS.contains(fieldName)){
//                    isKeyword = true;
//                }
//            }
//            // 正则匹配 转义列名
//            fieldName = escapeSql(fieldName);
//            if ("risingwave".equalsIgnoreCase(options.getSourceDbType())) {
//                // 先写死，没办法！妥协一下 目标是 rw的才需要转换一下
//                if ("DATE".equals(field.getFieldType())) {
//                    fieldName = "TO_CHAR(" + field.getFieldName() + ",'YYYY-MM-DD HH24:mi:SS') AS " + fieldName;
//                }
//            }
//            // 非json格式下拼接
//            if (!useJsonFormat) {
//                // 兼容postgresql 判断如果包含转义符则不需要拼接
//                if ("postgresql".equalsIgnoreCase(options.getSourceDbType()) && !fieldName.contains("\\\"")) {
//                    fields.add("\\\"" + fieldName + "\\\"");
//                } else {
//                    if (isKeyword) {
//                        fields.add("\\\"" + fieldName + "\\\"");
//                    } else {
//                        fields.add(fieldName);
//                    }
//                }
//            } else {
//                if (isKeyword) {
//                    fields.add("\\\"" + fieldName + "\\\"");
//                } else {
//                    fields.add(fieldName);
//                }
//            }
//        }
//        return StringUtils.join(fields, ",");
//    }

    /**
     * 列集合转译成字符串 col1,col2,...
     *
     * @param options 数据模型
     * @return 字符串 col1,col2,...
     */
    private static String getFieldsStr(DataModelOptions options, boolean useJsonFormat) {
        List<SeaTunnelField> seaTunnelFields = options.getSeaTunnelFields();
        List<String> fields = new ArrayList<>();
        // 获取SQL关键字配置
        List<String> sqlKeywordsList = getSqlKeywordsConfig();
        boolean isRisingwave = "risingwave".equalsIgnoreCase(options.getSourceDbType());
        boolean isPostgresql = "postgresql".equalsIgnoreCase(options.getSourceDbType());
        for (SeaTunnelField field : seaTunnelFields) {
            String fieldName = field.getFieldName();
            if (StringUtils.isEmpty(fieldName)) {
                continue;
            }
            boolean isKeyword = isSqlKeyword(fieldName, sqlKeywordsList);
            fieldName = escapeSql(fieldName);
            if (isRisingwave && "DATE".equals(field.getFieldType())) {
                fieldName = "TO_CHAR(" + field.getFieldName() + ",'YYYY-MM-DD HH24:mi:SS') AS " + fieldName;
            }
            fields.add(formatFieldName(fieldName, isKeyword, isPostgresql, useJsonFormat));
        }
        return StringUtils.join(fields, ",");
    }

    /**
     * 列集合转译成字符串 col1,col2,...
     *
     * @param sourceOptions 数据模型
     * @return 字符串 col1,col2,...
     */
    private static String getFieldsStrDisabledTransform(DataModelOptions sourceOptions, DataModelOptions sinkOptions, boolean useJsonFormat) {
        List<SeaTunnelField> sourceSeaTunnelFields = sourceOptions.getSeaTunnelFields();
        List<SeaTunnelField> sinkSeaTunnelFields = sinkOptions.getSeaTunnelFields();
        List<String> fields = new ArrayList<>();
        // 获取SQL关键字配置
        List<String> sqlKeywordsList = getSqlKeywordsConfig();
        boolean isRisingwave = "risingwave".equalsIgnoreCase(sourceOptions.getSourceDbType());
        boolean isPostgresql = "postgresql".equalsIgnoreCase(sourceOptions.getSourceDbType());
        for (int i = 0; i < sourceSeaTunnelFields.size(); i++) {
            SeaTunnelField field = sourceSeaTunnelFields.get(i);
            String fieldName = field.getFieldName();
            if (StringUtils.isEmpty(fieldName)) {
                continue;
            }
            boolean isKeyword = isSqlKeyword(fieldName, sqlKeywordsList);
            fieldName = escapeSql(fieldName);
            if (isRisingwave && "DATE".equals(field.getFieldType())) {
                fieldName = "TO_CHAR(" + field.getFieldName() + ",'YYYY-MM-DD HH24:mi:SS') AS " + fieldName;
            }
            fields.add(formatFieldNameDisabledTransform(fieldName, sinkSeaTunnelFields.get(i).getFieldName(), isKeyword, isPostgresql, useJsonFormat));
        }
        // 拼接实体内置列
        List<SeaTunnelField> builtInFields = sourceOptions.getBuiltInFields();
        if (builtInFields != null && !builtInFields.isEmpty()) {
            for (SeaTunnelField field : builtInFields) {
                String fieldName = field.getFieldName();
                if (StringUtils.isEmpty(fieldName)) {
                    continue;
                }
                // 字符串格式化
                String builtInFmt = "'${%s}' as \\\"%s\\\"";
                // 实体内置列需要单独插入数据
                if (BuiltinField._BATCH_ID.getDesc().equalsIgnoreCase(fieldName)) {
                    fields.add(String.format(builtInFmt, BuiltinField._BATCH_ID.getDesc(), fieldName));
                } else if (BuiltinField._IMPORT_TIME.getDesc().equalsIgnoreCase(fieldName)) {
                    fields.add(String.format(builtInFmt, BuiltinField._IMPORT_TIME.getDesc(), fieldName));
                }
            }
        }
        return StringUtils.join(fields, ",");
    }

    private static boolean isSqlKeyword(String fieldName, List<String> sqlKeywordsList) {
        if (CollectionUtils.isNotEmpty(sqlKeywordsList)) {
            return sqlKeywordsList.stream()
                    .anyMatch(keyword -> keyword.equalsIgnoreCase(fieldName));
        }
        return SQL_KEYWORDS.stream()
                .anyMatch(keyword -> keyword.equalsIgnoreCase(fieldName));
    }

    private static String formatFieldName(String fieldName, boolean isKeyword, boolean isPostgresql, boolean useJsonFormat) {
        if (!useJsonFormat && isPostgresql && !fieldName.contains("\\\"")) {
            return "\\\"" + fieldName + "\\\"";
        }
        if (isKeyword) {
            return "\\\"" + fieldName + "\\\"";
        }
        return fieldName;
    }

    private static String formatFieldNameDisabledTransform(String sourceFieldName, String sinkFileName, boolean isKeyword, boolean isPostgresql, boolean useJsonFormat) {
        String builtInFmt = "%s as %s";
        sourceFieldName = formatFieldName(sourceFieldName, isKeyword, isPostgresql, useJsonFormat);
        // AS sink 需要确保大小写一致，否则sink端取不到值
        sinkFileName = "\\\"" + sinkFileName + "\\\"";
        return String.format(builtInFmt, sourceFieldName, sinkFileName);
    }

    /**
     * 获取主键列
     *
     * @param options 数据模型
     * @return 字符串 col1,col2,...
     */
    private static String getPrimaryKeyStr(DataModelOptions options) {
        // seaTunnel 属性列
        List<SeaTunnelField> seaTunnelFields = options.getSeaTunnelFields();
        List<String> fields = new ArrayList<>();
        for (SeaTunnelField field : seaTunnelFields) {
            if (field.getIsPk()) {
                String fieldName = field.getFieldName();
                if (StringUtils.isEmpty(fieldName)) {
                    continue;
                }
                // 正则匹配 转义列名
                fields.add(escapeSql(fieldName));
            }
        }
        return StringUtils.join(fields, ",");
    }


    /**
     * 列集合转译成字符串 col1,col2,...
     *
     * @param seaTunnelFields ST列集合对象
     * @return 字符串 col1,col2,...
     */
    private static String getInsertFieldsStr(String dbType, List<SeaTunnelField> seaTunnelFields, List<SeaTunnelField> builtInFields) {
        //log.info("seaTunnelFields {}, builtInFields {}", JSON.toJSONString(seaTunnelFields), JSON.toJSONString(builtInFields));
        List<String> fields = new ArrayList<>();
        // 拼接列名
        for (SeaTunnelField field : seaTunnelFields) {
            String fieldName = field.getFieldName();
            if (StringUtils.isEmpty(fieldName)) {
                continue;
            }
            // 正则匹配 转义列名
            fieldName = escapeSql(fieldName);
            if ("postgresql".equalsIgnoreCase(dbType)) {
                fields.add("\\\"" + fieldName + "\\\"");
            } else {
                fields.add(fieldName);
            }
        }
        // 拼接实体内置列
        if (builtInFields != null && !builtInFields.isEmpty()) {
            for (SeaTunnelField field : builtInFields) {
                String fieldName = field.getFieldName();
                if (StringUtils.isEmpty(fieldName)) {
                    continue;
                }
                if ("postgresql".equalsIgnoreCase(dbType)) {
                    fields.add("\\\"" + fieldName + "\\\"");
                } else {
                    fields.add(fieldName);
                }
            }
        }
        return StringUtils.join(fields, ",");
    }

    /**
     * Insert 属性列 占位符拼接 ?,?,...
     *
     * @param size 属性列集合大小
     * @return 占位符拼接 ?,?,...
     */
    private static String getInsertFields(int size) {
        List<String> fields = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            fields.add("?");
        }
        return StringUtils.join(fields, ",");
    }

    /**
     * 获取源SeaTunnel Json 配置
     *
     * @param dbType  数据源类型
     * @param options 数据源/实体
     * @return SeaTunnelSourceConfig
     */
    public static SourceConfig getSourceDomainInstance(String dbType, DataModelOptions options) {
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(options.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        switch (dbType) {
            case "hana":
            case "mysql":
            case "oracle":
            case "dm":
            case "postgresql":
            case "sqlserver":
            case "risingwave":
            case "trino":
                JdbcSourceConfig jdbcSourceConfig = new JdbcSourceConfig();
                jdbcSourceConfig.setQuery(SeaTunnelConnectorFactory.buildQuerySqlStr(options, null, true, true));
                jdbcSourceConfig.setDriver(dataSourceBasicInfo.getDriverClassName());
                jdbcSourceConfig.setUrl(dataSourceBasicInfo.getJdbcUrl());
                jdbcSourceConfig.setUser(dataSourceBasicInfo.getUsername());
                jdbcSourceConfig.setPassword(dataSourceBasicInfo.getPassword());
                return jdbcSourceConfig;
            case "doris":
                // 正则匹配 转义schema 其实有的会是数据库，但在这里这里统称为schema
                String schemaName = escapeSql(options.getDatabaseName());
                // 正则匹配 转义表名
                String tableName = escapeSql(options.getTableName());
                DorisSourceConfig dorisSourceConfig = new DorisSourceConfig();
                dorisSourceConfig.setFenodes(String.format("%s:%s", dataSourceBasicInfo.getHost(), dataSourceBasicInfo.getBePort()));
                dorisSourceConfig.setQueryPort(String.valueOf(dataSourceBasicInfo.getPort()));
                dorisSourceConfig.setUsername(dataSourceBasicInfo.getUsername());
                dorisSourceConfig.setPassword(dataSourceBasicInfo.getPassword());
                dorisSourceConfig.setDatabase(schemaName);
                dorisSourceConfig.setTable(tableName);
                return dorisSourceConfig;
            case "iceberg":
                String catalogName = "iceberg";
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getCatalog())) {
                    catalogName = dataSourceBasicInfo.getCatalog();
                }
                IcebergSourceConfig icebergSourceConfig = new IcebergSourceConfig();
                icebergSourceConfig.setCatalogName(catalogName);
                Map<String, String> catalogConfig = new HashMap<>();
                String catalogType = "hive";
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getCatalogType())) {
                    catalogType = dataSourceBasicInfo.getCatalogType();
                }
                catalogConfig.put("type", catalogType);
                catalogConfig.put("uri", String.format("thrift://%s:%s", dataSourceBasicInfo.getMetastoreHost(), dataSourceBasicInfo.getMetastorePort()));
                catalogConfig.put("warehouse", dataSourceBasicInfo.getWarehouse());
                icebergSourceConfig.setCatalogConfig(catalogConfig);
                // hadoop 配置文件路径
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHdfsSitePath())) {
                    // 转换为Path对象
                    Path path = Paths.get(dataSourceBasicInfo.getHdfsSitePath().split(",")[0]);
                    // 获取父目录
                    Path directory = path.getParent();
                    // Hadoop conf 目录 需要包含 "core-site.xml", "hdfs-site.xml", "hive-site.xml"
                    String hadoopConfPath = directory.toString();
                    icebergSourceConfig.setHadoopConfPath(hadoopConfPath);
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosPrincipal())) {
                    icebergSourceConfig.setKerberosPrincipal(dataSourceBasicInfo.getKerberosPrincipal());
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosKeytabPath())) {
                    icebergSourceConfig.setKerberosKeytabPath(dataSourceBasicInfo.getKerberosKeytabPath());
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosConfPath())) {
                    icebergSourceConfig.setKerberosKrb5ConfPath(dataSourceBasicInfo.getKerberosConfPath());
                }
                icebergSourceConfig.setNamespace(escapeSql(options.getDatabaseName()));
                icebergSourceConfig.setTable(escapeSql(options.getTableName()));
                return icebergSourceConfig;
            default:
                throw new RuntimeException(String.format("数据集成源数据暂不支[%s]类型", dbType));
        }
    }

    /**
     * 获取目标SeaTunnel Json 配置
     *
     * @param dbType  源表数据源类型
     * @param options 数据源/实体
     * @return SeaTunnelSinkConfig
     */
    public static SinkConfig getSinkDomainInstance(String dbType, DataModelOptions options) {
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(options.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        switch (dbType) {
            case "hana":
            case "mysql":
            case "oracle":
            case "dm":
            case "postgresql":
            case "sqlserver":
            case "risingwave":
            case "trino":
                JdbcSinkConfig jdbcSinkConfig = new JdbcSinkConfig();
                jdbcSinkConfig.setDriver(dataSourceBasicInfo.getDriverClassName());
                jdbcSinkConfig.setUrl(dataSourceBasicInfo.getJdbcUrl());
                jdbcSinkConfig.setUser(dataSourceBasicInfo.getUsername());
                jdbcSinkConfig.setPassword(dataSourceBasicInfo.getPassword());
                if (options.getIsUpsert()) {
                    // 开启 upsert
                    jdbcSinkConfig.setIsUpsert(true);
                }
                List<String> primaryKeys = SeaTunnelConnectorFactory.getSinkPrimaryKeys(options);
                if (!primaryKeys.isEmpty()) {
                    // 主键列
                    jdbcSinkConfig.setPrimaryKeys(primaryKeys);
                }
                // 数据库名称 这里这样做是因为 pg sqlserver这些数据库 是有schema的，所以数据库会必填，其他的jdbc类型的数据库数据库非必填，如果为空就取参数中传过来的数据库名称
                String dbName = dataSourceBasicInfo.getDatabase();
                if (StringUtils.isEmpty(dbName)) {
                    dbName = options.getDatabaseName();
                }
                // 正则匹配 转义库名
                dbName = escapeSql(dbName);
                // 正则匹配 转义表名
                String tableName = escapeSql(options.getTableName());
                // 正则匹配 转义schema 其实有的会是数据库，但在这里这里统称为schema
                String schemaName = escapeSql(options.getDatabaseName());
                // 数据库
                jdbcSinkConfig.setDatabase(dbName);
                // 表 库名.表名
                jdbcSinkConfig.setTable(String.format(JDBC_TABLE_FMT, schemaName, tableName));
                return jdbcSinkConfig;
            case "doris":
                // 正则匹配 转义schema 其实有的会是数据库，但在这里这里统称为schema
                schemaName = escapeSql(options.getDatabaseName());
                // 正则匹配 转义表名
                tableName = escapeSql(options.getTableName());
                // doris sink details url https://seatunnel.apache.org/docs/connector-v2/sink/Doris
                DorisSinkConfig dorisSinkConfig = new DorisSinkConfig();
                dorisSinkConfig.setSinkEnable2pc(false);
                dorisSinkConfig.setFenodes(String.format("%s:%s", dataSourceBasicInfo.getHost(), dataSourceBasicInfo.getBePort()));
                dorisSinkConfig.setQueryPort(String.valueOf(dataSourceBasicInfo.getPort()));
                dorisSinkConfig.setUsername(dataSourceBasicInfo.getUsername());
                dorisSinkConfig.setPassword(dataSourceBasicInfo.getPassword());
                dorisSinkConfig.setTableIdentifier(String.format(JDBC_TABLE_FMT, schemaName, tableName));
                dorisSinkConfig.setSinkLabelPrefix("sink");
                dorisSinkConfig.setDorisConfig(new HashMap<String, String>() {{
                    put("format", "json");
                    put("read_json_by_line", "true");
                }});
                return dorisSinkConfig;
            case "iceberg":
                String catalogName = "iceberg";
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getCatalog())) {
                    catalogName = dataSourceBasicInfo.getCatalog();
                }
                IcebergSinkConfig icebergSinkConfig = new IcebergSinkConfig();
                icebergSinkConfig.setCatalogName(catalogName);
                Map<String, String> catalogConfig = new HashMap<>();
                catalogConfig.put("type", "hive");
                catalogConfig.put("uri", String.format("thrift://%s:%s", dataSourceBasicInfo.getMetastoreHost(), dataSourceBasicInfo.getMetastorePort()));
                catalogConfig.put("warehouse", dataSourceBasicInfo.getWarehouse());
                icebergSinkConfig.setCatalogConfig(catalogConfig);
                // hadoop 配置文件路径
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getHdfsSitePath())) {
                    // 转换为Path对象
                    Path path = Paths.get(dataSourceBasicInfo.getHdfsSitePath().split(",")[0]);
                    // 获取父目录
                    Path directory = path.getParent();
                    // Hadoop conf 目录 需要包含 "core-site.xml", "hdfs-site.xml", "hive-site.xml"
                    String hadoopConfPath = directory.toString();
                    icebergSinkConfig.setHadoopConfPath(hadoopConfPath);
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosPrincipal())) {
                    icebergSinkConfig.setKerberosPrincipal(dataSourceBasicInfo.getKerberosPrincipal());
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosKeytabPath())) {
                    icebergSinkConfig.setKerberosKeytabPath(dataSourceBasicInfo.getKerberosKeytabPath());
                }
                if (StringUtils.isNotEmpty(dataSourceBasicInfo.getKerberosConfPath())) {
                    icebergSinkConfig.setKerberosKrb5ConfPath(dataSourceBasicInfo.getKerberosConfPath());
                }
                icebergSinkConfig.setNamespace(escapeSql(options.getDatabaseName()));
                icebergSinkConfig.setTable(escapeSql(options.getTableName()));
                // 是否开启 upsert模式，数据覆盖模式下开启
                String primaryKey = getPrimaryKeyStr(options);
                if (StringUtils.isNotEmpty(primaryKey) && options.getIsUpsert()) {
                    // 开启 upsert
                    icebergSinkConfig.setTableUpsertEnabled(true);
                    icebergSinkConfig.setTablePrimaryKeys(primaryKey);
                }
                icebergSinkConfig.setCaseSensitive(true);
                return icebergSinkConfig;
            default:
                throw new RuntimeException(String.format("数据集成目标数据源暂不支[%s]类型", dbType));
        }
    }


    /**
     * 获取 cdc source 配置
     *
     * @param shadeIdentifier 配置文件敏感信息加密方式
     * @param dbType          数据类型
     * @param options         Cdc源数据实体
     * @return
     */
    public static String getCdcSourceInstance(String shadeIdentifier, String dbType, CdcDataModelOptions options) {
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(options.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        // 密码脱敏
        String password = dataSourceBasicInfo.getPassword();
        if (StringUtils.isNotBlank(shadeIdentifier)) {
            password = desensitization(shadeIdentifier, password);
        }
        // 数据库名称 这里这样做是因为 pg sqlserver这些数据库 是有schema的，所以数据库会必填，其他的jdbc类型的数据库数据库非必填，如果为空就取参数中传过来的数据库名称
        String dataBaseName = escapeSql(dataSourceBasicInfo.getDatabase());
        StringBuilder cdcConfigBuilder = new StringBuilder();
        String cdcType = null;
        if (dbType.equalsIgnoreCase("mysql")) {
            cdcType = SeaTunnelConnectorType.MYSQL_CDC.getCode();
        } else if (dbType.equalsIgnoreCase("postgresql")) {
            cdcType = SeaTunnelConnectorType.POSTGRESQL_CDC.getCode();
        } else if (dbType.equalsIgnoreCase("sqlserver")) {
            cdcType = SeaTunnelConnectorType.SQLSERVER_CDC.getCode();
        } else if (dbType.equalsIgnoreCase("dm")) {
            cdcType = SeaTunnelConnectorType.DM_CDC.getCode();
        } else if (dbType.equalsIgnoreCase("oracle")) {
            cdcType = SeaTunnelConnectorType.ORACLE_CDC.getCode();
            String serviceName;
            if (dataSourceBasicInfo.getOracleType() == OracleType.SID) {
                serviceName = dataSourceBasicInfo.getSid();
            } else {
                serviceName = dataSourceBasicInfo.getServiceName();
            }
            serviceName = escapeSql(serviceName);
            String baseUrl = String.format("**************************", dataSourceBasicInfo.getHost(), dataSourceBasicInfo.getPort(), serviceName);
            cdcConfigBuilder.append("    base-url = ").append("\"").append(baseUrl).append("\"\n");
            cdcConfigBuilder.append("    username = ").append("\"").append(dataSourceBasicInfo.getUsername()).append("\"\n");
            cdcConfigBuilder.append("    password = ").append("\"").append(password).append("\"\n");
            cdcConfigBuilder.append("    database-names = ").append("[\"").append(serviceName).append("\"]\n");
            String finalServiceName = serviceName;
            String schemaNames = options.getCdcObjects().stream()
                    .map(cdcObject -> "\"" + escapeSql(cdcObject.getDataBaseName()) + "\"")
                    .collect(Collectors.joining(","));
            String tableNames = options.getCdcObjects().stream()
                    .flatMap(cdcObject -> cdcObject.getTableNames().stream()
                            .map(tableName -> "\"" + String.format("%s.%s.%s", finalServiceName, escapeSql(cdcObject.getDataBaseName()), escapeSql(tableName)) + "\""))
                    .collect(Collectors.joining(","));
            cdcConfigBuilder.append("    schema-names = ").append("[").append(schemaNames).append("]\n");
            cdcConfigBuilder.append("    table-names = ").append("[").append(tableNames).append("]\n");
        } else {
            throw new RuntimeException(String.format("实时数据集成源数据源暂不支[%s]类型", dbType));
        }
        if (options.getCustomConfig() != null) {
            options.getCustomConfig().forEach((key, val) -> {
                cdcConfigBuilder.append("    ").append(key).append(" = ").append("\"").append(val).append("\"\n");
            });
        }
        if (options.getDebezium() != null) {
            cdcConfigBuilder.append("    debezium = ").append("{\n");
            options.getDebezium().forEach((key, val) -> {
                cdcConfigBuilder.append("        ").append(key).append(" = ").append("\"").append(val).append("\"\n");
            });
            cdcConfigBuilder.append("    }\n");
        }
        // cdc source
        StringBuilder cdcBuilder = new StringBuilder();
        cdcBuilder.append("  ").append(cdcType).append(" {\n");
        cdcBuilder.append(cdcConfigBuilder);
        cdcBuilder.append("  }\n");
        return cdcBuilder.toString();
    }

    /**
     * 获取 transform sql 配置
     *
     * @param sourceOptions 源表
     * @return transform sql 配置
     */
    public static String getTransformSqlInstance(DataModelOptions sourceOptions, DataModelOptions sinkOptions) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("  Sql {\n");
        sqlBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
        sqlBuilder.append("    result_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
        // query sql 拼接，内置列放在这里拼接，ST的transform支持内置列
        StringBuilder selectBuilder = new StringBuilder("SELECT ");
        selectBuilder.append(SeaTunnelConnectorFactory.getTransformFieldsStr(sourceOptions.getSeaTunnelFields(), sourceOptions.getBuiltInFields(), sinkOptions.getSeaTunnelFields()));
        selectBuilder.append(" FROM ");
        selectBuilder.append(ST_GLOBAL_VIEW_NAME);
        // 不需要拼接source where条件
        /*if (StringUtils.isNotEmpty(sourceOptions.getWhereSql())){
            selectBuilder.append(" WHERE ").append(sourceOptions.getWhereSql());
        }*/
        sqlBuilder.append("    query = ").append("\"").append(selectBuilder).append("\"\n");
        sqlBuilder.append("  }\n");
        return sqlBuilder.toString();
    }

    /**
     * TransformFieldMapper
     *
     * @param sourceOptions 源信息
     * @param sinkOptions   目标信息
     * @return
     */
    private String getTransformFieldMapperInstance(DataModelOptions sourceOptions, DataModelOptions sinkOptions) {
        StringBuilder fieldMapperBuilder = new StringBuilder();
        // 映射属性数量匹配时 才拼接FieldMapper
        if (sourceOptions.getSeaTunnelFields().size() != sinkOptions.getSeaTunnelFields().size()) {
            return fieldMapperBuilder.toString();
        }
        fieldMapperBuilder.append("  FieldMapper {\n");
        fieldMapperBuilder.append("    source_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
        fieldMapperBuilder.append("    result_table_name = ").append("\"").append(ST_GLOBAL_VIEW_NAME).append("\"\n");
        fieldMapperBuilder.append("    field_mapper = {\n");
        // 拼接映射关系 默认一一对应
        for (int i = 0; i < sinkOptions.getSeaTunnelFields().size(); i++) {
            fieldMapperBuilder.append("      ").append(sourceOptions.getSeaTunnelFields().get(i).getFieldName()).append(" = ").append(sinkOptions.getSeaTunnelFields().get(i).getFieldName()).append("\n");
        }
        // 拼接内置列映射 内置列默认列名保持一致
        for (int i = 0; i < sinkOptions.getBuiltInFields().size(); i++) {
            fieldMapperBuilder.append("      ").append(sourceOptions.getBuiltInFields().get(i).getFieldName()).append(" = ").append(sourceOptions.getBuiltInFields().get(i).getFieldName()).append("\n");
        }
        fieldMapperBuilder.append("    }\n");
        fieldMapperBuilder.append("  }\n");
        return fieldMapperBuilder.toString();
    }


    /**
     * 列集合转译列字符串 拼接 col1,col2,...
     *
     * @param sourceSeaTunnelFields 源字段列
     * @param builtInFields         实体内置列（目标实体的内置列会复制一份到源的对象集合中）
     * @param sinkSeaTunnelFields   目标字段列
     * @return 字符串 col1,col2,...
     */
    private static String getTransformFieldsStr(List<SeaTunnelField> sourceSeaTunnelFields, List<SeaTunnelField> builtInFields, List<SeaTunnelField> sinkSeaTunnelFields) {
        List<String> fields = new ArrayList<>();
        for (int i = 0; i < sourceSeaTunnelFields.size(); i++) {
            SeaTunnelField field = sourceSeaTunnelFields.get(i);
            String fieldName = field.getFieldName();
            if (StringUtils.isEmpty(fieldName)) {
                continue;
            }
            if (StringUtils.isNotEmpty(field.getTransformFieldType())) {
                if (field.getTransformFieldType().equals(SeaTunnelCastType.RAWTOHEX.getCode())) {
                    fields.add(String.format("RAWTOHEX(%s) AS %s", fieldName, sinkSeaTunnelFields.get(i).getFieldName()));
                } else {
                    fields.add(String.format("CAST(%s AS %s) AS %s", fieldName, field.getTransformFieldType(), sinkSeaTunnelFields.get(i).getFieldName()));
                }
            } else {
                // 字符串格式化 源列 as 目标列 这里前端那边都是source跟sink列的顺序都是一一对应保持一致的，所以这里直接取对应位置的sink字段列名称即可，如果以后有变化，就再调整对应的逻辑
                String builtInFmt = "%s as %s";
                fields.add(String.format(builtInFmt, fieldName, sinkSeaTunnelFields.get(i).getFieldName()));
            }
        }
        // 拼接实体内置列
        if (builtInFields != null && !builtInFields.isEmpty()) {
            for (SeaTunnelField field : builtInFields) {
                String fieldName = field.getFieldName();
                if (StringUtils.isEmpty(fieldName)) {
                    continue;
                }
                // 字符串格式化
                String builtInFmt = "'${%s}' as %s";
                // 实体内置列需要单独插入数据
                if (BuiltinField._BATCH_ID.getDesc().equalsIgnoreCase(fieldName)) {
                    fields.add(String.format(builtInFmt, BuiltinField._BATCH_ID.getDesc(), fieldName));
                } else if (BuiltinField._IMPORT_TIME.getDesc().equalsIgnoreCase(fieldName)) {
                    builtInFmt = "TO_DATE('${%s}', 'yyyy-MM-dd HH:mm:ss') as %s";
                    fields.add(String.format(builtInFmt, BuiltinField._IMPORT_TIME.getDesc(), fieldName));
                }
            }
        }
        return StringUtils.join(fields, ",");
    }


    /**
     * 生成 insert sql （暂时不用了，采用seatunnel自动生成的sink sql）
     *
     * @param options 数据模型
     * @return insert sql
     */
    private static String buildInsertSqlStr(DataModelOptions options) {
        if (options.getSeaTunnelFields() == null) {
            throw new ServiceException("目标字段不能为空，请映射字段关系");
        }
        StringBuilder insertBuilder = new StringBuilder("INSERT INTO ");
        // 正则匹配 转义库名
        String dataBaseName = escapeSql(options.getDatabaseName());
        // 判断如果包含转义符则不需要拼接
        if ("postgresql".equalsIgnoreCase(options.getSinkDbType()) && !dataBaseName.contains("\\\"")) {
            dataBaseName = "\\\"" + dataBaseName + "\\\"";
        }
        // 正则匹配 转义表名
        String tableName = escapeSql(options.getTableName());
        // 判断如果包含转义符则不需要拼接
        if ("postgresql".equalsIgnoreCase(options.getSinkDbType()) && !tableName.contains("\\\"")) {
            tableName = "\\\"" + tableName + "\\\"";
        }
        insertBuilder.append(String.format(JDBC_TABLE_FMT, dataBaseName, tableName));
        insertBuilder.append("(");
        // 拼接 内置列（但是这里会有逻辑问题，如果他的源包含了这个列如何处理？）
        List<SeaTunnelField> builtInField = options.getBuiltInFields();
        int builtInFieldSize = 0;
        if (builtInField != null) {
            builtInFieldSize = builtInField.size();
        }
        // 计算占位符数量 表列字段数量+内置列字段数量
        int fieldSize = options.getSeaTunnelFields().size() + builtInFieldSize;
        insertBuilder.append(SeaTunnelConnectorFactory.getInsertFieldsStr(options.getSinkDbType(), options.getSeaTunnelFields(), builtInField));
        insertBuilder.append(") ").append("VALUES(");
        insertBuilder.append(SeaTunnelConnectorFactory.getInsertFields(fieldSize));
        insertBuilder.append(")");
        return insertBuilder.toString();
    }

    /**
     * 生成 select sql
     *
     * @param sourceOptions 源数据数据模型
     * @param sinkOptions   目标数据数据模型
     * @param useJsonFormat 是否使用json格式
     * @return insert sql
     */
    private static String buildQuerySqlStr(DataModelOptions sourceOptions, DataModelOptions sinkOptions, boolean useJsonFormat, boolean enableTransform) {
        StringBuilder selectBuilder = new StringBuilder("SELECT ");
        if (enableTransform) {
            selectBuilder.append(SeaTunnelConnectorFactory.getFieldsStr(sourceOptions, useJsonFormat));
        } else {
            selectBuilder.append(SeaTunnelConnectorFactory.getFieldsStrDisabledTransform(sourceOptions, sinkOptions, useJsonFormat));
        }
        // 正则匹配 转义库名
        String dataBaseName = escapeSql(sourceOptions.getDatabaseName());
        // 非json格式下拼接
        if (!useJsonFormat) {
            // 判断如果包含转义符则不需要拼接
            if ("postgresql".equalsIgnoreCase(sourceOptions.getSourceDbType()) && !dataBaseName.contains("\\\"")) {
                dataBaseName = "\\\"" + dataBaseName + "\\\"";
            }
        }
        // 正则匹配 转义表名
        String tableName = escapeSql(sourceOptions.getTableName());
        // 非json格式下拼接
        if (!useJsonFormat) {
            // 判断如果包含转义符则不需要拼接
            if ("postgresql".equalsIgnoreCase(sourceOptions.getSourceDbType()) && !tableName.contains("\\\"")) {
                tableName = "\\\"" + tableName + "\\\"";
            }
        }
        selectBuilder.append(" FROM ").append(String.format(JDBC_TABLE_FMT, dataBaseName, tableName));
        if (StringUtils.isNotEmpty(sourceOptions.getWhereSql())) {
            selectBuilder.append(" WHERE ").append(sourceOptions.getWhereSql());
        }
        return selectBuilder.toString();
    }

    /**
     * 获取目标端的主键列
     *
     * @param options
     * @return
     */
    private static List<String> getSinkPrimaryKeys(DataModelOptions options) {
        //Collectors.joining("\",\"", "\"", "\"")
        return options.getSeaTunnelFields().stream().filter(SeaTunnelField::getIsPk).map(SeaTunnelField::getFieldName).collect(Collectors.toList());
    }


    /**
     * 转义SQL中的特殊字符
     *
     * @param input 需要转义的字符串
     * @return 转义后的字符串
     */
    private static String escapeSql(String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        // 使用Matcher查找并替换特殊字符
        // 创建Pattern对象
        Pattern pattern = Pattern.compile(SQL_REGEX);
        // 正则匹配
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return "\\\"" + input + "\\\"";
        }
        return input;  // 使用双反斜杠进行转义
    }

    /**
     * 配置文件敏感信息加密
     *
     * @param shadeIdentifier 脱敏加密方式
     * @param content         脱敏内容
     * @return
     */
    public static String desensitization(String shadeIdentifier, String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        switch (shadeIdentifier) {
            case "base64":
                return ENCODER.encodeToString(content.getBytes(StandardCharsets.UTF_8));
            default:
                throw new IllegalStateException("Unexpected value: " + shadeIdentifier);
        }
    }

    /**
     * 获取 SQL关键字配置 配置
     *
     * @return SeaTunnel 配置
     */
    private static List<String> getSqlKeywordsConfig() {
        try {
            R<Object> r = remoteParamService.selectValueByCode("SQL_KEYWORDS");
            if (r.getCode() == Status.SUCCESS.getCode()) {
                String data = (String) r.getData();
                if (StringUtils.isNotEmpty(data)) {
                    JSONArray jsonArray = JSONArray.parseArray(data);
                    // 直接转换为 List<String>
                    return jsonArray.toJavaList(String.class);
                }
            }
        } catch (Exception e) {
            e.getMessage();
        }
        return null;
    }

}
