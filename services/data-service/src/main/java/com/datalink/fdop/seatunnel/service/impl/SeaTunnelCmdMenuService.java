package com.datalink.fdop.seatunnel.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdMenu;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdTree;
import com.datalink.fdop.seatunnel.mapper.SeaTunnelCmdMapper;
import com.datalink.fdop.seatunnel.mapper.SeaTunnelCmdMenuMapper;
import com.datalink.fdop.seatunnel.service.ISeaTunnelCmdMenuService;
import com.datalink.fdop.seatunnel.service.ISeaTunnelCmdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Service
@Slf4j
public class SeaTunnelCmdMenuService implements ISeaTunnelCmdMenuService {

    @Autowired
    private SeaTunnelCmdMapper seaTunnelCmdMapper;

    @Autowired
    private SeaTunnelCmdMenuMapper seaTunnelCmdMenuMapper;

    @Autowired
    private ISeaTunnelCmdService seaTunnelCmdService;


    /**
     * 全局序号间隔参数 当前数领 * 序号间隔数量
     */
    @Value("${dwms.serial-interval}")
    private Integer serialIntervalNumber;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(SeaTunnelCmdMenu seaTunnelCmdMenu) {
        if (seaTunnelCmdMenuMapper.selectByCode(seaTunnelCmdMenu.getCode()) != null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_MENU_ALREADY_EXISTS);
        }
        seaTunnelCmdMenu.setId(IdWorker.getId());
        int insert = seaTunnelCmdMenuMapper.insertSeaTunnelCmdMenu(seaTunnelCmdMenu);
        // 创建菜单边关系
        if (insert > 0 && seaTunnelCmdMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            seaTunnelCmdMenuMapper.createSeaTunnelCmdMenuEdge(seaTunnelCmdMenu.getPid(), Arrays.asList(seaTunnelCmdMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(SeaTunnelCmdMenu seaTunnelCmdMenu) {
        VlabelItem<SeaTunnelCmdMenu> vlabelItem = seaTunnelCmdMenuMapper.selectById(seaTunnelCmdMenu.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_MENU_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(seaTunnelCmdMenu.getCode()) && seaTunnelCmdMenuMapper.checkCodeIsExists(seaTunnelCmdMenu.getId(), seaTunnelCmdMenu.getCode()) != null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_MENU_ALREADY_EXISTS);
        }
        int update = seaTunnelCmdMenuMapper.updateById(seaTunnelCmdMenu);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            seaTunnelCmdMenuMapper.deleteSeaTunnelCmdMenuEdge(Arrays.asList(seaTunnelCmdMenu.getId()), vlabelItem.getProperties().getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (seaTunnelCmdMenu.getPid() != -1L) {
                seaTunnelCmdMenuMapper.createSeaTunnelCmdMenuEdge(seaTunnelCmdMenu.getPid(), Arrays.asList(seaTunnelCmdMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<SeaTunnelCmdMenu> vlabelItem = seaTunnelCmdMenuMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            SeaTunnelCmdMenu seaTunnelCmdMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = seaTunnelCmdMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = seaTunnelCmdMenuMapper.bacthUpdatePidById(menuIdList, seaTunnelCmdMenu.getPid());
                if (update > 0 && seaTunnelCmdMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    seaTunnelCmdMenuMapper.createSeaTunnelCmdMenuEdge(seaTunnelCmdMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = seaTunnelCmdMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int update = seaTunnelCmdMapper.bacthUpdatePidById(elementIdList, seaTunnelCmdMenu.getPid());
                if (update > 0 && seaTunnelCmdMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    seaTunnelCmdMapper.createSeaTunnelCmdAndMenuEdge(seaTunnelCmdMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return seaTunnelCmdMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<SeaTunnelCmdTree> tree(String sort, String code, Boolean isQueryNode) {
        List<SeaTunnelCmdTree> trees = new ArrayList<>();
        // 添加Flink流任务树
        if (isQueryNode) {
            trees.addAll(seaTunnelCmdMapper.selectTree(sort, code));
        }
        // 添加Flink流任务菜单树
        trees.addAll(seaTunnelCmdMenuMapper.selectMenuTree(sort, null));
        // 递归成树结构
        List<SeaTunnelCmdTree> treeList = (List<SeaTunnelCmdTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public PageDataInfo<SeaTunnelCmd> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<SeaTunnelCmd> page = PageUtils.getPage(SeaTunnelCmd.class);
        IPage<SeaTunnelCmd> SeaTunnelCmdIpage = seaTunnelCmdMenuMapper.overview(page, pid, sort, searchVo);
        List<SeaTunnelCmd> records = SeaTunnelCmdIpage.getRecords();
        for (SeaTunnelCmd seaTunnelCmd : records) {
            // 更新SeaTunnel任务状态
            seaTunnelCmdService.syncUpdateSeaTunnelTaskStatus(seaTunnelCmd);
        }
        return PageUtils.getPageInfo(records, (int) SeaTunnelCmdIpage.getTotal());
    }

    @Override
    public Integer querySerialNumber(Boolean menuFlag) {
        if (menuFlag) {
            return seaTunnelCmdMenuMapper.querySerialNumber() * serialIntervalNumber;
        } else {
            return seaTunnelCmdMapper.querySerialNumber() * serialIntervalNumber;
        }

    }

}
