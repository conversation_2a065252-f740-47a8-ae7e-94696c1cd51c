package com.datalink.fdop.seatunnel.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.restapi.domain.JobInfo;
import com.datalink.fdop.seatunnel.service.ISeaTunnelCmdService;
import com.datalink.fdop.seatunnel.utils.ResponseTaskLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 使用CMD 启动SeaTunnel
 * 目前只适用于SeaTunnel STREAMING，可以用于STREAMING任务的提交，状态查询，停止，从检查点重启等操作
 */
@RequestMapping(value = "/seatunnel/seatunnel/cmd")
@RestController
@Api(tags = "SeaTunnel任务api")
public class SeaTunnelCmdController {

    @Autowired
    private ISeaTunnelCmdService seaTunnelCmdService;

    @ApiOperation("创建SeaTunnel任务")
    @Log(title = "SeaTunnel任务", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody SeaTunnelCmd seaTunnelCmd) {
        return R.toResult(seaTunnelCmdService.create(seaTunnelCmd));
    }

    @ApiOperation("修改SeaTunnel任务")
    @Log(title = "SeaTunnel任务", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody SeaTunnelCmd seaTunnelCmd) {
        return R.toResult(seaTunnelCmdService.update(seaTunnelCmd));
    }

    @ApiOperation("删除SeaTunnel任务")
    @Log(title = "SeaTunnel任务", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.LEASE_SPECIFY_THE_FLOW_TO_DELETE);
        }
        return R.toResult(seaTunnelCmdService.delete(ids));
    }

    @ApiOperation("根据id查询SeaTunnel任务信息")
    @Log(title = "SeaTunnel任务")
    @GetMapping(value = "/selectById/{id}")
    public R<SeaTunnelCmd> selectById(@PathVariable("id") Long id) {
        return R.ok(seaTunnelCmdService.selectById(id));
    }

    @ApiOperation("检查任务状态")
    @Log(title = "SeaTunnel任务")
    @GetMapping(value = "/checkStatus/{id}")
    public R checkStatus(@PathVariable("id") Long id) {
        return R.ok(seaTunnelCmdService.checkStatus(id));
    }

    @ApiOperation("判断任务是否存在")
    @Log(title = "SeaTunnel任务")
    @GetMapping(value = "/checkTaskIsExists")
    public R<Boolean> checkTaskIsExists(@RequestParam(value = "taskName") String taskName) {
        return R.ok(seaTunnelCmdService.checkTaskIsExists(taskName));
    }

    @ApiOperation("提交任务")
    @Log(title = "SeaTunnel任务")
    @PostMapping(value = "/submit")
    public R<JobInfo> submit(@RequestBody SeaTunnelCmd seaTunnelCmd) {
        return seaTunnelCmdService.submitJob(seaTunnelCmd);
    }

    @ApiOperation("停止任务")
    @Log(title = "SeaTunnel任务")
    @PostMapping(value = "/stop/{id}")
    public R stop(@PathVariable("id") Long id) {
        return seaTunnelCmdService.stopJob(id);
    }

    @ApiOperation("恢复任务")
    @Log(title = "SeaTunnel任务")
    @PostMapping(value = "/restore/{id}")
    public R<JobInfo> restore(@PathVariable("id") Long id) {
        return seaTunnelCmdService.restoreJob(id);
    }

    @ApiOperation("根据任务ID获取任务日志")
    @Log(title = "SeaTunnel任务")
    @GetMapping(value = "/getLog")
    public R<ResponseTaskLog> queryLog(@RequestParam(value = "id") Long id,
                                            @RequestParam(value = "skipLineNum") int skipNum,
                                            @RequestParam(value = "limit") int limit) {
        return R.ok(seaTunnelCmdService.getLog(id, skipNum, limit), Status.SUCCESS.getMsg());
    }

}
