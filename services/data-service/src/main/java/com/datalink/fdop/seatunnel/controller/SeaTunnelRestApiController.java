package com.datalink.fdop.seatunnel.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelSubmitConfig;
import com.datalink.fdop.seatunnel.api.restapi.domain.Job;
import com.datalink.fdop.seatunnel.api.restapi.domain.JobInfo;
import com.datalink.fdop.seatunnel.service.ISeaTunnelRestApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SeaTunnel RestApi Controller
 */
@RestController
@RequestMapping(value = "/seatunnel/seatunnel/rest/api")
@Api(tags = "SeaTunnel Rest API")
public class SeaTunnelRestApiController {

    @Autowired
    private ISeaTunnelRestApiService seaTunnelRestApiService;

    @ApiOperation("提交SeaTunnel任务")
    @Log(title = "SeaTunnel Rest API")
    @PostMapping("/submit-job")
    public R<JobInfo> submitJob(@RequestBody SeaTunnelSubmitConfig modelConfig) {
        return R.ok(seaTunnelRestApiService.submitJob(modelConfig));
    }

    @ApiOperation("获取SeaTunnel任务")
    @Log(title = "SeaTunnel Rest API")
    @PostMapping("/running-job/{jobId}")
    public R<Job> getJob(@PathVariable(value = "jobId") String jobId) {
        return R.ok(seaTunnelRestApiService.getJob(jobId));
    }

    @ApiOperation("停止SeaTunnel任务")
    @Log(title = "SeaTunnel Rest API")
    @PostMapping("/stop-job/{jobId}")
    public R<Job> stopJob(@PathVariable(value = "jobId") String jobId) {
        return R.ok(seaTunnelRestApiService.stopJob(jobId));
    }

    @ApiOperation("查询历史SeaTunnel任务列表")
    @Log(title = "SeaTunnel Rest API")
    @PostMapping("/finished-jobs/{state}")
    public R<List<Job>> finishedJobs(@PathVariable(value = "state") String state) {
        return R.ok(seaTunnelRestApiService.finishedJobs(state));
    }



}
