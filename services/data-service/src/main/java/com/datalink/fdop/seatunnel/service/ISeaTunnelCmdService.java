package com.datalink.fdop.seatunnel.service;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.restapi.domain.Job;
import com.datalink.fdop.seatunnel.api.restapi.domain.JobInfo;
import com.datalink.fdop.seatunnel.utils.ResponseTaskLog;

import java.util.List;

public interface ISeaTunnelCmdService {

    int create(SeaTunnelCmd seaTunnelCmd);

    int update(SeaTunnelCmd seaTunnelCmd);

    int delete(List<Long> ids);

    SeaTunnelCmd selectById(Long id);

    Boolean checkStatus(Long id);

    Boolean checkTaskIsExists(String taskName);

    ResponseTaskLog getLog(Long id, int skipLineNum, int limit);


    /**
     * 提交 SeaTunnel任务
     * @param seaTunnelCmd SeaTunnel任务配置信息
     * @return  SeaTunnel 任务信息
     */
    R<JobInfo> submitJob(SeaTunnelCmd seaTunnelCmd);

    void checkSeaTunnelTaskStatus(SeaTunnelCmd seaTunnelCmd);

    /**
     * 获取执行中的任务情况（如果任务执行完成，接口会查询不到）
     * @param jobId JobId
     * @return 任务执行信息
     */
    Job getJob(String jobId);


    /**
     * 停止正在运行中的任务
     * @param id SeaTunnelCmd任务主键ID
     * @return JobId
     */
    R stopJob(Long id);

    /**
     * 基于SavePoint恢复任务
     * @param id SeaTunnelCmd任务主键ID
     * @return
     */
    R<JobInfo> restoreJob(Long id);


    /**
     * 查询所有已完成的任务
     * @param state 根据任务状态查询
     * @return 任务集合信息
     */
    List<Job> finishedJobs(String state);

    /**
     * 同步更新SeaTunnel任务状态(调用Rest API)
     * @param seaTunnelCmd
     */
    void syncUpdateSeaTunnelTaskStatus(SeaTunnelCmd seaTunnelCmd);

}
