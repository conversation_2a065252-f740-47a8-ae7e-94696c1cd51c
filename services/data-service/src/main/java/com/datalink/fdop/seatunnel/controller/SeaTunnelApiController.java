package com.datalink.fdop.seatunnel.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.seatunnel.api.domain.DataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelRunConfig;
import com.datalink.fdop.seatunnel.api.enums.SeaTunnelCastType;
import com.datalink.fdop.seatunnel.service.ISeaTunnelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SeaTunnel API
 */
@RestController
@RequestMapping(value = "/seatunnel/seatunnel/api")
@Api(tags = "SeaTunnel API")
public class SeaTunnelApiController {

    @Autowired
    private ISeaTunnelService seaTunnelService;

    @ApiOperation("获取数据模型的列信息")
    @Log(title = "SeaTunnel API")
    @PostMapping("/getDataModelOptionsFields")
    public R<List<SeaTunnelField>> getDataModelOptionsFields(@RequestBody DataModelOptions options) {
        return R.ok(seaTunnelService.getDataModelOptionsFields(options));
    }

    @ApiOperation("查询SeaTunnel可转译的类型")
    @GetMapping("/querySeaTunnelCastType")
    public R<List<SelectVo>> querySeaTunnelCastType() {
        List<SelectVo> selectVos = Arrays.stream(SeaTunnelCastType.values()).map(castType -> {
            return new SelectVo(castType.getCode(), castType.getCode());
        }).collect(Collectors.toList());
        return R.ok(selectVos);
    }

    @ApiOperation("生成SeaTunnel运行配置")
    @Log(title = "SeaTunnel API")
    @PostMapping("/generateSeaTunnelRunConfig")
    public R<String> generateSeaTunnelRunConfig(@RequestBody SeaTunnelRunConfig runConfig) {
        return R.ok(seaTunnelService.generateSeaTunnelRunConfig(runConfig.getEnv(), runConfig.getSourceOptions(), runConfig.getSinkOptions()),"success");
    }

    @ApiOperation("获取数据模型的数据源信息")
    @Log(title = "SeaTunnel API")
    @PostMapping("/getDataModelOptionsDataSource")
    public R<DataSource> getDataModelOptionsDataSource(@RequestBody DataModelOptions options) {
        return R.ok(seaTunnelService.getDataModelOptionsDataSource(options));
    }


    @ApiOperation("生成SeaTunnel运行配置目标为文件服务器")
    @Log(title = "SeaTunnel API")
    @PostMapping("/generateSeaTunnelRunConfigSinkFile")
    public R<String> generateSeaTunnelRunConfigSinkFile(@RequestBody SeaTunnelRunConfig runConfig) {
        return R.ok(seaTunnelService.generateSeaTunnelRunConfigSinkFile(runConfig.getEnv(), runConfig.getSourceOptions(), runConfig.getSinkFileOptions()),"success");
    }

    @ApiOperation("生成SeaTunnelCdc运行配置")
    @Log(title = "SeaTunnel API")
    @PostMapping("/generateSeaTunnelCdcRunConfig")
    public R<String> generateSeaTunnelCdcRunConfig(@RequestBody SeaTunnelRunConfig runConfig) {
        return R.ok(seaTunnelService.generateSeaTunnelRunConfigSinkFile(runConfig.getEnv(), runConfig.getSourceOptions(), runConfig.getSinkFileOptions()),"success");
    }

    @ApiOperation("SeaTunnel运行配置占位符转换")
    @Log(title = "SeaTunnel API")
    @PostMapping("/transformPlaceholderSeaTunnelRunConfig")
    public R<String> transformPlaceholderSeaTunnelRunConfig(@RequestBody SeaTunnelRunConfig runConfig) {
        return R.ok(seaTunnelService.transformPlaceholderSeaTunnelRunConfig(runConfig.getEnv(), runConfig.getSourceOptions(), runConfig.getSinkOptions(), runConfig.getSubmitConfig()),"success");
    }

}
