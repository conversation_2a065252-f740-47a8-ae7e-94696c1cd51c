package com.datalink.fdop.seatunnel.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdMenu;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdTree;
import com.datalink.fdop.seatunnel.service.ISeaTunnelCmdMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RequestMapping(value = "/seatunnel/seatunnel/cmd/menu")
@RestController
@Api(tags = "SeaTunnel任务菜单api")
public class SeaTunnelCmdMenuController extends BaseController {

    @Autowired
    private ISeaTunnelCmdMenuService seaTunnelCmdMenuService;

    @ApiOperation("创建SeaTunnel任务菜单")
    @Log(title = "SeaTunnel任务菜单", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody SeaTunnelCmdMenu seaTunnelCmdMenu) {
        return R.toResult(seaTunnelCmdMenuService.create(seaTunnelCmdMenu));
    }

    @ApiOperation("修改SeaTunnel任务菜单")
    @Log(title = "SeaTunnel任务菜单", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody SeaTunnelCmdMenu seaTunnelCmdMenu) {
        return R.toResult(seaTunnelCmdMenuService.update(seaTunnelCmdMenu));
    }

    @ApiOperation("删除SeaTunnel任务菜单")
    @Log(title = "SeaTunnel任务菜单", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_FLOW_MENU_TO_DELETE);
        }
        return R.toResult(seaTunnelCmdMenuService.delete(ids));
    }


    @ApiOperation("SeaTunnel任务树菜单")
    @Log(title = "SeaTunnel任务菜单")
    @GetMapping(value = "/tree")
    public R<List<SeaTunnelCmdTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode) {
        List<SeaTunnelCmdTree> list = seaTunnelCmdMenuService.tree(sort, code, isQueryNode);
        return R.ok(list);
    }


    @ApiOperation("总览")
    @Log(title = "SeaTunnel任务菜单")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<SeaTunnelCmd>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                            @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                            @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(seaTunnelCmdMenuService.overview(pid, sort, searchVo));
    }


    @ApiOperation("查序号")
    @Log(title = "SeaTunnel任务菜单")
    @GetMapping(value = "/querySerialNumber")
    public R<Integer> querySerialNumber(@RequestParam Boolean menuFlag) {
        return R.ok(seaTunnelCmdMenuService.querySerialNumber(menuFlag));
    }

}
