package com.datalink.fdop;


import com.datalink.fdop.common.security.annotation.EnableCustomConfig;
import com.datalink.fdop.common.security.annotation.EnableRyFeignClients;
import com.gitee.starblues.loader.launcher.SpringBootstrap;
import com.gitee.starblues.loader.launcher.SpringMainBootstrap;
import org.apache.dolphinscheduler.fdop.api.RemoteExecutorService;
import org.apache.dolphinscheduler.fdop.api.RemoteProjectService;
import org.apache.dolphinscheduler.fdop.api.RemoteResourceService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;

/**
 * 数据管理模块
 */
@EnableAsync
@EnableCustomConfig
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@EnableRyFeignClients(basePackageClasses = {
        RemoteProjectService.class,
        RemoteResourceService.class,
        RemoteExecutorService.class})
@SpringBootApplication(scanBasePackages = {"com.datalink.fdop", "datart.core.common", "datart.data.provider", "org.jeecg.modules.jmreport"})
public class DataCenterApplication implements SpringBootstrap {

    //private static final Logger logger = LoggerFactory.getLogger(DataCenterApplication.class);

    public static void main(String[] args) {
        SpringMainBootstrap.launch(DataCenterApplication.class, args);
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder)
    {
        return builder.build();
    }

    @Override
    public void run(String[] args) {
        SpringApplication.run(DataCenterApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  数据管理模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "  _____ ______ __  __ _____ \n" +
                " / ____|  ____|  \\/  |_   _|\n" +
                "| (___ | |__  | \\  / | | |  \n" +
                " \\___ \\|  __| | |\\/| | | |  \n" +
                " ____) | |____| |  | |_| |_ \n" +
                "|_____/|______|_|  |_|_____|\n");

        // 定时清理etl任务缓存的预览数据  etl任务暂时不用
//        while (true) {
//            try {
//                Thread.sleep(10000);
//
//                CacheDataLogMapper cacheDataLogMapper = SpringUtils.getBean(CacheDataLogMapper.class);
//                // 查询任务缓存的预览数据
//                List<CacheDataLog> cacheDataLogList = cacheDataLogMapper.selectAll();
//                // 过滤出大于一天时间的缓存数据
//                cacheDataLogList = cacheDataLogList.stream()
//                        .filter(cacheDataLog -> {
//                            LocalDateTime createTime = cacheDataLog.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
//                            LocalDateTime nowTime = LocalDateTime.now();
//
//                            Duration duration = Duration.between(createTime, nowTime);
//                            long hoursDifference = duration.toHours();
//
//                            if (Math.abs(hoursDifference) >= 24) {
//                                return true;
//                            }
//                            return false;
//                        }).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(cacheDataLogList)) {
//
//                    FlinkGraphMapper flinkGraphMapper = SpringUtils.getBean(FlinkGraphMapper.class);
//                    for (CacheDataLog cacheDataLog : cacheDataLogList) {
//                        // 删除缓存的数据
//                        String dropTableSql = "drop table if exists " + cacheDataLog.getDatabaseName() + "." + cacheDataLog.getTableName();
//                        logger.info("删表语句:{}", dropTableSql);
//                        flinkGraphMapper.execDdlSql(dropTableSql);
//                        // 删除缓存记录
//                        cacheDataLogMapper.deleteById(cacheDataLog.getId());
//                    }
//                }
//            } catch (Exception e) {
//                logger.error(e.getMessage(), e);
//            }
//        }
    }
}
