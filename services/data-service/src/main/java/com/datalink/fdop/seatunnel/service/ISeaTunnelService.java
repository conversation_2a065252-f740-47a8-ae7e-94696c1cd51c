package com.datalink.fdop.seatunnel.service;

import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.seatunnel.api.domain.DataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.FileModelOptions;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelEnv;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;

import java.util.List;

public interface ISeaTunnelService {

    /**
     * 获取数据模型的属性列
     * @param options 数据模型对象
     * @return ST属性列
     */
    List<SeaTunnelField> getDataModelOptionsFields(DataModelOptions options);

    /**
     * 生成SeaTunnel运行配置
     * @param env 运行环境配置
     * @param sourceOptions 源
     * @param sinkOptions 目标
     * @return SeaTunnel运行配置
     */
    String generateSeaTunnelRunConfig(SeaTunnelEnv env, DataModelOptions sourceOptions, DataModelOptions sinkOptions);

    /**
     * 生成SeaTunnel运行配置目标为文件服务器
     * @param env 运行环境配置
     * @param sourceOptions 源
     * @param sinkOptions 目标
     * @return SeaTunnel运行配置
     */
    String generateSeaTunnelRunConfigSinkFile(SeaTunnelEnv env, DataModelOptions sourceOptions, FileModelOptions sinkOptions);

    /**
     * 获取实体映射源表字段（如果数据模型为原表，则直接返回现有字段）
     * @param options  数据模型
     * @return 映射源表字段
     */
    List<SeaTunnelField> getEntityFieldsMappingFields(DataModelOptions options);


    /**
     * 获取数据模型的数据源映射信息
     * @param options 数据模型对象(是数据源则直接返回)
     * @return 数据源
     */
    DataSource getDataModelOptionsDataSource(DataModelOptions options);


    DataSource getDataSourceInfoById(DataModelOptions options);

    /**
     * SeaTunnel运行配置通配符转换
     * @param env 环境
     * @param sourceOptions 源
     * @param sinkOptions 目标
     * @param submitConfig 提交配置
     * @return 转换后的配置信息
     */
    String transformPlaceholderSeaTunnelRunConfig(SeaTunnelEnv env, DataModelOptions sourceOptions, DataModelOptions sinkOptions, String submitConfig);

}
