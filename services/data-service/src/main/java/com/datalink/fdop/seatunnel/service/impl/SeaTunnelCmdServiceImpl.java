package com.datalink.fdop.seatunnel.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd;
import com.datalink.fdop.seatunnel.api.restapi.domain.Job;
import com.datalink.fdop.seatunnel.api.restapi.domain.JobInfo;
import com.datalink.fdop.seatunnel.api.restapi.domain.SeaTunnelAPI;
import com.datalink.fdop.seatunnel.mapper.SeaTunnelCmdMapper;
import com.datalink.fdop.seatunnel.mapper.SeaTunnelCmdMenuMapper;
import com.datalink.fdop.seatunnel.service.ISeaTunnelCmdService;
import com.datalink.fdop.seatunnel.utils.Constants;
import com.datalink.fdop.seatunnel.utils.ResponseTaskLog;
import com.datalink.fdop.seatunnel.utils.ShellUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SeaTunnel RestApi 实现
 */
@Service
@Slf4j
public class SeaTunnelCmdServiceImpl implements ISeaTunnelCmdService {

    /**
     * SeaTunnel 安装路径
     */
    private final String SEATUNNEL_HOME = "/opt/soft/seatunnel";

    /**
     * SeaTunnel 执行配置文件路径
     */
    private final String SEATUNNEL_CONFIG_HOME = String.format("%s/dwms/config/", SEATUNNEL_HOME);

    /**
     * SeaTunnel 执行日志路径
     */
    private final String SEATUNNEL_LOG_HOME = String.format("%s/dwms/logs/", SEATUNNEL_HOME);

    /**
     * SeaTunnel 集群地址
     */
    @Value("${dwms.seatunnel.host}")
    private String seaTunnelHost;

    @Autowired
    private SeaTunnelCmdMapper seaTunnelCmdMapper;

    @Autowired
    private SeaTunnelCmdMenuMapper seaTunnelCmdMenuMapper;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public int create(SeaTunnelCmd seaTunnelCmd) {
        if (seaTunnelCmd.getPid() != -1L && seaTunnelCmdMenuMapper.selectById(seaTunnelCmd.getPid()) == null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_MENU_DOES_NOT_EXIST);
        }
        if (seaTunnelCmdMapper.selectByCode(seaTunnelCmd.getCode()) != null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_ALREADY_EXISTS);
        }
        seaTunnelCmd.setId(IdWorker.getId());
        if (seaTunnelCmd.getCommand() != null) {
            // 转义 存入PG age
            seaTunnelCmd.setCommand(seaTunnelCmd.getCommand().replace("'", "\\'"));
        }
        int insert = seaTunnelCmdMapper.insertSeaTunnelCmd(seaTunnelCmd);
        // 创建元素边关系
        if (insert > 0 && seaTunnelCmd.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            seaTunnelCmdMapper.createSeaTunnelCmdAndMenuEdge(seaTunnelCmd.getPid(), Arrays.asList(seaTunnelCmd.getId()));
        }
        return insert;
    }

    @Override
    public int update(SeaTunnelCmd seaTunnelCmd) {
        VlabelItem<SeaTunnelCmd> vlabelItem = seaTunnelCmdMapper.selectById(seaTunnelCmd.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(seaTunnelCmd.getCode()) && seaTunnelCmdMapper.checkCodeIsExists(seaTunnelCmd.getId(), seaTunnelCmd.getCode()) != null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_ALREADY_EXISTS);
        }
        // 不能拖拽到节点里面
        if (seaTunnelCmd.getPid() != null && seaTunnelCmd.getPid() != -1L && seaTunnelCmdMenuMapper.selectById(seaTunnelCmd.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_SEATUNNEL_CMD_MENU);
        }
        if (seaTunnelCmd.getCommand() != null) {
            // 转义 存入PG age
            seaTunnelCmd.setCommand(seaTunnelCmd.getCommand().replace("'", "\\'"));
        }
        int update = seaTunnelCmdMapper.updateById(seaTunnelCmd);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            seaTunnelCmdMapper.deleteSeaTunnelCmdAndMenuEdge(Arrays.asList(seaTunnelCmd.getId()), vlabelItem.getProperties().getPid());
            if (seaTunnelCmd.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                seaTunnelCmdMapper.createSeaTunnelCmdAndMenuEdge(seaTunnelCmd.getPid(), Arrays.asList(seaTunnelCmd.getId()));
            }
        }
        return update;
    }

    @Override
    public int delete(List<Long> ids) {
        return seaTunnelCmdMapper.deleteBatchIds(ids);
    }

    @Override
    public SeaTunnelCmd selectById(Long id) {
        VlabelItem<SeaTunnelCmd> vlabelItem = seaTunnelCmdMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_DOES_NOT_EXIST);
        }
        SeaTunnelCmd seaTunnelCmd = vlabelItem.getProperties();
        // 更新SeaTunnel任务状态
        syncUpdateSeaTunnelTaskStatus(seaTunnelCmd);
        return seaTunnelCmd;
    }

    @Override
    public Boolean checkStatus(Long id) {
        return true;
    }

    @Override
    public Boolean checkTaskIsExists(String taskName) {
        return true;
    }

    @Override
    public ResponseTaskLog getLog(Long id, int skipLineNum, int limit) {
        SeaTunnelCmd seaTunnelCmd = selectById(id);
        if (StringUtils.isEmpty(seaTunnelCmd.getLogFilePath())) {
            throw new ServiceException("未获取到SeaTunnel任务日志文件路径");
        }
        StringBuilder log = new StringBuilder();
        int lineNum = 0;
        if (Files.exists(Paths.get(seaTunnelCmd.getLogFilePath()))) {
            try (BufferedReader reader = Files.newBufferedReader(Paths.get(seaTunnelCmd.getLogFilePath()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (lineNum >= skipLineNum) {
                        log.append(line).append("\n");
                    }
                    lineNum++;
                    if (lineNum >= skipLineNum + limit) {
                        break;
                    }
                }
            } catch (IOException e) {
                log.append("读取日志文件失败");
            }
        } else {
            log.append("日志文件不存在");
        }
        return new ResponseTaskLog(lineNum, log.toString());
    }

    @Override
    public R<JobInfo> submitJob(SeaTunnelCmd seaTunnelCmd) {
        JobInfo jobInfo = new JobInfo();
        if (StringUtils.isEmpty(seaTunnelCmd.getCommand())) {
            throw new ServiceException("SeaTunnel任务配置不能为空");
        }
        String jobName = seaTunnelCmd.getCode();
        try {
            // 创建并写入配置文件
            String configFilePath = createAndWriteConfigSeaTunnelFile(jobName, seaTunnelCmd.getCommand());
            String logFilePath = createSeaTunnelLogFile(jobName);
            // 提交SeaTunnel任务
            String exeCmd = buildRunSeaTunnelCommand(configFilePath, jobName);
            log.info("提交SeaTunnel任务：{}", exeCmd);
            ShellUtils.execute(exeCmd, logFilePath);
            seaTunnelCmd.setJobName(seaTunnelCmd.getCode());
            seaTunnelCmd.setLogFilePath(logFilePath);
            // 重置任务ID
            seaTunnelCmdMapper.updateSeaTunnelTaskJobIdEmpty(seaTunnelCmd.getId());
            // 不重复更新SeaTunnel配置
            seaTunnelCmd.setCommand(null);
            seaTunnelCmd.setStatus("RUNNING");
            // 更新SeaTunnelCmd信息
            seaTunnelCmdMapper.updateById(seaTunnelCmd);
            // 异步检查任务是否正常执行
            applicationContext.getBean(ISeaTunnelCmdService.class).checkSeaTunnelTaskStatus(seaTunnelCmd);
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
            return R.fail("任务提交失败" + e.getMessage());
        }
        return R.ok(jobInfo);
    }

    @Async
    @Override
    public void checkSeaTunnelTaskStatus(SeaTunnelCmd seaTunnelCmd) {
        if (StringUtils.isEmpty(seaTunnelCmd.getLogFilePath())) {
            return;
        }
        Path logFile = Paths.get(seaTunnelCmd.getLogFilePath());
        while (true) {
            boolean error = checkForSeaTunnelError(logFile);
            if (!error) {
                String jobId = extractJobId(logFile);
                if (StringUtils.isNotEmpty(jobId)) {
                    log.info("SeaTunnel任务ID：{}", jobId);
                    seaTunnelCmd.setJobId(jobId);
                    seaTunnelCmd.setStatus("RUNNING");
                    break;
                }
            } else {
                seaTunnelCmd.setStatus("FAILED");
                break;
            }
        }
        // 更新SeaTunnelCmd信息
        seaTunnelCmdMapper.updateById(seaTunnelCmd);
    }

    @Override
    public Job getJob(String jobId) {
        if (StringUtils.isEmpty(jobId)) {
            return null;
        }
        SeaTunnelAPI api = SeaTunnelAPI.build(seaTunnelHost);
        return api.getJob(jobId);
    }

    @Override
    public R stopJob(Long id) {
        SeaTunnelCmd seaTunnelCmd = selectById(id);
        seaTunnelCmd.setStatus("FINISH");
        // 不重复更新SeaTunnel配置
        seaTunnelCmd.setCommand(null);
        // 更新SeaTunnelCmd信息
        seaTunnelCmdMapper.updateById(seaTunnelCmd);
        String jobId = seaTunnelCmd.getJobId();
        if (StringUtils.isEmpty(jobId)) {
            throw new ServiceException("SeaTunnel任务未运行");
        }
        try {
            // 提交SeaTunnel任务
            String exeCmd = buildCancelSeaTunnelCommand(jobId);
            log.info("停止SeaTunnel任务：{}", exeCmd);
            ShellUtils.execute(exeCmd, null);
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
            return R.fail("任务停止异常" + e.getMessage());
        }
        return R.ok("任务已停止");
    }

    @Override
    public R<JobInfo> restoreJob(Long id) {
        JobInfo jobInfo = new JobInfo();
        SeaTunnelCmd seaTunnelCmd = selectById(id);
        String jobId = seaTunnelCmd.getJobId();
        if (StringUtils.isEmpty(jobId)) {
            throw new ServiceException("SeaTunnel任务未运行");
        }
        String jobName = seaTunnelCmd.getCode();
        // 任务是否恢复
        boolean restore = true;
        try {
            // 创建并写入配置文件
            String configFilePath = createAndWriteConfigSeaTunnelFile(jobName, seaTunnelCmd.getCommand());
            String logFilePath = createSeaTunnelLogFile(jobName);
            // 恢复SeaTunnel任务
            String exeCmd = buildRestoreSeaTunnelCommand(configFilePath, jobName, jobId);
            log.info("恢复SeaTunnel任务：{}", exeCmd);
            ShellUtils.execute(exeCmd, logFilePath);
            seaTunnelCmd.setJobName(seaTunnelCmd.getCode());
            seaTunnelCmd.setLogFilePath(logFilePath);
            // 不重复更新SeaTunnel配置
            seaTunnelCmd.setCommand(null);
            seaTunnelCmd.setStatus("RUNNING");
            // 更新SeaTunnelCmd信息
            seaTunnelCmdMapper.updateById(seaTunnelCmd);
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
            return R.fail("任务提交失败" + e.getMessage());
        }
        if (!restore) {
            return R.fail("任务恢复失败，详情查看日志");
        }
        return R.ok(jobInfo);
    }

    @Override
    public List<Job> finishedJobs(String state) {
        if (StringUtils.isEmpty(state)) {
            return new ArrayList<>();
        }
        SeaTunnelAPI api = SeaTunnelAPI.build(seaTunnelHost);
        return api.finishedJobs(state);
    }

    @Override
    public void syncUpdateSeaTunnelTaskStatus(SeaTunnelCmd seaTunnelCmd) {
        try {
            String oldStatus = seaTunnelCmd.getStatus();
            if (StringUtils.isNotEmpty(seaTunnelCmd.getJobId())) {
                // 更新任务状态
                Job job = getJob(seaTunnelCmd.getJobId());
                if (job != null && StringUtils.isNotEmpty(job.getJobStatus())) {
                    seaTunnelCmd.setStatus(job.getJobStatus());
                } else {
                    // 任务是否以及完成
                    boolean isFinish = false;
                    // 查询历史任务状态(已完成已结束的任务只能查询该接口)
                    List<Job> finishedList = finishedJobs(null);
                    for (Job jobInfo : finishedList) {
                        if (seaTunnelCmd.getJobId().equals(jobInfo.getJobId())) {
                            seaTunnelCmd.setStatus(jobInfo.getJobStatus());
                            isFinish = true;
                        }
                    }
                    // 如果任务未在运行中、状态也未完成则表示任务失败
                    if (!isFinish) {
                        seaTunnelCmd.setStatus("FAILED");
                    }
                }
                // 状态相同不执行update
                if (!oldStatus.equals(seaTunnelCmd.getStatus())) {
                    seaTunnelCmdMapper.updateById(seaTunnelCmd);
                }
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
        }
    }

    /**
     * 创建并写入SeaTunnel配置文件
     *
     * @param jobName 任务名称
     * @return 配置文件路径
     */
    private String createAndWriteConfigSeaTunnelFile(String jobName, String command) throws Exception {
        String configFilePath = String.format("%s/%s.conf", SEATUNNEL_CONFIG_HOME, jobName);
        // 生成SeaTunnel执行配置文件
        File configFile = new File(configFilePath);
        // 判断文件目录是否存在
        if (!configFile.getParentFile().exists()) {
            configFile.getParentFile().mkdirs();
        }
        // 文件已存在就删除重新创建
        if (configFile.exists()) {
            configFile.delete();
        }
        configFile.createNewFile();
        // 写入配置文件
        FileUtils.writeStringToFile(configFile, command.replaceAll("\\r\\n", "\n"), "UTF-8");
        return configFilePath;
    }


    /**
     * 创建SeaTunnel日志文件
     *
     * @param jobName 任务名称
     * @return 日志文件路径
     */
    private String createSeaTunnelLogFile(String jobName) throws Exception {
        String logFilePath = String.format("%s/%s.log", SEATUNNEL_LOG_HOME, jobName);
        // 生成SeaTunnel执行配置文件
        File logFile = new File(logFilePath);
        // 判断文件目录是否存在
        if (!logFile.getParentFile().exists()) {
            logFile.getParentFile().mkdirs();
        }
        // 文件已存在就删除重新创建
        if (logFile.exists()) {
            logFile.delete();
        }
        logFile.createNewFile();
        return logFilePath;
    }

    /**
     * 提取日志文件中的SeaTunnel JobId
     *
     * @param logFile 日志文件
     * @return jobId
     */
    private static String extractJobId(Path logFile) {
        try (BufferedReader reader = Files.newBufferedReader(logFile)) {
            String line;
            Pattern pattern = Pattern.compile("Submit job finished, job id: (\\d+)");
            while ((line = reader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    return matcher.group(1); // 返回匹配的Job Id
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到Job Id，则返回null
    }

    private static boolean checkForSeaTunnelError(Path logFile) {
        try (BufferedReader reader = Files.newBufferedReader(logFile)) {
            String line;
            Pattern pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2},\\d{3} (ERROR|FATAL)");
            while ((line = reader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find() || line.toLowerCase().contains("seatunnel task error")) {
                    // 发现错误关键字，任务失败
                    return true;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            return true; // IO异常，可以视为任务失败的一种情况
        }
        return false; // 没有发现错误关键字，任务可能成功
    }

    /**
     * 取消SeaTunnel任务
     *
     * @param jobId SeaTunnel任务ID
     * @return sh执行命令
     */
    private String buildCancelSeaTunnelCommand(String jobId) {
        List<String> args = new ArrayList<>();
        args.add(String.format(Constants.RUN_COMMAND, SEATUNNEL_HOME));
        args.add(Constants.CANCEL_JOB);
        args.add(jobId);
        String command = String.join(" ", args);
        return command;
    }

    /**
     * 提交运行SeaTunnel任务
     *
     * @param configFilePath SeaTunnel任务配置文件
     * @param jobName        SeaTunnel任务名称
     * @return sh执行命令
     */
    private String buildRunSeaTunnelCommand(String configFilePath, String jobName) {
        List<String> args = new ArrayList<>();
        args.add(String.format(Constants.RUN_COMMAND, SEATUNNEL_HOME));
        args.add(Constants.CONFIG_OPTIONS);
        args.add(configFilePath);
        args.add(Constants.JOB_NAME);
        args.add(jobName);
        String command = String.join(" ", args);
        return command;
    }

    /**
     * 基于jobId savepoint恢复SeaTunnel任务
     *
     * @param configFilePath SeaTunnel任务配置文件
     * @param jobName        SeaTunnel任务名称
     * @param jobId          SeaTunnel任务ID
     * @return sh执行命令
     */
    private String buildRestoreSeaTunnelCommand(String configFilePath, String jobName, String jobId) {
        List<String> args = new ArrayList<>();
        args.add(String.format(Constants.RUN_COMMAND, SEATUNNEL_HOME));
        args.add(Constants.CONFIG_OPTIONS);
        args.add(configFilePath);
        args.add(Constants.JOB_NAME);
        args.add(jobName);
        args.add(Constants.RESTORE_JOB);
        args.add(jobId);
        String command = String.join(" ", args);
        return command;
    }


}
