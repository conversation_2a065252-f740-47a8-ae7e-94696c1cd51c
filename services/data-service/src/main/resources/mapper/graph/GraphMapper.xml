<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.graph.mapper.GraphMapper">

    <select id="insertGraphNode" parameterType="com.datalink.fdop.graph.api.domain.graph.GraphNode" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:f_g_c_etl_node ${@com.datalink.fdop.graph.utils.DomainAgeUtils@getGraphNodeAgeStr(graphNode)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createTransformNodeAndGraphEdge" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (etl:f_g_c_etl_node), (graph:f_g_c_etl_node)
                                   WHERE etl.nodeId = '${transformId}' AND graph.nodeId = '${nodeId}'
                                   CREATE (etl) -[:f_g_c_etl_node_node_edge
                                   {startTable:'f_g_c_etl_node', endTable:'f_g_c_etl_node', isGraph: true }]->(graph)-[:f_g_c_etl_node_node_edge
            {startTable:'f_g_c_etl_node', endTable:'f_g_c_etl_node', isGraph: true }
            ]->(etl)
            RETURN id(etl), properties(etl)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateProjectionNode" parameterType="com.datalink.fdop.graph.api.domain.graph.ProjectionNode"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="preNodeId != null and preNodeId != ''">node.preNodeId = '${preNodeId}',</if>
            <if test="filter != null and filter != ''">node.filter = '${filter}',</if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteProjectionNodeEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (source:f_g_c_etl_node) -[edge:f_g_c_etl_node_node_edge]-(target:f_g_c_etl_node)
            WHERE source.nodeId = '${sourceNodeId}' AND target.nodeId = '${targetNodeId}' AND edge.isEtl = true
            DELETE edge
            RETURN id(edge), properties(edge)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createProjectionNodeEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (source:f_g_c_etl_node), (target:f_g_c_etl_node)
                                   WHERE source.nodeId = '${sourceNodeId}' AND target.nodeId = '${targetNodeId}'
                                   CREATE (source) -[edge:f_g_c_etl_node_node_edge {startTable:'f_g_c_etl_node',
                               endTable:'f_g_c_etl_node', sideId:'${sideId}', isSubNode: true, isEtl: true }]->(target)-
        [:f_g_c_etl_node_node_edge {startTable:'f_g_c_etl_node',
        endTable:'f_g_c_etl_node', sideId:'${sideId}', isSubNode: false, isEtl: true }]->(source)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchDeleteNode" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId IN
        <foreach collection="nodeIdList" item="nodeId" open="[" separator="," close="]">
            '${nodeId}'
        </foreach>
        DETACH DELETE node
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype);
    </select>

    <select id="batchDeleteNodeAndField" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)-[edge]->(field:f_g_c_etl_field)
        WHERE node.nodeId IN
        <foreach collection="nodeIdList" item="nodeId" open="[" separator="," close="]">
            '${nodeId}'
        </foreach>
        DETACH DELETE node,field
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype);
    </select>

    <select id="createNodeField" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',$$
        <foreach collection="fieldList" item="field" index="i">
            CREATE (field${i}:f_g_c_etl_field
            ${@com.datalink.fdop.graph.utils.DomainAgeUtils@getGraphNodeFieldAgeStr(field)})
        </foreach>
        RETURN id(field0), properties(field0)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateUnionNode" parameterType="com.datalink.fdop.graph.api.domain.graph.UnionNode"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="unionType != null">node.unionType = '${unionType}',</if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createUnionFieldEdge" resultType="int">
        <foreach collection="unionFieldDtoList" item="unionFieldDto" separator=";">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (preField:f_g_c_etl_field), (field:f_g_c_etl_field)
            WHERE preField.fieldId IN
            <foreach collection="unionFieldDto.fieldIdList" item="preFieldId" open="[" separator="," close="]">
                '${preFieldId}'
            </foreach>
            AND field.fieldId = '${unionFieldDto.fieldId}'
            CREATE (preField) -[:f_g_c_etl_field_field_edge
            {startTable: 'f_g_c_etl_field', endTable: 'f_g_c_etl_field', isSubField: true }]->(field)
            -[:f_g_c_etl_field_field_edge {startTable: 'f_g_c_etl_field' , endTable: 'f_g_c_etl_field' , isSubField:
            false }]->(preField)
            RETURN id(preField), properties(preField)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </foreach>
    </select>

    <select id="createPreNodeFieldAndFieldEdge" resultType="int">
        <foreach collection="fieldList" item="field" separator=";">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (preField:f_g_c_etl_field) , (field:f_g_c_etl_field)
            WHERE preField.fieldId = '${field.citeFieldId}' AND field.fieldId = '${field.fieldId}'
            CREATE (preField) -[:f_g_c_etl_field_field_edge
            {startTable: 'f_g_c_etl_field' , endTable: 'f_g_c_etl_field' , isSubField: true }]->(field)
            -[:f_g_c_etl_field_field_edge
            {startTable: 'f_g_c_etl_field' , endTable: 'f_g_c_etl_field' , isSubField: false }]->(preField)
            RETURN id(preField), properties(preField)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </foreach>
    </select>

    <select id="updateJoinNode" parameterType="com.datalink.fdop.graph.api.domain.graph.JoinNode" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="joinType != null">node.joinType = '${joinType}',</if>
            <if test="leftNodeId != null and leftNodeId != ''">node.leftNodeId = '${leftNodeId}',</if>
            <if test="rightNodeId != null and rightNodeId != ''">node.rightNodeId = '${rightNodeId}',</if>
            <if test="joinFieldRelations != null and joinFieldRelations.size() != 0">
                node.joinFieldRelations =
                <foreach collection="joinFieldRelations" item="joinFieldRelation" open="[" separator="," close="]">
                    ${joinFieldRelation.ageStr}
                </foreach>
                ,
            </if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateAggergationNodeNode" parameterType="com.datalink.fdop.graph.api.domain.graph.AggergationNode"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="filter != null and filter != ''">node.filter = '${filter}',</if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateRankNode" parameterType="com.datalink.fdop.graph.api.domain.graph.RankNode"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="filter != null and filter != ''">node.filter = '${filter}',</if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateOutputNode" parameterType="com.datalink.fdop.graph.api.domain.graph.OutputNode"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="filter != null and filter != ''">node.filter = '${filter}',</if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateGraphField" parameterType="com.datalink.fdop.graph.api.domain.graph.GraphField"
            resultType="int">
        <foreach collection="fieldList" item="field" separator=";">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (field:f_g_c_etl_field)
            WHERE field.fieldId = '${field.fieldId}'
            <set>
                <if test="field.preNodeName != null and field.preNodeName != ''
                and field.operType.name == 'UPDATE'.toString() and field.warnType.name == 'ERROR'.toString()">
                    field.preNodeName =
                    '${field.preNodeName}',
                </if>
                <if test="field.preFieldName != null and field.preFieldName != ''
                and field.operType.name == 'UPDATE'.toString() and field.warnType.name == 'ERROR'.toString()">
                    field.preFieldName =
                    '${field.preFieldName}',
                </if>
                <if test="field.fieldSource != null">field.fieldSource = '${field.fieldSource}',</if>
                <if test="field.calcFunction != null and field.calcFunction != ''">field.calcFunction =
                    '${field.calcFunction}',
                </if>
                <if test="field.fieldName != null and field.fieldName != ''">field.fieldName = '${field.fieldName}',
                </if>
                <if test="field.fieldDesc != null and field.fieldDesc != ''">field.fieldDesc = '${field.fieldDesc}',
                </if>
                <if test="field.fieldType != null">field.fieldType = '${field.fieldType}',</if>
                <if test="field.length != null">field.length = ${field.length},</if>
                <if test="field.decimalLength != null">field.decimalLength = ${field.decimalLength},</if>
                <if test="field.seq != null">field.seq = ${field.seq},</if>
                <if test="field.warnType != null">field.warnType = '${field.warnType}',</if>
                <if test="field.unionFieldList != null and field.unionFieldList.size() != 0">
                    field.unionFieldList =
                    <foreach collection="field.unionFieldList" item="unionField" open="[" separator="," close="]">
                        ${unionField.ageStr}
                    </foreach>
                    ,
                </if>
                <if test="field.aggFunction != null">field.aggFunction = '${field.aggFunction}',</if>
                <if test="field.rankString != null and field.rankString != ''">
                    field.rankString = '${field.rankString}',
                </if>
            </set>
            RETURN id(field), properties(field)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </foreach>
    </select>

    <select id="selectProjectionNode" resultType="com.datalink.fdop.graph.api.domain.graph.ProjectionNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.warnType, node.graphNodeType,
                               node.preNodeId, node.filter
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT,graphNodeType TEXT,
                                   preNodeId TEXT,filter TEXT)
    </select>

    <resultMap id="gatherField" type="com.datalink.fdop.graph.api.domain.graph.GraphField">
        <id property="fieldId" column="fieldId"/>
        <result property="preNodeCode" column="preNodeCode"/>
        <result property="preNodeName" column="preNodeName"/>
        <result property="fieldSource" column="fieldSource"/>
        <result property="calcFunction" column="calcFunction"/>
        <result property="fieldName" column="fieldName"/>
        <result property="fieldDesc" column="fieldDesc"/>
        <result property="fieldType" column="fieldType"/>
        <result property="length" column="length"/>
        <result property="decimalLength" column="decimalLength"/>
        <result property="isPk" column="isPk"/>
        <result property="warnType" column="warnType"/>
        <result property="unionFieldList" column="unionFieldList"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
        <result property="aggFunction" column="aggFunction"/>
        <result property="rank" column="rank"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
        <result property="rankString" column="rankString"/>
    </resultMap>

    <select id="selectAllGraphFieldList" resultMap="gatherField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)
            WHERE node.nodeId = '${nodeId}'
            RETURN field.fieldId, field.preNodeCode,field.preNodeName, field.preFieldName, field.fieldSource, field.calcFunction,
                               field.fieldName, field.fieldDesc,
                               field.fieldType, field.length,
                               field.decimalLength, field.isPk, field.seq, field.warnType, field.unionFieldList,
                               field.aggFunction,
                               field.rankString ORDER BY field.seq ASC
                                   $$) as (fieldId TEXT,preNodeCode TEXT,preNodeName TEXT,preFieldName TEXT,fieldSource TEXT,calcFunction TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
            BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT,unionFieldList VARCHAR,aggFunction TEXT,rankString TEXT)
    </select>

    <select id="selectProjectionNodeFieldList" resultType="com.datalink.fdop.graph.api.domain.graph.GraphField">
            SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH
            (node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(citeField:f_g_c_etl_field)-[fieldNodeEdge]->(citeNode:f_g_c_etl_node)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false AND
            fieldNodeEdge.isFatherNode = true
            RETURN field.fieldSource, field.fieldId,field.calcFunction, field.fieldName, field.fieldDesc,
            field.fieldType, field.length,
            field.decimalLength, field.isPk, field.seq, field.warnType, citeNode.nodeId as citeNodeId,
            citeField.fieldId as citeFieldId,
            citeNode.nodeCode as preNodeCode,citeNode.nodeName as preNodeName, citeField.fieldName as preFieldName
            ORDER BY field.seq ASC
            $$) as (fieldSource TEXT,fieldId TEXT,calcFunction TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
            BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT,citeNodeId TEXT,citeFieldId TEXT,
            preNodeCode TEXT,preNodeName TEXT,preFieldName TEXT)
    </select>

    <resultMap id="join" type="com.datalink.fdop.graph.api.domain.graph.JoinNode">
        <id property="nodeId" column="nodeId"/>
        <result property="nodeCode" column="nodeCode"/>
        <result property="nodeName" column="nodeName"/>
        <result property="frontData" column="frontData"/>
        <result property="warnType" column="warnType"/>
        <result property="graphNodeType" column="graphNodeType"/>
        <result property="joinType" column="joinType"/>
        <result property="leftNodeId" column="leftNodeId"/>
        <result property="rightNodeId" column="rightNodeId"/>
        <result property="joinFieldRelations" column="joinFieldRelations"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="selectJoinNode" resultMap="join">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.warnType, node.graphNodeType,
                               node.joinType, node.leftNodeId, node.rightNodeId, node.joinFieldRelations
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT,graphNodeType TEXT,
                                   joinType TEXT, leftNodeId TEXT,rightNodeId TEXT,joinFieldRelations VARCHAR)
    </select>

    <select id="selectJoinNodeFieldList" resultType="com.datalink.fdop.graph.api.domain.graph.GraphField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(citeField:f_g_c_etl_field)-[fieldNodeEdge]->(citeNode:f_g_c_etl_node)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false AND fieldNodeEdge.isFatherNode = true
            RETURN field.fieldSource, field.fieldId, field.calcFunction, field.fieldName, field.fieldDesc,
                               field.fieldType, field.length,
                               field.decimalLength, field.isPk, field.seq, field.warnType,
                               citeNode.nodeId as citeNodeId,
                               citeField.fieldId as citeFieldId,
                               citeNode.nodeCode as preNodeCode, citeNode.nodeName as preNodeName, citeField.fieldName as preFieldName
            ORDER BY field.seq ASC
            $$) as (fieldSource TEXT,fieldId TEXT,calcFunction TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
            BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT,citeNodeId TEXT,citeFieldId TEXT,preNodeCode TEXT,preNodeName TEXT,preFieldName TEXT)
    </select>

    <select id="selectUnionNode" resultType="com.datalink.fdop.graph.api.domain.graph.UnionNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.warnType, node.graphNodeType,
                               node.unionType
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT,graphNodeType TEXT,
                                   unionType TEXT)
    </select>

    <resultMap id="unionField" type="com.datalink.fdop.graph.api.domain.graph.GraphField">
        <id property="fieldId" column="fieldId"/>
        <result property="fieldSource" column="fieldSource"/>
        <result property="calcFunction" column="calcFunction"/>
        <result property="fieldName" column="fieldName"/>
        <result property="fieldDesc" column="fieldDesc"/>
        <result property="fieldType" column="fieldType"/>
        <result property="length" column="length"/>
        <result property="decimalLength" column="decimalLength"/>
        <result property="isPk" column="isPk"/>
        <result property="seq" column="seq"/>
        <result property="citeNodeId" column="citeNodeId"/>
        <result property="citeFieldId" column="citeFieldId"/>
        <result property="preNodeName" column="preNodeName"/>
        <result property="preFieldName" column="preFieldName"/>
        <result property="unionFieldList" column="unionFieldList"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="selectUnionNodeFieldList" resultMap="unionField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(citeField:f_g_c_etl_field)-[fieldNodeEdge]->(citeNode:f_g_c_etl_node)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false AND fieldNodeEdge.isFatherNode = true
            RETURN field.fieldSource, field.fieldId, field.calcFunction, field.fieldName, field.fieldDesc,
                               field.fieldType, field.length,
                               field.decimalLength, field.isPk, field.seq, citeNode.nodeId as citeNodeId,
                               citeField.fieldId as citeFieldId,
                               citeNode.nodeName as preNodeName, citeField.fieldName as preFieldName,
                               field.unionFieldList ORDER BY field.seq ASC
                                   $$) as (fieldSource TEXT,fieldId TEXT,calcFunction TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
            BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,citeNodeId TEXT,citeFieldId TEXT,preNodeName TEXT,preFieldName TEXT,unionFieldList VARCHAR)
    </select>

    <select id="selectAggregationNode" resultType="com.datalink.fdop.graph.api.domain.graph.AggergationNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.warnType, node.graphNodeType,
                               node.filter
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT,graphNodeType TEXT,
        filter TEXT)
    </select>

    <select id="selectAggregationNodeFieldList" resultType="com.datalink.fdop.graph.api.domain.graph.GraphField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(citeField:f_g_c_etl_field)-[fieldNodeEdge]->(citeNode:f_g_c_etl_node)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false AND fieldNodeEdge.isFatherNode = true
            RETURN field.fieldSource, field.fieldId, field.calcFunction, field.fieldName, field.fieldDesc,
                               field.fieldType, field.length,
                               field.decimalLength, field.isPk, field.seq, field.warnType,
                               citeNode.nodeId as citeNodeId,
                               citeField.fieldId as citeFieldId,
                               citeNode.nodeCode as preNodeCode, citeNode.nodeName as preNodeName,
                               citeField.fieldName as preFieldName,
                               field.aggFunction ORDER BY field.seq ASC
                                   $$) as (fieldSource TEXT,fieldId TEXT,calcFunction TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
            BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT,citeNodeId TEXT,citeFieldId TEXT,preNodeCode TEXT,preNodeName TEXT,preFieldName TEXT,aggFunction TEXT)
    </select>

    <select id="selectRankNode" resultType="com.datalink.fdop.graph.api.domain.graph.RankNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.warnType, node.graphNodeType,
                               node.filter
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT,graphNodeType TEXT,
        filter TEXT)
    </select>

    <resultMap id="rankField" type="com.datalink.fdop.graph.api.domain.graph.GraphField">
        <id property="fieldId" column="fieldId"/>
        <result property="fieldSource" column="fieldSource"/>
        <result property="calcFunction" column="calcFunction"/>
        <result property="fieldName" column="fieldName"/>
        <result property="fieldDesc" column="fieldDesc"/>
        <result property="fieldType" column="fieldType"/>
        <result property="length" column="length"/>
        <result property="decimalLength" column="decimalLength"/>
        <result property="isPk" column="isPk"/>
        <result property="seq" column="seq"/>
        <result property="citeNodeId" column="citeNodeId"/>
        <result property="citeFieldId" column="citeFieldId"/>
        <result property="preNodeName" column="preNodeName"/>
        <result property="preFieldName" column="preFieldName"/>
        <result property="rank" column="rank"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
        <result property="rankString" column="rankString"/>
    </resultMap>

    <select id="selectRankNodeFieldList" resultMap="rankField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(citeField:f_g_c_etl_field)-[fieldNodeEdge]->(citeNode:f_g_c_etl_node)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false AND fieldNodeEdge.isFatherNode = true
            RETURN field.fieldSource, field.fieldId, field.calcFunction, field.fieldName, field.fieldDesc,
                               field.fieldType, field.length,
                               field.decimalLength, field.isPk, field.seq, field.warnType,
                               citeNode.nodeId as citeNodeId,
                               citeField.fieldId as citeFieldId,
                               citeNode.nodeCode as preNodeCode, citeNode.nodeName as preNodeName,
                               citeField.fieldName as preFieldName,
                               field.rankString ORDER BY field.seq ASC
                                   $$) as (fieldSource TEXT,fieldId TEXT,calcFunction TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
            BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT,citeNodeId TEXT,citeFieldId TEXT,preNodeCode TEXT,preNodeName TEXT,preFieldName TEXT,rankString VARCHAR)
    </select>

    <select id="selectOutputNode" resultType="com.datalink.fdop.graph.api.domain.graph.OutputNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.warnType, node.graphNodeType, node.filter
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT,graphNodeType TEXT,filter TEXT)
    </select>

    <select id="selectOutputNodeFieldList" resultMap="rankField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(citeField:f_g_c_etl_field)-[fieldNodeEdge]->(citeNode:f_g_c_etl_node)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false AND fieldNodeEdge.isFatherNode = true
            RETURN field.fieldSource, field.fieldId, field.calcFunction, field.fieldName, field.fieldDesc,
                               field.fieldType, field.length,
                               field.decimalLength, field.isPk, field.seq, field.warnType,
                               citeNode.nodeId as citeNodeId,
                               citeField.fieldId as citeFieldId,
                               citeNode.nodeCode as preNodeCode, citeNode.nodeName as preNodeName, citeField.fieldName as preFieldName
                               ORDER BY field.seq ASC
                                   $$) as (fieldSource TEXT,fieldId TEXT,calcFunction TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
            BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT,citeNodeId TEXT,citeFieldId TEXT,preNodeCode TEXT,preNodeName TEXT,preFieldName TEXT)
    </select>

    <select id="selectGraphNodeList" resultType="com.datalink.fdop.graph.api.domain.graph.GraphNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (etl:f_g_c_etl_node) -[edge:f_g_c_etl_node_node_edge]->(graph:f_g_c_etl_node)
            WHERE etl.nodeId = '${transfromId}' AND edge.isGraph = true
            RETURN graph.nodeId, graph.nodeCode, graph.nodeName, graph.graphNodeType, graph.frontData, graph.warnType
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,graphNodeType TEXT,frontData TEXT,warnType TEXT)
    </select>

    <select id="selectSourceGraphNodeEdgeList" resultType="com.datalink.fdop.graph.api.domain.graph.GraphNodeEdge">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (source :f_g_c_etl_node) -[edge:f_g_c_etl_node_node_edge]->(target:f_g_c_etl_node)
            WHERE source.nodeId = '${nodeId}' AND edge.isSubNode = true AND edge.isEtl = true
            RETURN edge.sideId, source.nodeId as sourceNodeId, target.nodeId as targetNodeId, edge.frontData
                                   $$) as (sideId TEXT,sourceNodeId TEXT,targetNodeId TEXT,frontData TEXT)
    </select>

    <select id="selectGraphNodeEdgeListByGraphNodeId" resultType="com.datalink.fdop.graph.api.domain.graph.GraphNodeEdge">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (source :f_g_c_etl_node) -[edge:f_g_c_etl_node_node_edge]->(target:f_g_c_etl_node)
            WHERE (source.nodeId = '${nodeId}' OR target.nodeId = '${nodeId}') AND edge.isSubNode = true
            RETURN edge.sideId, source.nodeId as sourceNodeId, target.nodeId as targetNodeId, edge.frontData
                                   $$) as (sideId TEXT,sourceNodeId TEXT,targetNodeId TEXT,frontData TEXT)
    </select>

    <select id="selectGraphNodeEdgeList" resultType="com.datalink.fdop.graph.api.domain.graph.GraphNodeEdge">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (source :f_g_c_etl_node) -[edge:f_g_c_etl_node_node_edge]->(target:f_g_c_etl_node)
            WHERE source.nodeId = '${nodeId}' AND edge.isSubNode = true
            RETURN edge.sideId, source.nodeId as sourceNodeId, target.nodeId as targetNodeId, edge.frontData,
                               edge.warnType
                                   $$) as (sideId TEXT,sourceNodeId TEXT,targetNodeId TEXT,frontData TEXT,warnType TEXT)
    </select>

    <select id="selectGraphNodeFieldIdList" resultType="String">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node :f_g_c_etl_node) -[edge]->(field:f_g_c_etl_field)
            WHERE source.nodeId = '${nodeId}'
            RETURN field.fieldId
            $$) as (fieldId TEXT)
    </select>

    <select id="selectPreNodes" resultType="String">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (source :f_g_c_etl_node) -[edge:f_g_c_etl_node_node_edge]->(target:f_g_c_etl_node)
        WHERE target.nodeId IN
        <foreach collection="etlNodeIds" item="nodeId" open="[" separator="," close="]">
            '${nodeId}'
        </foreach>
        RETURN source.nodeId
        $$) as (nodeId TEXT)
    </select>

    <select id="selectSubFieldList" resultType="com.datalink.fdop.graph.api.domain.graph.GraphField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(preField:f_g_c_etl_field)
        WHERE node.nodeId = '${nodeId}'
        <if test="fieldIdss != null and fieldIdss.size() > 0">
            AND
            <foreach collection="fieldIdss" item="fieldIds" index="" open="(" separator="OR" close=")">
                field.fieldId IN
                <foreach collection="fieldIds" item="fieldId" index="" open="[" separator="," close="]">
                    '${fieldId}'
                </foreach>
            </foreach>
        </if>
        AND edge.isSubField = true
        RETURN preField.fieldSource, preField.fieldId,preField.calcFunction, preField.fieldName, preField.fieldDesc,
        preField.fieldType, preField.length,
        preField.decimalLength, preField.isPk, preField.seq, preField.warnType, node.nodeId as citeNodeId,
        field.fieldId as citeFieldId,
        node.nodeName as preNodeName, field.fieldName as preFieldName,preField.aggFunction
        ORDER BY preField.seq ASC
        $$) as (fieldSource TEXT,fieldId TEXT,calcFunction TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
        BIGINT,
        decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT,citeNodeId TEXT,citeFieldId TEXT,preNodeName
        TEXT,preFieldName
        TEXT,aggFunction TEXT)
    </select>

    <select id="selectSubProjectionNodeList" resultType="com.datalink.fdop.graph.api.domain.graph.ProjectionNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (sourceNode:f_g_c_etl_node) -[]->(graphNode:f_g_c_etl_node)-[]->(targetNode:f_g_c_etl_node)
            WHERE sourceNode.nodeId = '${sourceNodeId}' AND targetNode.nodeId = '${targetNodeId}'
            RETURN graphNode.nodeId, graphNode.nodeCode, graphNode.nodeName, graphNode.frontData, graphNode.warnType, graphNode.graphNodeType,
                               graphNode.preNodeId, graphNode.filter
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT,graphNodeType TEXT,
                                   preNodeId TEXT,filter TEXT)
    </select>

    <select id="selectEtlNodeByGraphNodeId" resultType="String">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (graphNode:f_g_c_etl_node) -[edge]->(etlNode:f_g_c_etl_node)
            WHERE graphNode.nodeId = '${nodeId}' AND edge.isGraph = true
            RETURN etlNode.nodeId $$) as (nodeId TEXT)
    </select>

</mapper> 