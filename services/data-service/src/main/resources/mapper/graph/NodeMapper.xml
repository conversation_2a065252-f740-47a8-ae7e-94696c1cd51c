<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.graph.mapper.NodeMapper">

    <select id="checkNodeCode" resultType="com.datalink.fdop.graph.api.domain.Node">
        select *
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (task:t_ds_task_definition) -[edge:f_g_c_etl_node_task_edge]->(node:f_g_c_etl_node)
        WHERE task.code = ${taskCode} AND node.nodeCode = '${nodeCode}'
        <if test="nodeId != null">AND node.nodeId &lt;&gt; '${nodeId}'</if>
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.warnType
        $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT)
    </select>

    <select id="checkNodeName" resultType="com.datalink.fdop.graph.api.domain.Node">
        select *
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (task:t_ds_task_definition) -[edge:f_g_c_etl_node_task_edge]->(node:f_g_c_etl_node)
        WHERE task.code = ${taskCode} AND node.nodeName = '${nodeName}'
        <if test="nodeId != null">AND node.nodeId &lt;&gt; '${nodeId}'</if>
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.warnType
        $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,warnType TEXT)
    </select>

    <select id="createNodeAndTaskEdge" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (task:t_ds_task_definition), (node:f_g_c_etl_node)
                                   WHERE task.code = ${taskCode} AND node.nodeId = '${nodeId}'
                                   CREATE (node) -[:f_g_c_etl_node_task_edge
                                   {startTable:'f_g_c_etl_node', endTable:'t_ds_task_definition' }]->(task)-[:f_g_c_etl_node_task_edge
            {startTable:'t_ds_task_definition', endTable:'f_g_c_etl_node' }
            ]->(node)
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createNodeEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (source:f_g_c_etl_node), (target:f_g_c_etl_node)
        WHERE source.nodeId = '${sourceNodeId}' AND target.nodeId = '${targetNodeId}'
        CREATE (source) -[edge:f_g_c_etl_node_node_edge {startTable:'f_g_c_etl_node',
        endTable:'f_g_c_etl_node', sideId:'${sideId}', isSubNode: true
        <if test="frontData != null and frontData != ''">, frontData: '${frontData}'</if>
        }]->(target)-
        [:f_g_c_etl_node_node_edge {startTable:'f_g_c_etl_node',
        endTable:'f_g_c_etl_node', sideId:'${sideId}', isSubNode: false }]->(source)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createNodeFieldEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node),(field:f_g_c_etl_field)
        WHERE node.nodeId = '${nodeId}'
        <if test="fieldIdss != null and fieldIdss.size() > 0">
            AND
            <foreach collection="fieldIdss" item="fieldIds" index="" open="(" separator="OR" close=")">
                field.fieldId IN
                <foreach collection="fieldIds" item="fieldId" index="" open="[" separator="," close="]">
                    '${fieldId}'
                </foreach>
            </foreach>
        </if>
        CREATE (node) -[:f_g_c_etl_node_field_edge
        {startTable:'f_g_c_etl_node', endTable:'f_g_c_etl_field', isSubField: true }]->(field)
        -[:f_g_c_etl_node_field_edge
        {startTable:'f_g_c_etl_field', endTable:'f_g_c_etl_node', isFatherNode: true }]->(node)
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteNode" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        DETACH DELETE node
        RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchDeleteNode" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node) WHERE node.nodeId IN
        <foreach collection="nodeIdList" item="nodeId" open="[" separator="," close="]">
            '${nodeId}'
        </foreach>
        DETACH DELETE node
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteNodeEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (source:f_g_c_etl_node) -[edge:f_g_c_etl_node_node_edge]-(target:f_g_c_etl_node)
            WHERE source.nodeId = '${sourceNodeId}' AND target.nodeId = '${targetNodeId}'
            DELETE edge
            RETURN id(edge), properties(edge)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteNodeField" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[edge]->(field:f_g_c_etl_field)
            WHERE node.nodeId = '${nodeId}'
            DETACH DELETE field
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchDeleteNodeFieldEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (citeField:f_g_c_etl_field) -[edge1]->(field:f_g_c_etl_field)-[edge2]->(citeField)
        WHERE edge1.isSubField = true AND edge2.isSubField = false
        <if test="fieldIdss != null and fieldIdss.size() > 0">
            AND
            <foreach collection="fieldIdss" item="fieldIds" index="" open="(" separator="OR" close=")">
                field.fieldId IN
                <foreach collection="fieldIds" item="fieldId" index="" open="[" separator="," close="]">
                    '${fieldId}'
                </foreach>
            </foreach>
        </if>
        DELETE edge1,edge2
        RETURN id(field), properties(field)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchDeleteNodeField" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)-[edge]->(field:f_g_c_etl_field)
        WHERE node.nodeId IN
        <foreach collection="nodeIdList" item="nodeId" open="[" separator="," close="]">
            '${nodeId}'
        </foreach>
        DETACH DELETE field
        RETURN id(field), properties(field)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype);
    </select>

    <select id="batchDeleteField" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:f_g_c_etl_field) WHERE
        <foreach collection="fieldIdss" item="fieldIds" index="" open="(" separator="OR" close=")">
            field.fieldId IN
            <foreach collection="fieldIds" item="fieldId" index="" open="[" separator="," close="]">
                '${fieldId}'
            </foreach>
        </foreach>
        DETACH DELETE field
        RETURN id(field), properties(field)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectEtlNodeById" resultType="com.datalink.fdop.graph.api.domain.etl.EtlNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
           RETURN node.nodeId, node.nodeCode, node.nodeName, node.nodeType, node.frontData, node.warnType
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,nodeType TEXT,frontData TEXT,warnType TEXT)
    </select>

    <select id="selectGraphNodeById" resultType="com.datalink.fdop.graph.api.domain.graph.GraphNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
           RETURN node.nodeId, node.nodeCode, node.nodeName, node.graphNodeType, node.frontData, node.warnType
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,graphNodeType TEXT,frontData TEXT,warnType TEXT)
    </select>

    <select id="updateNodeWarnType" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
            SET node.warnType = '${warnType}'
           RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateFieldWarnType" resultType="int">
        <foreach collection="fieldWarnList" item="fieldWarn" separator=";">
            <if test="fieldWarn.warnType.name() == 'WARN'.toString()">
                SELECT count(1)
                FROM ag_catalog.cypher('zjdata_graph', $$
                MATCH (field:f_g_c_etl_field)
                WHERE field.fieldId = '${fieldWarn.fieldId}' AND field.warnType &lt;&gt; 'ERROR'
                <if test="fieldWarn.isSyn == true">
                    SET field.warnType = 'NORMAL'
                    ,field.fieldType = '${fieldWarn.fieldType}'
                    ,field.length = '${fieldWarn.length}'
                    ,field.decimalLength = '${fieldWarn.decimalLength}'
                </if>
                <if test="fieldWarn.isSyn == false">
                    SET field.warnType = '${fieldWarn.warnType}'
                </if>
                RETURN id(field), properties(field)
                $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
            </if>
            <if test="fieldWarn.warnType.name() == 'ERROR'.toString()">
                <if test="fieldWarn.isSyn == true">
                    SELECT count(1)
                    FROM ag_catalog.cypher('zjdata_graph', $$
                    MATCH (field:f_g_c_etl_field)
                    WHERE field.fieldId = '${fieldWarn.fieldId}'
                    DETACH DELETE field
                    RETURN id(field), properties(field)
                    $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
                </if>
                <if test="fieldWarn.isSyn == false">
                    SELECT count(1)
                    FROM ag_catalog.cypher('zjdata_graph', $$
                    MATCH (field:f_g_c_etl_field)
                    WHERE field.fieldId = '${fieldWarn.fieldId}'
                    SET field.preNodeName = '${fieldWarn.preNodeName}',
                    field.preFieldName = '${fieldWarn.preFieldName}',
                    field.warnType = '${fieldWarn.warnType}'
                    RETURN id(field), properties(field)
                    $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
                </if>
            </if>
        </foreach>
    </select>

    <select id="selectSubNodeEdgeWarnType" resultType="com.datalink.fdop.graph.api.enums.WarnType">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (sourceNode:f_g_c_etl_node) -[edge]->(targetNode:f_g_c_etl_node)
            WHERE sourceNode.nodeId = '${sourceNodeId}' AND targetNode.nodeId = '${targetNodeId}' AND edge.isSubNode = true
            RETURN edge.warnType
            $$) as (warnType TEXT)
    </select>

    <select id="updateNodeEdgeWarnType" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (sourceNode:f_g_c_etl_node) -[edge]->(targetNode:f_g_c_etl_node)
            WHERE sourceNode.nodeId = '${sourceNodeId}' AND targetNode.nodeId = '${targetNodeId}' AND edge.isSubNode = true
            SET edge.warnType = '${warnType}' RETURN id(edge), properties(edge)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateCurrentNodeEdgeWarnType" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (sourceNode:f_g_c_etl_node) -[edge]->(targetNode:f_g_c_etl_node)
        WHERE targetNode.nodeId = '${nodeId}' AND edge.isSubNode = true
        <if test="preNodeId != null and preNodeId != ''">
            AND sourceNode.nodeId = '${preNodeId}'
        </if>
        SET edge.warnType = '${warnType}' RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchUpdateNodeEdgeWarnType" resultType="int">
        <foreach collection="nodeWarnTypeList" item="nodeWarnType" separator=";">
            SELECT count(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (sourceNode:f_g_c_etl_node) -[edge]->(targetNode:f_g_c_etl_node)
            WHERE sourceNode.nodeId = '${nodeWarnType.nodeId}' AND targetNode.nodeId = '${nodeId}' AND edge.isSubNode =
            true
            SET edge.warnType = '${nodeWarnType.warnType}'
            RETURN id(edge), properties(edge)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </foreach>
    </select>

    <select id="selectNodeWarnType" resultType="com.datalink.fdop.graph.api.enums.WarnType">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
            RETURN node.warnType
            $$) as (warnType TEXT)
    </select>

    <select id="batchUpdateFieldWarnType" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:f_g_c_etl_field)
        WHERE
        <foreach collection="fieldIdss" item="fieldIds" index="" open="(" separator="OR" close=")">
            field.fieldId IN
            <foreach collection="fieldIds" item="fieldId" index="" open="[" separator="," close="]">
                '${fieldId}'
            </foreach>
        </foreach>
        SET field.warnType = '${warnType}'
        RETURN id(field), properties(field)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper> 