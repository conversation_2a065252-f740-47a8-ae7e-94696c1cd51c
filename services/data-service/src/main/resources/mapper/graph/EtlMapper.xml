<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.graph.mapper.EtlMapper">

    <select id="insertEtlNode" parameterType="com.datalink.fdop.graph.api.domain.etl.EtlNode" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:f_g_c_etl_node ${@com.datalink.fdop.graph.utils.DomainAgeUtils@getEtlNodeAgeStr(etlNode)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateSourceNode" parameterType="com.datalink.fdop.graph.api.domain.etl.SourceNode" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="sourceType != null">node.sourceType = '${sourceType}',</if>
            <if test="dataSource != null">node.dataSource = ${dataSource.ageStr},</if>
            <if test="dataEntity != null">node.dataEntity = ${dataEntity.ageStr},</if>
            <if test="sourceParamId != null">node.sourceParamId = ${sourceParamId},</if>
            <if test="dataFilter != null">node.dataFilter = '${dataFilter}',</if>
            <if test="runtimeMode != null">node.runtimeMode = '${runtimeMode}',</if>
        </set>
        <if test="sourceType != null">
            <if test="sourceType.name() == 'DATASOURCE'.toString()">remove node.dataEntity</if>
            <if test="sourceType.name() == 'DATAENTITY'.toString()">remove node.dataSource</if>
        </if>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteNodeEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[edge:f_g_c_etl_node_entity_edge]-(entity:d_e_data_entity)
            WHERE node.nodeId = '${nodeId}'
            DETACH DELETE edge
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createSourceNodeEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node),(entity:d_e_data_entity)
            WHERE node.nodeId = '${nodeId}' and entity.id = ${dataEntityId}
            CREATE (node)&lt;-[edge:f_g_c_etl_node_entity_edge {startTable:'d_e_data_entity',
                               endTable:'f_g_c_etl_node'}]-(entity)
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createSinkNodeEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node),(entity:d_e_data_entity)
                                   WHERE node.nodeId = '${nodeId}'  and entity.id = ${dataEntityId}
                                   CREATE (node)-[edge:f_g_c_etl_node_entity_edge {startTable:'f_g_c_etl_node',
                               endTable:'d_e_data_entity'}]->(entity)
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteNodeTableEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[edge:f_g_c_etl_node_table_edge]-(table:d_g_synchronization_table)
            WHERE node.nodeId = '${nodeId}'
            DETACH DELETE edge
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createNodeTableEdge" resultType="int">
        <if test="databaseName != null and databaseName != ''">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node),(d:datasource) -[edge1]->(database:d_g_synchronization_database)-[edge2]->(schema
            :d_g_synchronization_schema)
            -[edge3]->(table :d_g_synchronization_table)
            WHERE node.nodeId = '${nodeId}' AND d.id = ${dataSourceId} AND database.code = '${databaseName}' AND
            schema.code = '${schemaName}' AND table.code = '${tableName}'
            CREATE (node)-[:f_g_c_etl_node_table_edge {startTable:'f_g_c_etl_node',
            endTable:'d_g_synchronization_table'}]->(table)
            RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
            ag_catalog.agtype)
        </if>
        <if test="databaseName == null or databaseName == ''">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node),(d:datasource) -[edge1]->(schema:d_g_synchronization_schema)
            -[edge2]->(table :d_g_synchronization_table)
            WHERE node.nodeId = '${nodeId}' AND d.id = ${dataSourceId} AND
            schema.code = '${schemaName}' AND table.code = '${tableName}'
            CREATE (node)-[:f_g_c_etl_node_table_edge {startTable:'f_g_c_etl_node',
            endTable:'d_g_synchronization_table'}]->(table)
            RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
            ag_catalog.agtype)
        </if>
    </select>

    <select id="createNodeField" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',$$
        <foreach collection="fieldList" item="field" index="i">
            CREATE (field${i}:f_g_c_etl_field
            ${@com.datalink.fdop.graph.utils.DomainAgeUtils@getEtlNodeFieldAgeStr(field)})
        </foreach>
        RETURN id(field0), properties(field0)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateEtlField" parameterType="com.datalink.fdop.graph.api.graph.etl.EtlField"
            resultType="int">
        <foreach collection="fieldList" item="field" separator=";">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (field:f_g_c_etl_field)
            WHERE field.fieldId = '${field.fieldId}'
            <set>
                <if test="field.preNodeName != null and field.preNodeName != ''
                and field.operType.name == 'UPDATE'.toString() and field.warnType.name == 'ERROR'.toString()">
                    field.preNodeName =
                    '${field.preNodeName}',
                </if>
                <if test="field.preFieldName != null and field.preFieldName != ''
                and field.operType.name == 'UPDATE'.toString() and field.warnType.name == 'ERROR'.toString()">
                    field.preFieldName =
                    '${field.preFieldName}',
                </if>
                <if test="field.fieldName != null and field.fieldName != ''">field.fieldName = '${field.fieldName}',
                </if>
                <if test="field.fieldDesc != null and field.fieldDesc != ''">field.fieldDesc = '${field.fieldDesc}',
                </if>
                <if test="field.fieldType != null">field.fieldType = '${field.fieldType}',</if>
                <if test="field.length != null">field.length = ${field.length},</if>
                <if test="field.decimalLength != null">field.decimalLength = ${field.decimalLength},</if>
                <if test="field.seq != null">field.seq = ${field.seq},</if>
                <if test="field.warnType != null">field.warnType = '${field.warnType}',</if>
            </set>
            RETURN id(field), properties(field)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </foreach>
    </select>


    <select id="createSinkNodeField" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',$$
        <foreach collection="fieldList" item="field" index="i">
            CREATE (field${i}:f_g_c_etl_field
            ${@com.datalink.fdop.graph.utils.DomainAgeUtils@getSinkNodeFieldAgeStr(field)})
        </foreach>
        RETURN id(field0), properties(field0)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateTransformNode" parameterType="com.datalink.fdop.graph.api.domain.etl.TransformNode"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="transformType != null">node.transformType = '${transformType}',</if>
            <if test="sql != null and sql != ''">node.sql = '${sql}',</if>
            <if test="graphNodeId != null and graphNodeId != ''">node.graphNodeId = '${graphNodeId}',</if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <sql id="isEntityField">
        <if test="${isEntityField} == true">
            <if test="field.fieldSourceType.name == 'MAIN_FIELD'.toString">
                'd_e_data_element', seq: ${field.seq}, warnType: 'NORMAL'
            </if>
            <if test="field.fieldSourceType.name == 'INPUT'.toString">
                'd_e_data_element_input', seq: ${field.seq}, warnType: 'NORMAL'
            </if>
            <if test="field.fieldSourceType.name == 'ENTITY'.toString">
                'd_e_data_entity_structure', seq: ${field.seq}, warnType: 'NORMAL'
            </if>
        </if>
        <if test="${isEntityField} == false">
            'f_g_c_etl_field'
        </if>
    </sql>

    <select id="createPreNodeFieldAndFieldEdge" resultType="int">
        <foreach collection="fieldList" item="field" separator=";">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH
            <if test="isSourceEntityField == true">
                <if test="field.fieldSourceType.name == 'MAIN_FIELD'.toString">
                    (preField:d_e_data_element)
                </if>
                <if test="field.fieldSourceType.name == 'INPUT'.toString">
                    (preField:d_e_data_element_input)
                </if>
                <if test="field.fieldSourceType.name == 'ENTITY'.toString">
                    (preField:d_e_data_entity_structure)
                </if>
            </if>
            <if test="isSourceEntityField == false">
                (preField:f_g_c_etl_field)
            </if>
            ,
            <if test="isTargetEntityField == true">
                <if test="field.fieldSourceType.name == 'MAIN_FIELD'.toString">
                    (field:d_e_data_element)
                </if>
                <if test="field.fieldSourceType.name == 'INPUT'.toString">
                    (field:d_e_data_element_input)
                </if>
                <if test="field.fieldSourceType.name == 'ENTITY'.toString">
                    (field:d_e_data_entity_structure)
                </if>
            </if>
            <if test="isTargetEntityField == false">
                (field:f_g_c_etl_field)
            </if>
            WHERE
            <if test="isSourceEntityField == true">
                preField.id = ${field.preFieldId}
            </if>
            <if test="isSourceEntityField == false">
                preField.fieldId = '${field.preFieldId}'
            </if>
            AND
            <if test="isTargetEntityField == true">
                field.id = ${field.fieldId}
            </if>
            <if test="isTargetEntityField == false">
                field.fieldId = '${field.fieldId}'
            </if>
            CREATE (preField) -[:f_g_c_etl_field_field_edge
            {startTable:
            <include refid="isEntityField">
                <property name="isEntityField" value="isSourceEntityField"/>
            </include>
            , endTable:
            <include refid="isEntityField">
                <property name="isEntityField" value="isTargetEntityField"/>
            </include>
            , isSubField: true }]->(field)
            -[:f_g_c_etl_field_field_edge
            {startTable:
            <include refid="isEntityField">
                <property name="isEntityField" value="isTargetEntityField"/>
            </include>
            , endTable:
            <include refid="isEntityField">
                <property name="isEntityField" value="isSourceEntityField"/>
            </include>
            , isSubField: false }]->(preField)
            RETURN id(preField), properties(preField)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </foreach>
    </select>

    <select id="updateSinkNode" parameterType="com.datalink.fdop.graph.api.domain.etl.SinkNode" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="sinkType != null">node.sinkType = '${sinkType}',</if>
            <if test="dataEntity != null">node.dataEntity = ${dataEntity.ageStr},</if>
            <if test="dataEntityQualityList != null and dataEntityQualityList.size() != 0">
                node.dataEntityQualityList =
                <foreach collection="dataEntityQualityList" item="dataEntityQuality" open="[" separator="," close="]">
                    ${dataEntityQuality.ageStr}
                </foreach>
                ,
            </if>
            <if test="preFlag != null">node.preFlag = ${preFlag},</if>
            <if test="preSqlCondition != null and preSqlCondition != ''">node.preSqlCondition = '${preSqlCondition}',
            </if>
            <if test="runtimeMode != null">node.runtimeMode = '${runtimeMode}',</if>
        </set>
        <if test="dataEntityQualityList == null or dataEntityQualityList.size() == 0">
            remove node.dataEntityQualityList
        </if>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateParamsNode" parameterType="com.datalink.fdop.graph.api.domain.etl.ParamsNode" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_g_c_etl_node)
        WHERE node.nodeId = '${nodeId}'
        <set>
            <if test="nodeCode != null and nodeCode != ''">node.nodeCode = '${nodeCode}',</if>
            <if test="nodeName != null and nodeName != ''">node.nodeName = '${nodeName}',</if>
            <if test="frontData != null and frontData != ''">node.frontData = '${frontData}',</if>
            <if test="outputParamType != null">node.outputParamType = '${outputParamType}',</if>
            <if test="outputParamId != null">node.outputParamId = ${outputParamId},</if>
            <if test="paramScope != null">node.paramScope = '${paramScope}',</if>
            <if test="paramHandleType != null">node.paramHandleType = '${paramHandleType}',</if>
            <if test="paramResultType != null">node.paramResultType = '${paramResultType}',</if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="isSourceNode" resultType="Boolean">
        WITH overview as (SELECT nodeType
                          FROM ag_catalog.cypher('zjdata_graph', $$
                              MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${targetNodeId}'
        RETURN node.nodeType
        $$) as (nodeType TEXT)
            )
        select case when nodeType = 'SOURCE' then true else false end
        from overview
    </select>

    <resultMap id="source" type="com.datalink.fdop.graph.api.domain.etl.SourceNode">
        <id property="nodeId" column="nodeId"/>
        <result property="nodeCode" column="nodeCode"/>
        <result property="nodeName" column="nodeName"/>
        <result property="nodeType" column="nodeType"/>
        <result property="sourceType" column="sourceType"/>
        <result property="dataSource" column="dataSource"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
        <result property="dataEntity" column="dataEntity"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
        <result property="sourceParamId" column="sourceParamId"/>
        <result property="dataFilter" column="dataFilter"/>
        <result property="runtimeMode" column="runtimeMode"/>
    </resultMap>

    <select id="selectSourceNode" resultMap="source">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.frontData, node.nodeType, node.sourceType,
                               node.dataSource, node.dataEntity, node.sourceParamId, node.dataFilter, node.runtimeMode
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,frontData TEXT,nodeType TEXT,sourceType TEXT,
                                   dataSource VARCHAR,dataEntity VARCHAR,sourceParamId BIGINT,dataFilter TEXT,runtimeMode TEXT)
    </select>

    <select id="selectSourceNodeFieldList" resultType="com.datalink.fdop.graph.api.graph.etl.EtlField">
        <if test="isEntity == true">
            WITH fieldList as (SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH
            (node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(element:d_e_data_element)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false
            RETURN field.fieldId, field.fieldName, field.fieldDesc,
            field.fieldType, field.length,
            field.decimalLength, field.isPk, field.seq, element.id as citeFieldId,element.code as
            preFieldName,field.warnType,'MAIN_FIELD' as fieldSourceType
            $$) as (fieldId TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,citeFieldId TEXT,preFieldName TEXT,warnType
            TEXT,fieldSourceType TEXT)
            UNION
            SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH
            (node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(input:d_e_data_element_input)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false
            RETURN field.fieldId, field.fieldName, field.fieldDesc,
            field.fieldType, field.length,
            field.decimalLength, field.isPk, field.seq, input.id as citeFieldId,input.code as
            preFieldName,field.warnType,'INPUT' as fieldSourceType
            $$) as (fieldId TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,citeFieldId TEXT,preFieldName TEXT,warnType
            TEXT,fieldSourceType TEXT)
            UNION
            SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH
            (node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(structure:d_e_data_entity_structure)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false
            RETURN field.fieldId, field.fieldName, field.fieldDesc,
            field.fieldType, field.length,
            field.decimalLength, field.isPk, field.seq, structure.id as citeFieldId,structure.code as
            preFieldName,field.warnType,'ENTITY' as fieldSourceType
            $$) as (fieldId TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,citeFieldId TEXT,preFieldName TEXT,warnType
            TEXT,fieldSourceType TEXT)
            ) select * from fieldList ORDER BY seq ASC
        </if>
        <if test="isEntity == false">
            SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)
            WHERE node.nodeId = '${nodeId}'
            RETURN field.fieldId, field.fieldName, field.fieldDesc,
            field.fieldType, field.length,
            field.decimalLength, field.isPk, field.seq, field.warnType
            ORDER BY field.seq ASC
            $$) as (fieldId TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT)
        </if>
    </select>

    <select id="selectTransformNode" resultType="com.datalink.fdop.graph.api.domain.etl.TransformNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.nodeType, node.frontData, node.transformType,
                               node.sql, node.graphNodeId $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,nodeType TEXT,frontData TEXT,transformType TEXT,
                                   sql TEXT,graphNodeId TEXT)
    </select>

    <select id="selectAllEtlFieldList" resultType="com.datalink.fdop.graph.api.graph.etl.EtlField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)
            WHERE node.nodeId = '${nodeId}'
            RETURN field.fieldId, field.preNodeCode, field.preNodeName, field.preFieldName,
                               field.fieldName, field.fieldDesc,
                               field.fieldType, field.length,
                               field.decimalLength, field.isPk, field.seq, field.warnType ORDER BY field.seq ASC
                                   $$) as (fieldId TEXT,preNodeCode TEXT,preNodeName TEXT,preFieldName TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
            BIGINT,
            decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT)
    </select>


    <select id="selectNodeFieldList" resultType="com.datalink.fdop.graph.api.graph.etl.EtlField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(citeField:f_g_c_etl_field)-[fieldNodeEdge]->(citeNode:f_g_c_etl_node)
            WHERE node.nodeId = '${nodeId}' AND edge.isSubField = false AND fieldNodeEdge.isFatherNode = true
            RETURN field.fieldId, field.fieldName, field.fieldDesc,
                               field.fieldType, field.length,
                               field.decimalLength, field.isPk, field.seq, field.warnType,
                               citeNode.nodeId as citeNodeId,
                               citeField.fieldId as citeFieldId,
                               citeNode.nodeName as preNodeName, citeField.fieldName as preFieldName
                               ORDER BY field.seq ASC
                               $$) as (fieldId TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length BIGINT,
                                   decimalLength BIGINT,isPk BOOLEAN,seq INTEGER,warnType TEXT,citeNodeId TEXT,citeFieldId TEXT,preNodeName TEXT,preFieldName TEXT)
    </select>

    <resultMap id="sink" type="com.datalink.fdop.graph.api.domain.etl.SinkNode">
        <id property="nodeId" column="nodeId"/>
        <result property="nodeCode" column="nodeCode"/>
        <result property="nodeName" column="nodeName"/>
        <result property="nodeType" column="nodeType"/>
        <result property="sinkType" column="sinkType"/>
        <result property="dataEntity" column="dataEntity"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
        <result property="dataEntityQualityList" column="dataEntityQualityList"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
        <result property="preFlag" column="preFlag"/>
        <result property="preSqlCondition" column="preSqlCondition"/>
        <result property="runtimeMode" column="runtimeMode"/>
    </resultMap>

    <select id="selectSinkNode" resultMap="sink">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.nodeType, node.frontData, node.sinkType, node.dataEntity,
                               node.dataEntityQualityList, node.preFlag, node.preSqlCondition, node.runtimeMode
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,nodeType TEXT,frontData TEXT,sinkType TEXT,
                                   dataEntity VARCHAR,
                                   dataEntityQualityList VARCHAR,preFlag BOOLEAN,preSqlCondition TEXT,runtimeMode TEXT)
    </select>

    <select id="selectParamsNode" resultType="com.datalink.fdop.graph.api.domain.etl.ParamsNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) WHERE node.nodeId = '${nodeId}'
        RETURN node.nodeId, node.nodeCode, node.nodeName, node.nodeType, node.frontData, node.outputParamType, node.outputParamId,
                               node.paramScope, node.paramHandleType, node.paramResultType
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,nodeType TEXT,frontData TEXT,outputParamType TEXT,
                                   outputParamId BIGINT,
                                   paramScope TEXT,paramHandleType TEXT,paramResultType TEXT)
    </select>

    <select id="selectAllSinkNodeFieldList" resultType="com.datalink.fdop.graph.api.graph.etl.EtlField">
        WITH fieldList as (SELECT *
                           FROM ag_catalog.cypher('zjdata_graph', $$
                               MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)-[fieldEdge:f_g_c_etl_field_field_edge]->(element:d_e_data_element)
                               WHERE node.nodeId = '${nodeId}' AND fieldEdge.isSubField = true
                               RETURN field.fieldId as sinkPreFieldId, field.fieldName as sinkPreFieldName,
                                                  field.fieldDesc as sinkPreFieldDesc,
                                                  field.fieldType as sinkPreFieldType, field.length as sinkPreLength,
                                                  field.decimalLength as sinkPreDecimalLength,
                                                  field.isPk as sinkPreIsPk,
                                                  field.warnType as sinkPreWarnType,
                                                  element.id as tableFieldId, element.code as tableFieldCode,
                                                  element.name as tableFieldName, element.description as tableFieldDesc,
                                                  element.fieldType as tableFieldType, element.length as tableLength,
                                                  element.decimalLength as tableDecimalLength,
                                                  element.isPk as tableIsPk,
                                                  fieldEdge.warnType as tableWarnType,
                                                  field.preNodeName,
                                                  field.preFieldName, 'MAIN_FIELD' as fieldSourceType, fieldEdge.seq
                                   $$) as (sinkPreFieldId TEXT, sinkPreFieldName TEXT, sinkPreFieldDesc TEXT, sinkPreFieldType TEXT, sinkPreLength BIGINT, sinkPreDecimalLength BIGINT, sinkPreIsPk BOOLEAN, sinkPreWarnType TEXT, tableFieldId TEXT, tableFieldCode TEXT, tableFieldName TEXT, tableFieldDesc TEXT, tableFieldType TEXT, tableLength BIGINT, tableDecimalLength BIGINT, tableIsPk BOOLEAN, tableWarnType TEXT, preNodeName TEXT, preFieldName TEXT, fieldSourceType TEXT, seq INTEGER)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)-[fieldEdge:f_g_c_etl_field_field_edge]->(input :d_e_data_element_input)
            WHERE node.nodeId = '${nodeId}' AND fieldEdge.isSubField = true
            RETURN field.fieldId as sinkPreFieldId, field.fieldName as sinkPreFieldName,
                               field.fieldDesc as sinkPreFieldDesc,
                               field.fieldType as sinkPreFieldType, field.length as sinkPreLength,
                               field.decimalLength as sinkPreDecimalLength, field.isPk as sinkPreIsPk,
                               field.warnType as sinkPreWarnType,
                               input.id as tableFieldId, input.code as tableFieldCode,
                               input.name as tableFieldName, input.description as tableFieldDesc,
                               input.fieldType as tableFieldType, input.length as tableLength,
                               input.decimalLength as tableDecimalLength, input.isPk as tableIsPk,
                               fieldEdge.warnType as tableWarnType,
                               field.preNodeName,
                               field.preFieldName, 'INPUT' as fieldSourceType, fieldEdge.seq
                                   $$) as (sinkPreFieldId TEXT,sinkPreFieldName TEXT,sinkPreFieldDesc TEXT,sinkPreFieldType TEXT,sinkPreLength BIGINT,
        sinkPreDecimalLength BIGINT,sinkPreIsPk BOOLEAN,sinkPreWarnType TEXT,tableFieldId TEXT,tableFieldCode TEXT,tableFieldName TEXT,
        tableFieldDesc TEXT,tableFieldType TEXT,tableLength BIGINT,
        tableDecimalLength BIGINT,tableIsPk BOOLEAN,tableWarnType TEXT,preNodeName TEXT,preFieldName TEXT,fieldSourceType TEXT,seq INTEGER)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_g_c_etl_node) -[]->(field:f_g_c_etl_field)-[fieldEdge:f_g_c_etl_field_field_edge]->(structure:d_e_data_entity_structure)
            WHERE node.nodeId = '${nodeId}' AND fieldEdge.isSubField = true
            RETURN field.fieldId as sinkPreFieldId, field.fieldName as sinkPreFieldName,
                               field.fieldDesc as sinkPreFieldDesc,
                               field.fieldType as sinkPreFieldType, field.length as sinkPreLength,
                               field.decimalLength as sinkPreDecimalLength, field.isPk as sinkPreIsPk,
                               field.warnType as sinkPreWarnType,
                               structure.id as tableFieldId, structure.code as tableFieldCode,
                               structure.name as tableFieldName, structure.description as tableFieldDesc,
                               structure.fieldType as tableFieldType, structure.length as tableLength,
                               structure.decimalLength as tableDecimalLength, structure.isPk as tableIsPk,
                               fieldEdge.warnType as tableWarnType,
                               field.preNodeName,
                               field.preFieldName, 'ENTITY' as fieldSourceType, fieldEdge.seq
                                   $$) as (sinkPreFieldId TEXT,sinkPreFieldName TEXT,sinkPreFieldDesc TEXT,sinkPreFieldType TEXT,sinkPreLength BIGINT,
        sinkPreDecimalLength BIGINT,sinkPreIsPk BOOLEAN,sinkPreWarnType TEXT,tableFieldId TEXT,tableFieldCode TEXT,tableFieldName TEXT,
        tableFieldDesc TEXT,tableFieldType TEXT,tableLength BIGINT,
        tableDecimalLength BIGINT,tableIsPk BOOLEAN,tableWarnType TEXT,preNodeName TEXT,preFieldName TEXT,fieldSourceType TEXT,seq INTEGER)
        )
        select *
        from fieldList
        ORDER BY seq ASC
    </select>

    <select id="selectSinkNodeFieldList" resultType="com.datalink.fdop.graph.api.graph.etl.EtlField">
        WITH fieldList as (SELECT *
                           FROM ag_catalog.cypher('zjdata_graph', $$
                               MATCH (citeNode:f_g_c_etl_node) -[nodeEdge]->(node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)
                               WHERE node.nodeId = '${nodeId}' AND nodeEdge.isSubNode = true
                               WITH citeNode, node, field
                                                        MATCH (citeField:f_g_c_etl_field) &lt; -[citeFieldEdge]-(field)-[subFieldEdge:f_g_c_etl_field_field_edge]->(element:d_e_data_element)
                                                      WHERE citeFieldEdge.isSubField = false AND subFieldEdge.isSubField = true
                                                      RETURN citeNode.nodeName as
                                                      preNodeName, citeField.fieldName as preFieldName,
                                                  citeNode.nodeId as sinkPreCiteNodeId, citeField.fieldId as
        sinkPreCiteFieldId, field.fieldId as
        sinkPreFieldId, field.fieldName as
        sinkPreFieldName,
                                                  field.fieldDesc as sinkPreFieldDesc,
                                                  field.fieldType as sinkPreFieldType, field.length as sinkPreLength,
                                                  field.decimalLength as sinkPreDecimalLength,
                                                  field.isPk as sinkPreIsPk,
                                                  field.warnType as sinkPreWarnType,
                                                  element.id as tableFieldId, element.code as tableFieldCode,
                                                  element.name as tableFieldName, element.description as tableFieldDesc,
                                                  element.fieldType as tableFieldType, element.length as tableLength,
                                                  element.decimalLength as tableDecimalLength,
                                                  element.isPk as tableIsPk,
                                                  subFieldEdge.warnType as tableWarnType,
                                                  'MAIN_FIELD' as fieldSourceType, subFieldEdge.seq
        $$) as (preNodeName TEXT, preFieldName TEXT, sinkPreCiteNodeId TEXT, sinkPreCiteFieldId TEXT, sinkPreFieldId
            TEXT, sinkPreFieldName
            TEXT, sinkPreFieldDesc TEXT, sinkPreFieldType TEXT, sinkPreLength
            BIGINT, sinkPreDecimalLength BIGINT, sinkPreIsPk BOOLEAN, sinkPreWarnType TEXT, tableFieldId TEXT, tableFieldCode
            TEXT, tableFieldName TEXT, tableFieldDesc TEXT, tableFieldType TEXT, tableLength BIGINT, tableDecimalLength BIGINT, tableIsPk BOOLEAN, tableWarnType TEXT, fieldSourceType TEXT, seq INTEGER)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (citeNode:f_g_c_etl_node) -[nodeEdge]->(node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)
            WHERE node.nodeId = '${nodeId}' AND nodeEdge.isSubNode = true
            WITH citeNode, node, field
                                     MATCH (citeField:f_g_c_etl_field) &lt;
                                 -[citeFieldEdge]-(field)-[subFieldEdge:f_g_c_etl_field_field_edge]->(input :d_e_data_element_input)
                                   WHERE citeFieldEdge.isSubField = false AND subFieldEdge.isSubField = true
                                   RETURN citeNode.nodeName as
                                   preNodeName, citeField.fieldName as preFieldName,
                               citeNode.nodeId as sinkPreCiteNodeId, citeField.fieldId as
        sinkPreCiteFieldId, field.fieldId as
        sinkPreFieldId, field.fieldName as
        sinkPreFieldName,
                               field.fieldDesc as sinkPreFieldDesc,
                               field.fieldType as sinkPreFieldType, field.length as sinkPreLength,
                               field.decimalLength as sinkPreDecimalLength, field.isPk as sinkPreIsPk,
                               field.warnType as sinkPreWarnType,
                               input.id as tableFieldId, input.code as tableFieldCode,
                               input.name as tableFieldName, input.description as tableFieldDesc,
                               input.fieldType as tableFieldType, input.length as tableLength,
                               input.decimalLength as tableDecimalLength, input.isPk as tableIsPk,
                               subFieldEdge.warnType as tableWarnType, 'INPUT' as fieldSourceType, subFieldEdge.seq
        $$) as (preNodeName TEXT,preFieldName TEXT,sinkPreCiteNodeId TEXT,sinkPreCiteFieldId TEXT,sinkPreFieldId
        TEXT,sinkPreFieldName
        TEXT,sinkPreFieldDesc TEXT,sinkPreFieldType TEXT,sinkPreLength
        BIGINT,
        sinkPreDecimalLength BIGINT,sinkPreIsPk BOOLEAN,sinkPreWarnType TEXT,tableFieldId TEXT,tableFieldCode
        TEXT,tableFieldName TEXT,
        tableFieldDesc TEXT,tableFieldType TEXT,tableLength BIGINT,
        tableDecimalLength BIGINT,tableIsPk BOOLEAN,tableWarnType TEXT,fieldSourceType TEXT,seq INTEGER)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (citeNode:f_g_c_etl_node) -[nodeEdge]->(node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)
            WHERE node.nodeId = '${nodeId}' AND nodeEdge.isSubNode = true
            WITH citeNode, node, field
                                     MATCH (citeField:f_g_c_etl_field) &lt; -[citeFieldEdge]-(field)-[subFieldEdge:f_g_c_etl_field_field_edge]->(structure:d_e_data_entity_structure)
                                   WHERE citeFieldEdge.isSubField = false AND subFieldEdge.isSubField = true
                                   RETURN citeNode.nodeName as
                                   preNodeName, citeField.fieldName as preFieldName,
                               citeNode.nodeId as sinkPreCiteNodeId, citeField.fieldId as
        sinkPreCiteFieldId, field.fieldId as
        sinkPreFieldId, field.fieldName as
        sinkPreFieldName,
                               field.fieldDesc as sinkPreFieldDesc,
                               field.fieldType as sinkPreFieldType, field.length as sinkPreLength,
                               field.decimalLength as sinkPreDecimalLength, field.isPk as sinkPreIsPk,
                               field.warnType as sinkPreWarnType,
                               structure.id as tableFieldId, structure.code as tableFieldCode,
                               structure.name as tableFieldName, structure.description as tableFieldDesc,
                               structure.fieldType as tableFieldType, structure.length as tableLength,
                               structure.decimalLength as tableDecimalLength, structure.isPk as tableIsPk,
                               subFieldEdge.warnType as tableWarnType, 'ENTITY' as fieldSourceType, subFieldEdge.seq
        $$) as (preNodeName TEXT,preFieldName TEXT,sinkPreCiteNodeId TEXT,sinkPreCiteFieldId TEXT,sinkPreFieldId
        TEXT,sinkPreFieldName
        TEXT,sinkPreFieldDesc TEXT,sinkPreFieldType TEXT,sinkPreLength
        BIGINT,
        sinkPreDecimalLength BIGINT,sinkPreIsPk BOOLEAN,sinkPreWarnType TEXT,tableFieldId TEXT,tableFieldCode
        TEXT,tableFieldName TEXT,
        tableFieldDesc TEXT,tableFieldType TEXT,tableLength BIGINT,
        tableDecimalLength BIGINT,tableIsPk BOOLEAN,tableWarnType TEXT,fieldSourceType TEXT,seq INTEGER)
        ) select * from fieldList ORDER BY seq ASC
    </select>

    <select id="selectEtlNodeList" resultType="com.datalink.fdop.graph.api.domain.etl.EtlNode">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (task:t_ds_task_definition) -[edge:f_g_c_etl_node_task_edge]->(node:f_g_c_etl_node)
            WHERE task.code = ${taskCode} AND node.nodeType IN ['SOURCE', 'TRANSFORM', 'SINK', 'OUTPUT_PARAM']
            RETURN node.nodeId, node.nodeCode, node.nodeName, node.nodeType, node.frontData, node.warnType
                                   $$) as (nodeId TEXT,nodeCode TEXT,nodeName TEXT,nodeType TEXT,frontData TEXT,warnType TEXT)
    </select>

    <select id="selectEtlNodeEdgeList" resultType="com.datalink.fdop.graph.api.domain.etl.EtlNodeEdge">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (task:t_ds_task_definition) -[:f_g_c_etl_node_task_edge]->(source :f_g_c_etl_node)-[edge:f_g_c_etl_node_node_edge]->(target:f_g_c_etl_node)
            WHERE task.code =
                                               ${taskCode} AND source.nodeId = '${nodeId}' AND edge.isSubNode = true AND edge.isEtl is null
            RETURN edge.sideId, source.nodeId as sourceNodeId, target.nodeId as targetNodeId, edge.frontData,
                               edge.warnType
                                   $$) as (sideId TEXT,sourceNodeId TEXT,targetNodeId TEXT,frontData TEXT, warnType TEXT)
    </select>

    <select id="selectSubFieldList" resultType="com.datalink.fdop.graph.api.graph.etl.EtlField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (sourceNode:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)
        -[edge:f_g_c_etl_field_field_edge]->(subField:f_g_c_etl_field)-[]->(targetNode:f_g_c_etl_node)
        WHERE sourceNode.nodeId = '${sourceNodeId}'
        <if test="fieldIdss != null and fieldIdss.size() > 0">
            AND
            <foreach collection="fieldIdss" item="fieldIds" index="" open="(" separator="OR" close=")">
                field.fieldId IN
                <foreach collection="fieldIds" item="fieldId" index="" open="[" separator="," close="]">
                    '${fieldId}'
                </foreach>
            </foreach>
        </if>
        AND edge.isSubField = true AND targetNode.nodeId = '${targetNodeId}'
        RETURN subField.fieldId, subField.fieldName, subField.fieldDesc,
        subField.fieldType, subField.length,
        subField.decimalLength, subField.isPk, subField.warnType, sourceNode.nodeId as citeNodeId,
        field.fieldId as citeFieldId,
        field.fieldName as preFieldName,
        sourceNode.nodeName as preNodeName
        $$) as (fieldId TEXT,fieldName TEXT,fieldDesc TEXT,fieldType TEXT,length
        BIGINT,
        decimalLength BIGINT,isPk BOOLEAN,warnType TEXT,citeNodeId TEXT,citeFieldId TEXT,preFieldName
        TEXT,preNodeName
        TEXT)
    </select>

</mapper> 