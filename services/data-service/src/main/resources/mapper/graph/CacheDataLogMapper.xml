<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.graph.mapper.CacheDataLogMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.graph.api.domain.CacheDataLog"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insertCacheDataLog" parameterType="com.datalink.fdop.graph.api.domain.CacheDataLog" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (log:f_g_c_cache_data_log ${@com.datalink.fdop.graph.utils.DomainAgeUtils@getCacheDataLogAgeStr(cacheDataLog)})
                                   RETURN id(log), properties(log)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.graph.api.domain.CacheDataLog" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:f_g_c_cache_data_log)
        WHERE log.id = ${id}
        <set>
            <if test="databaseName != null and databaseName != ''">log.databaseName = '${databaseName}',</if>
            <if test="sql != null and sql != ''">log.sql = '${sql}',</if>
            <if test="jobId != null and jobId != ''">log.jobId = '${jobId}',</if>
            <if test="errorLog != null and errorLog != ''">log.errorLog = '${errorLog}',</if>
            <if test="cacheDataStatus != null">log.cacheDataStatus = '${cacheDataStatus}'</if>
        </set>
        RETURN id(log), properties(log)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:f_g_c_cache_data_log)
        WHERE log.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE log RETURN id(log),properties(log) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="deleteById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:f_g_c_cache_data_log)
        WHERE log.id = ${id}
        DETACH DELETE log RETURN id(log),properties(log) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectByIds" resultType="com.datalink.fdop.graph.api.domain.CacheDataLog">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:f_g_c_cache_data_log) WHERE log.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN log.id, log.tenantId, log.userId, log.databaseName, log.tableName, log.sql, log.cacheDataStatus,
        log.errorLog,
        log.jobId,
        log.createTime ORDER BY log.createTime desc
        $$) as (id BIGINT,tenantId BIGINT,userId BIGINT,databaseName TEXT,tableName TEXT,sql TEXT,cacheDataStatus
        TEXT,errorLog TEXT,jobId TEXT,createTime TEXT)
    </select>

    <select id="selectByTaskCode" resultType="com.datalink.fdop.graph.api.domain.CacheDataLog">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:f_g_c_cache_data_log) WHERE log.taskCode = ${taskCode}
            RETURN log.id, log.tenantId, log.userId, log.databaseName, log.tableName, log.sql, log.cacheDataStatus, log.errorLog,
                               log.jobId,
                               log.createTime ORDER BY log.createTime desc
        $$) as (id BIGINT,tenantId BIGINT,userId BIGINT,databaseName TEXT,tableName TEXT,sql TEXT,cacheDataStatus TEXT,errorLog TEXT,jobId TEXT,createTime TEXT)
    </select>

    <select id="selectList" resultType="com.datalink.fdop.graph.api.domain.CacheDataLog">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:f_g_c_cache_data_log) WHERE log.tenantId = ${tenantId} and log.userId =
                                               ${userId} and log.taskCode = ${taskCode}
            RETURN log.id, log.tenantId, log.userId, log.databaseName, log.tableName, log.sql, log.cacheDataStatus, log.errorLog,
                               log.jobId,
                               log.createTime ORDER BY log.createTime desc
        $$) as (id BIGINT,tenantId BIGINT,userId BIGINT,databaseName TEXT,tableName TEXT,sql TEXT,cacheDataStatus TEXT,errorLog TEXT,jobId TEXT,createTime TEXT)
    </select>

    <select id="selectAll" resultType="com.datalink.fdop.graph.api.domain.CacheDataLog">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:f_g_c_cache_data_log) RETURN log.id, log.tenantId, log.userId, log.databaseName, log.tableName,
                               log.sql, log.cacheDataStatus, log.errorLog,
                               log.jobId,
                               log.createTime ORDER BY log.createTime desc
        $$) as (id BIGINT,tenantId BIGINT,userId BIGINT,databaseName TEXT,tableName TEXT,sql TEXT,cacheDataStatus TEXT,errorLog TEXT,jobId TEXT,createTime TEXT)
    </select>

</mapper> 