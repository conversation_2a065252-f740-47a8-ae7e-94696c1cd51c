<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.seatunnel.mapper.SeaTunnelCmdMenuMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="createSeaTunnelCmdMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:s_c_seatunnel_cmd_menu), (menuE:s_c_seatunnel_cmd_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'s_c_seatunnel_cmd_menu',endTable:'s_c_seatunnel_cmd_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'s_c_seatunnel_cmd_menu',endTable:'s_c_seatunnel_cmd_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertSeaTunnelCmdMenu" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:s_c_seatunnel_cmd_menu ${@com.datalink.fdop.seatunnel.utils.DomainAgeUtils@getSeaTunnelCmdMenuAgeStr(seaTunnelCmdMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_seatunnel_cmd_menu)
        WHERE menu.id = ${id}
        <set>
            <if test="pid != null">menu.pid = ${pid},</if>
            <if test="code != null and code != ''">menu.code = '${code}',</if>
            <if test="name != null and name != ''">menu.name = '${name}',</if>
            <if test="description != null and description != ''">menu.description = '${description}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" parameterType="com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_seatunnel_cmd_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>





    <select id="deleteSeaTunnelCmdMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[mme1:menu_menu_edge]->(menu)-[mme2:menu_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menu.id = ${pid}
        DELETE mme1, mme2 RETURN id(mme1),
        properties(mme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_seatunnel_cmd_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:s_c_seatunnel_cmd_menu) WHERE menu.id = ${id} RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:s_c_seatunnel_cmd_menu) WHERE menu.code = '${code}' RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByPid" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:s_c_seatunnel_cmd_menu) WHERE menu.pid = ${pid} RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:s_c_seatunnel_cmd_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id} RETURN id(menu),
                               properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:s_c_seatunnel_cmd_menu) -[mme:menu_menu_edge]->(menuE:s_c_seatunnel_cmd_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id ag_catalog.agtype)
    </select>


    <select id="overview" resultType="com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmd">
        WITH overview as (
        <if test="pid == -1">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_seatunnel_cmd)
            WHERE node.pid = -1
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,'顶级菜单' as
            menuName,node.createTime,node.updateTime,node.serialNumber,node.type,node.jobId,node.status
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime
            TEXT,updateTime TEXT,serialNumber BIGINT,type TEXT,jobId TEXT,status TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_seatunnel_cmd) -[:node_menu_edge]->(menu:s_c_seatunnel_cmd_menu)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.createTime,node.updateTime,node.serialNumber,node.type,node.jobId,node.status
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime TEXT,updateTime
        TEXT,serialNumber BIGINT,type TEXT,jobId TEXT,status TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>

    <select id="selectMenuTree" resultType="com.datalink.fdop.seatunnel.api.domain.SeaTunnelCmdTree">
        SELECT * FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_seatunnel_cmd_menu)
        <where>
            <if test="code != null and code != ''">menu.code =~'.*${code}.*'</if>
        </where>
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType
        ORDER BY menu.code ${sort}
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuType TEXT)
    </select>

    <select id="querySerialNumber" resultType="int">
        SELECT (count(1) + 1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_seatunnel_cmd_menu) RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

</mapper> 