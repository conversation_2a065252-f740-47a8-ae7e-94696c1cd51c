<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.datart.mapper.SourceMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties"
                javaType="com.datalink.fdop.datart.domain.Source"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="createSource" parameterType="String" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:f_d_source ${@com.datalink.fdop.datart.utils.DomainAgeUtils@getSourceAgeStr(source)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateSource" parameterType="com.datalink.fdop.datart.domain.Source" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_source)
        WHERE node.id = '${id}'
        <set>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="config != null and config != ''">node.config = '${config}',</if>
            <if test="type != null and type != ''">node.type = '${type}',</if>
            <if test="orgId != null and orgId != ''">node.orgId = '${orgId}',</if>
            <if test="parentId != null and parentId != ''">node.parentId = '${parentId}',</if>
            <if test="isFolder != null">node.isFolder = '${isFolder}',</if>
            <if test="index != null">node.index = '${index}',</if>
            <if test="status != null">node.status = '${status}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteSource" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_source) WHERE node.id = '${id}'
            DETACH DELETE node RETURN id(node),
                               properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.datart.domain.Source">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_source) WHERE node.id = '${id}'
RETURN node.id, node.name, node.type, node.config, node.status, node.isFolder
                                   $$) as (id TEXT,name TEXT,type TEXT,config TEXT, status INT,isFolder TEXT)
    </select>

    <select id="selectByName" resultType="com.datalink.fdop.datart.domain.Source">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_source) WHERE node.name = '${name}'
RETURN node.id, node.name, node.type, node.config, node.status, node.isFolder
                                   $$) as (id TEXT,name TEXT,type TEXT,config TEXT, status INT,isFolder TEXT)
    </select>
    <select id="listByOrg" resultType="com.datalink.fdop.datart.domain.Source">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_source) WHERE node.orgId = '${orgId}' and node.status=${active}
            RETURN node.id, node.name, node.type, node.config, node.status, node.isFolder
                                   $$) as (id TEXT,name TEXT,type TEXT,config TEXT, status INT,isFolder TEXT)
    </select>
    <select id="getSourceDetail" resultType="com.datalink.fdop.datart.domain.Source">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_source) WHERE node.id = '${sourceId}'
            RETURN node.id, node.name, node.type, node.config, node.status, node.isFolder
                                   $$) as (id TEXT,name TEXT,type TEXT,config TEXT, status INT,isFolder TEXT)
    </select>

</mapper>