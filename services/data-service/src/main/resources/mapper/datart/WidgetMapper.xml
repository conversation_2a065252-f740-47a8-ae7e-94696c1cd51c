<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.datart.mapper.WidgetMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.datart.api.domain.Widget"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="selectById" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_widget)
            WHERE node.id = '${id}'
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="listByDashboard" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_widget) WHERE node.dashboardId = ${dashboardId}
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchInsert" parameterType="com.datalink.fdop.datart.api.domain.Widget" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',$$
        <foreach collection="widgets" item="widget">
            CREATE (:f_d_widget ${@com.datalink.fdop.datart.utils.DomainAgeUtils@getWidgetAgeStr(widget)})
        </foreach>
        $$) as (w ag_catalog.agtype)
    </select>

    <select id="batchUpdate" parameterType="com.datalink.fdop.datart.api.domain.Widget" resultType="int">
        <foreach collection="widgets" item="widget">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_widget)
            WHERE node.id = '${widget.id}'
            <set>
                <if test="widget.config != null and widget.config != ''">node.config = '${widget.config}',</if>
                node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
                node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
            </set>
            RETURN id(node), properties(node)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </foreach>
    </select>

    <select id="update" parameterType="com.datalink.fdop.datart.api.domain.Widget" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_widget)
        WHERE node.id = '${widget.id}'
        <set>
            <if test="widget.parentId != null and widget.parentId != ''">node.parentId = '${widget.parentId}',</if>
            <if test="widget.config != null and widget.config != ''">node.config = '${widget.config}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteWidgets" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_widget)
        WHERE node.id IN
        <foreach collection="widgetIds" item="widgetId" open="[" separator="," close="]">
            '${widgetId}'
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="deleteWidgetElements" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_rel_widget_element)
        WHERE node.widgetId IN
        <foreach collection="widgetIds" item="widgetId" open="[" separator="," close="]">
            '${widgetId}'
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="deleteWidgetWidgets" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_rel_widget_widget)
        WHERE node.sourceId IN
        <foreach collection="widgetIds" item="widgetId" open="[" separator="," close="]">
            '${widgetId}'
        </foreach>
        OR node.targetId IN
        <foreach collection="widgetIds" item="widgetId" open="[" separator="," close="]">
            '${widgetId}'
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

</mapper> 