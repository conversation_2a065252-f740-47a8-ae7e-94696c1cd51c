<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.datart.mapper.ViewMenuMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties"
                javaType="com.datalink.fdop.datart.domain.ViewMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <select id="createViewMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:f_d_view_menu), (menuE:f_d_view_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'f_d_view_menu',endTable:'f_d_view_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'f_d_view_menu',endTable:'f_d_viewd_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertViewMenu" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:f_d_view_menu ${@com.datalink.fdop.datart.utils.DomainAgeUtils@getViewMenuAgeStr(viewMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.datart.domain.ViewMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:f_d_view_menu)
        WHERE menu.id = ${viewMenu.id}
        <set>
            <if test="viewMenu.pid != null">menu.pid = ${viewMenu.pid},</if>
            <if test="viewMenu.code != null and viewMenu.code != ''">menu.code = '${viewMenu.code}',</if>
            <if test="viewMenu.name != null and viewMenu.name != ''">menu.name = '${viewMenu.name}',</if>
            <if test="viewMenu.description != null and viewMenu.description != ''">menu.description = '${viewMenu.description}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" parameterType="com.datalink.fdop.datart.domain.ViewMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:f_d_view_menu)
        WHERE menu.id IN
        <foreach collection="menuIdList" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteViewMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[mme1:menu_menu_edge]->(menu)-[mme2:menu_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menu.id = ${pid}
        DELETE mme1, mme2 RETURN id(mme1),
        properties(mme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:f_d_view_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:f_d_view_menu) WHERE menu.id = ${id} RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:f_d_view_menu) WHERE menu.code = '${code}' RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByPid" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:f_d_view_menu) WHERE menu.pid = ${pid} RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:f_d_view_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id} RETURN id(menu),
                               properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:f_d_view_menu) -[mme:menu_menu_edge]->(menuE:f_d_view_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="createViewAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:f_d_view_menu), (node:f_d_view)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="elementIdList" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'f_d_view_menu',endTable:'f_d_view'}]->(node)-[:node_menu_edge
        {startTable:'f_d_view',endTable:'f_d_view_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectMenuTree" resultType="com.datalink.fdop.datart.domain.ViewTree">
        SELECT *
        FROM (
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:f_d_view_menu) WHERE menu.pid = -1
        WITH DISTINCT (menu)
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType
        $$) as (id TEXT,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_view) WHERE node.pid = -1
        <if test="code != null and code != ''">AND node.code =~'.*${code}.*'</if>
        WITH DISTINCT (node)
        RETURN node.id, node.pid, node.code, node.name, node.description, 'NODE' as menuType
        $$) as (id TEXT, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:f_d_view_menu) -[]->(menuE:f_d_view_menu)
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType
        $$) as (id TEXT, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:f_d_view_menu) -[]->(nodeE:f_d_view)
        <where>
            <if test="code != null and code != ''">nodeE.code =~'.*${code}.*'</if>
        </where>
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType
        $$) as (id TEXT, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT)
        ) as zjdata_graph_table
        ORDER BY zjdata_graph_table.code ${sort}
    </select>
    <select id="overview" resultType="com.datalink.fdop.datart.domain.View">
        WITH overview as (
        <if test="pid == -1">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_view)
            WHERE node.pid = -1
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,'顶级菜单' as
            menuName,node.createTime,node.updateTime,node.type
            $$) as (id TEXT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime
            TEXT,updateTime TEXT,type TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_view) -[:node_menu_edge]->(menu:f_d_view_menu)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.createTime,node.updateTime,node.type
        $$) as (id TEXT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime TEXT,updateTime
        TEXT,type TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>


</mapper>