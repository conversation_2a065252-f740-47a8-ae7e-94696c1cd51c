<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.datart.mapper.DownloadMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties"
                javaType="com.datalink.fdop.datart.api.domain.Download"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insertDownload" parameterType="String" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
        $$ CREATE (node:f_d_download ${@com.datalink.fdop.datart.utils.DomainAgeUtils@getDownloadAgeStr(download)}) RETURN id(node),
        properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.datart.api.domain.Download" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_download)
        WHERE node.id = '${id}'
        <set>
            <if test="resourceType != null">node.resourceType = '${resourceType}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="path != null and path != ''">node.path = '${path}',</if>
            <if test="lastDownloadTime != null">node.lastDownloadTime = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime(lastDownloadTime)}',</if>
            <if test="status != null">node.status = ${status},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_download) WHERE node.id = '${id}'
        DETACH DELETE node RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectById1" resultType="com.datalink.fdop.datart.api.domain.Download">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_download) WHERE node.id = '${id}'
        RETURN node.id,node.pid,node.code, node.name, node.type, node.model, node.config, node.script, node.status,
        node.isFolder,
        node.sourceId, node.description
        $$) as (id TEXT,pid BIGINT,code TEXT,name TEXT,type TEXT,model TEXT,config TEXT,script TEXT, status INT,isFolder
        TEXT,sourceId
        TEXT,description TEXT)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_download) WHERE node.id = '${id}' RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectList" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_download)
        <where>
            <if test="download != null and download.resourceType != null">and node.resourceType = '${download.resourceType}'</if>
            <if test="download != null and download.createBy != null">and node.createBy = '${download.createBy}'</if>
        </where>
        WITH node
        ORDER BY node.createTime desc
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper>