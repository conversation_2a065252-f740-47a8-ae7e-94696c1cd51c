<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.datart.mapper.ViewMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties"
                javaType="com.datalink.fdop.datart.domain.View"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="updateById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_view)
        WHERE node.id = '${id}'
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="description != null and description != ''">node.description = '${description}',</if>
            <if test="orgId != null and orgId != ''">node.orgId = '${orgId}',</if>
            <if test="sourceId != null">node.sourceId = ${sourceId},</if>
            <if test="dataSourceId != null and dataSourceId != ''">node.dataSourceId = '${dataSourceId}',</if>
            <if test="script != null and script != ''">node.script = ${script},</if>
            <if test="type != null">node.type = '${type}',</if>
            <if test="model != null and model != ''">node.model = '${model}',</if>
            <if test="config != null and config != ''">node.config = '${config}',</if>
            <if test="parentId != null and parentId != ''">node.parentId = '${parentId}',</if>
            <if test="isFolder != null and isFolder != ''">node.isFolder = ${isFolder},</if>
            <if test="index != null and index != ''">node.index = ${index},</if>
            <if test="status != null and status != ''">node.status = '${status}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteViewAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            '${id}'
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createView" parameterType="String" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:f_d_view ${@com.datalink.fdop.datart.utils.DomainAgeUtils@getViewAgeStr(view)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteView" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_view) WHERE node.id  in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            '${id}'
        </foreach>
            DETACH DELETE node RETURN id(node),
                               properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_view) WHERE node.id = '${id}'
           RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </select>

    <select id="listByIds" resultType="com.datalink.fdop.datart.domain.View">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_view) WHERE node.status = 1 AND node.id IN
        <foreach collection="viewIds" item="id" open="[" separator="," close="]">
            '${id}'
        </foreach>
        RETURN node.id, node.name, node.type, node.model, node.config, node.script, node.status, node.isFolder,
        node.sourceId, node.description
        $$) as (id TEXT,name TEXT,type TEXT,model TEXT,config TEXT,script TEXT, status INT,isFolder TEXT,sourceId
        TEXT,description TEXT)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_view) WHERE node.code = '${code}'
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createViewAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:f_d_view_menu), (node:f_d_view)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            '${id}'
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'f_d_view_menu',endTable:'f_d_view'}]->(node)-[:node_menu_edge
        {startTable:'f_d_view',endTable:'f_d_view_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_view) WHERE node.code = '${code}' AND node.id &lt;&gt; '${id}' RETURN id(node),
                               properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectTree" resultType="com.datalink.fdop.datart.domain.ViewTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (nodeS:f_d_view) -[]->(menuE:f_d_view_menu)
        <where>
            <if test="code != null and code != ''">nodeS.code =~'.*${code}.*'</if>
        </where>
        RETURN nodeS.id, nodeS.pid, nodeS.code, nodeS.name, nodeS.description, 'NODE' as menuType
        ORDER BY nodeS.code ${sort}
        $$) as (id TEXT,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType
        TEXT)
    </select>

</mapper>