<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.datart.mapper.RelWidgetElementMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties"
                javaType="com.datalink.fdop.datart.api.domain.RelWidgetElement"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="listWidgetElements" parameterType="com.datalink.fdop.datart.api.domain.RelWidgetElement"
            resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_rel_widget_element) WHERE node.widgetId = '${widgetId}'
            RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="batchInsert" parameterType="com.datalink.fdop.datart.api.domain.RelWidgetElement" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',$$
        <foreach collection="elements" item="element">
            CREATE (:f_d_rel_widget_element
            ${@com.datalink.fdop.datart.utils.DomainAgeUtils@getWidgetElementAgeStr(element)})
        </foreach>
        $$) as (e ag_catalog.agtype)
    </select>

    <select id="deleteByWidgets" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:f_d_rel_widget_element)
        WHERE node.widgetId IN
        <foreach collection="widgetIds" item="widgetId" open="[" separator="," close="]">
            '${widgetId}'
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

</mapper> 