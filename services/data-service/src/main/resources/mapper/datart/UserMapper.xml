<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.datart.mapper.UserMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties"
                javaType="com.datalink.fdop.datart.api.domain.User"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="createUser" parameterType="String" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:f_d_user ${@com.datalink.fdop.datart.utils.DomainAgeUtils@getUserAgeStr(user)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteUser" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_user) WHERE node.userId = ${userId}
            DETACH DELETE node RETURN id(node),
                               properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.datart.api.domain.User">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:f_d_user) WHERE node.userId = ${userId}
            RETURN node.userId, node.datartUserId
                                   $$) as (userId BIGINT,datartUserId TEXT)
    </select>

</mapper>