<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.stream.mapper.StreamHistoryMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.stream.api.domain.StreamHistory"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="createStreamAndStreamHistoryEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (stream:s_c_stream), (node:s_c_stream_history)
        WHERE stream.id = ${streamId}
        AND node.streamId = ${streamId} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (stream)-[nme:s_c_stream_history_edge
        {startTable:'s_c_stream',endTable:'s_c_stream_history'}]->(node)-[:s_c_stream_history_edge
        {startTable:'s_c_stream_history',endTable:'s_c_stream'}]->(stream)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertStreamHistory" parameterType="com.datalink.fdop.stream.api.domain.StreamHistory" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:s_c_stream_history ${@com.datalink.fdop.stream.utils.DomainAgeUtils@getStreamHistoryAgeStr(streamHistory)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.stream.api.domain.StreamHistory" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_history)
        WHERE node.id = ${id}
        <set>
            <if test="sort != null">node.sort = ${sort},</if>
            <if test="jobId != null and jobId != ''">node.jobId = '${jobId}',</if>
            <if test="status != null and status != ''">node.status = '${status}',</if>
            <if test="errorInfo != null and errorInfo != ''">node.errorInfo = '${errorInfo}',</if>
            <if test="stream != null and stream != ''">node.stream = '${stream}',</if>
            <if test="taskInfo != null and taskInfo != ''">node.taskInfo = '${taskInfo}',</if>
            <if test="endTime != null">node.endTime = '${endTime}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_history)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteStreamAndStreamHistoryEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:s_c_stream_history_edge]->(node)-[nme2:s_c_stream_history_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_history)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_history) WHERE node.id = ${id} RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectHistoryListByStreamId"  resultType="com.datalink.fdop.stream.api.domain.StreamHistory">
        WITH overview as (
            SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream) -[:s_c_stream_history_edge]->(history:s_c_stream_history)
            <where>
                <if test="streamId != -1">AND history.streamId = ${streamId}</if>
                <if test="searchVo != null "> AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("history",searchVo)})</if>
            </where>
            RETURN history.id, history.sort, history.jobId, history.status, history.errorInfo, history.stream, history.taskInfo, history.createTime, history.endTime
        $$) as (id BIGINT, sort BIGINT, jobId TEXT, status TEXT, errorInfo TEXT, stream TEXT, taskInfo TEXT, createTime TEXT, endTime TEXT)
        )
        select * from overview ORDER BY createTime is null,sort desc,sort ${sort}
    </select>

    <select id="querySerialNumber" resultType="int">
        SELECT (count(1) + 1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_history) RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

</mapper> 