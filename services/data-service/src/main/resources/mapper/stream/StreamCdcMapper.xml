<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.stream.mapper.StreamCdcMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.stream.api.domain.StreamCdc"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="createStreamAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_stream_cdc_menu), (node:s_c_stream_cdc)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'s_c_stream_cdc_menu',endTable:'s_c_stream_cdc'}]->(node)-[:node_menu_edge
        {startTable:'s_c_stream_cdc',endTable:'s_c_stream_cdc_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertStream" parameterType="com.datalink.fdop.stream.api.domain.StreamCdc" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:s_c_stream_cdc ${@com.datalink.fdop.stream.utils.DomainAgeUtils@getStreamCdcAgeStr(stream)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.stream.api.domain.StreamCdc" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_cdc)
        WHERE node.id = ${id}
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="description != null and description != ''">node.description = '${description}',</if>
            <if test="baseTaskInfo != null and baseTaskInfo != ''">node.baseTaskInfo = '${baseTaskInfo}',</if>
            <if test="dorisConfig != null and dorisConfig != ''">node.dorisConfig = '${dorisConfig}',</if>
            <if test="taskInfo != null and taskInfo != ''">node.taskInfo = '${taskInfo}',</if>
            <if test="status != null">node.status = ${status},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_cdc)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteStreamAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_cdc)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_cdc) WHERE node.id = ${id} RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_cdc) WHERE node.code = '${code}' RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_cdc) WHERE node.code = '${code}' AND node.id &lt;&gt; ${id} RETURN id(node),
                               properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:s_c_stream_cdc_menu) -[nme:node_menu_edge]->(nodeE:s_c_stream_cdc)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>
    <select id="selectTree" resultType="com.datalink.fdop.stream.api.domain.StreamCdcTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_cdc)
        <where>
            <if test="code != null and code != ''">node.code =~'.*${code}.*'</if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, 'NODE' as menuType, node.type
        ORDER BY node.code ${sort}
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,type TEXT)
    </select>

    <select id="querySerialNumber" resultType="int">
        SELECT (count(1) + 1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_cdc) RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

</mapper> 