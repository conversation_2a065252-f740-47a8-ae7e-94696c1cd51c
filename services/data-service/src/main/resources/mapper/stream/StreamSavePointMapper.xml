<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.stream.mapper.StreamSavePointMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.stream.api.domain.StreamSavePoint"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="createStreamAndStreamSavePointEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (stream:s_c_stream), (node:s_c_stream_savepoint)
        WHERE stream.id = ${streamId}
        AND node.streamId = ${streamId} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (stream)-[nme:s_c_stream_savepoint_edge
        {startTable:'s_c_stream',endTable:'s_c_stream_savepoint'}]->(node)-[:s_c_stream_savepoint_edge
        {startTable:'s_c_stream_savepoint',endTable:'s_c_stream'}]->(stream)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertStreamSavePoint" parameterType="com.datalink.fdop.stream.api.domain.StreamSavePoint" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:s_c_stream_savepoint ${@com.datalink.fdop.stream.utils.DomainAgeUtils@getStreamSavePointAgeStr(streamSavePoint)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.stream.api.domain.StreamSavePoint" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_savepoint)
        WHERE node.id = ${id}
        <set>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="description != null and description != ''">node.description = '${description}',</if>
            <if test="savePointPath != null and savePointPath != ''">node.savePointPath = '${savePointPath}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_savepoint)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteStreamAndStreamSavePointEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:s_c_stream_savepoint_edge]->(node)-[nme2:s_c_stream_savepoint_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_stream_savepoint)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_savepoint) WHERE node.id = ${id} RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_savepoint) WHERE node.code = '${code}' RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="selectSavePointListByStreamId"  resultType="com.datalink.fdop.stream.api.domain.StreamSavePoint">
        WITH overview as (
            SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream) -[:s_c_stream_savepoint_edge]->(savepoint:s_c_stream_savepoint)
            <where>
                <if test="streamId != -1">AND savepoint.streamId = ${streamId}</if>
                <if test="searchVo != null "> AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("savepoint",searchVo)})</if>
            </where>
            RETURN savepoint.id, savepoint.sort, savepoint.code, savepoint.name, savepoint.description,savepoint.savePointPath,savepoint.createTime
        $$) as (id BIGINT, sort BIGINT, code TEXT, name TEXT, description TEXT, savePointPath TEXT,createTime TEXT)
        )
        select * from overview ORDER BY createTime is null,sort desc,code ${sort}
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_savepoint) WHERE node.code = '${code}' AND node.id &lt;&gt; ${id} RETURN id(node),
                               properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="querySerialNumber" resultType="int">
        SELECT (count(1) + 1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_stream_savepoint) RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

</mapper> 