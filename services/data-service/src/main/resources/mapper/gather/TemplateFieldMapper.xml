<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.gather.mapper.TemplateFieldMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.gather.api.domain.TemplateField"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <select id="del" resultType="int" >
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (f:g_c_template_field)-[r:g_c_template_field_edge]->(t:g_c_template)
        where t.id=${templateId} and f.id in [
        <foreach collection="fieldIds" separator="," item="fieldId">
            ${fieldId}
        </foreach>
          ]
        DETACH DELETE f,r
        RETURN id(f), properties(f)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="update" parameterType="com.datalink.fdop.gather.api.domain.TemplateField" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:g_c_template_field)
        where u.id=${id}
        <set>
            u.elementId =${elementId}
            <if test="elementId == null">
                null
            </if>
            ,
            <if test="fieldText != null and fieldText != ''">u.fieldText = '${fieldText}',</if>
            <if test="isMust != null">u.isMust = ${isMust},</if>
            <if test="isFilter != null">u.isFilter = ${isFilter},</if>
            <if test="relevanceId != null ">u.relevanceId = ${relevanceId},</if>
            <if test="rank != null ">u.rank = ${rank},</if>
            u.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            u.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="save" parameterType="com.datalink.fdop.gather.api.domain.TemplateField" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        CREATE (node:g_c_template_field ${@com.datalink.fdop.gather.utils.DomainAgeUtils@getTempalteFieldAgeStr(templateField)})
        RETURN id(node),properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createTemplateFieldEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field), (t:g_c_template)
        WHERE t.id = ${templateId}
        AND field.id = ${fieldId}
        CREATE (field)-[nme:g_c_template_field_edge
        {startTable:'g_c_template_field',endTable:'g_c_template'}]->(t)-[:g_c_template_field_edge
        {startTable:'g_c_template',endTable:'g_c_template_field'}]->(field)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="findByTemplateId" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH ()&lt;-[]-(f:g_c_template_field)-[r:g_c_template_field_edge]->(t:g_c_template)
        WHERE t.id = ${templateId}
        with f
        order by f.rank asc
        RETURN id(f), properties(f)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="findByTemplateIdPage" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH ()&lt;-[]-(f:g_c_template_field)-[r:g_c_template_field_edge]->(t:g_c_template)
        WHERE t.id = ${templateId}
        with f
        order by f.rank asc
        RETURN id(f), properties(f)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="saveStructureTemplateFieldEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field),(s:d_e_data_element)
        where field.id=${templateFieldId} and s.id=${relevanceId}
        CREATE (field)-[nme:g_c_template_field_structure_edge
        {startTable:'g_c_template_field',endTable:'d_e_data_element'}]->(s)-[:g_c_template_field_structure_edge
        {startTable:'d_e_data_element',endTable:'g_c_template_field'}]->(field)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="saveInputTemplateFieldEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field),(input:d_e_data_element_input)
        where field.id=${templateFieldId} and input.id=${relevanceId}
        CREATE (field)-[nme:g_c_template_field_input_edge
        {startTable:'g_c_template_field',endTable:'d_e_data_element'}]->(input)-[:g_c_template_field_input_edge
        {startTable:'d_e_data_element',endTable:'g_c_template_field'}]->(field)
        RETURN id(nme), properties(nme)
         $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="findFieldInfo" resultType="com.datalink.fdop.gather.api.domain.TemplateField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_structure_edge]->(s:d_e_data_element)
        where field.id=${id}
        RETURN distinct s.code,s.name,s.description,s.dataElementType ,s.fieldType,s.length,s.decimalLength,s.isPk,s.mapFieldName,s.id as relevanceId
         $$) as (code TEXT,name TEXT,description TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,mapFieldName TEXT,relevanceId BIGINT)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_input_edge]->(s:d_e_data_element_input)
        where field.id=${id}
        RETURN distinct  s.code,s.name,s.description,s.dataElementType ,s.fieldType,s.length,s.decimalLength,s.isPk,s.mapFieldName,s.id as relevanceId
         $$) as (code TEXT,name TEXT,description TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,mapFieldName TEXT,relevanceId BIGINT)
    </select>

    <select id="createFieldToEntityStructureEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field),(structure:d_e_data_entity_structure)
        where field.id=${id} and structure.id=${relevanceId}
        CREATE (field)-[nme:g_c_template_field_entity_structure_edge
        {startTable:'g_c_template_field',endTable:'d_e_data_entity_structure'}]->(structure)-[:g_c_template_field_entity_structure_edge
        {startTable:'d_e_data_entity_structure',endTable:'g_c_template_field'}]->(field)
        RETURN id(nme), properties(nme)
         $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    
    <select id="delStructureTemplateFieldEdge" resultType="int" >
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_structure_edge]-(s:d_e_data_element)
        where field.id=${id} and s.id=${relevanceId}
        delete r
        RETURN id(r), properties(r)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delInputTemplateFieldEdge" resultType="int" >
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_input_edge]-(input:d_e_data_element_input)
        where field.id=${id} and input.id=${relevanceId}
        delete r
        RETURN id(r), properties(r)
         $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="delFieldToEntityStructureEdge" resultType="int" >
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_entity_structure_edge]-(structure:d_e_data_entity_structure)
        where field.id=${id} and structure.id=${relevanceId}
        delete r
        RETURN id(r), properties(r)
         $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findEntityFieldInfo" resultType="com.datalink.fdop.gather.api.domain.TemplateField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_entity_structure_edge]->(s:d_e_data_entity_structure)
        where field.id=${id}
        RETURN s.code,s.name,s.description ,s.fieldType,s.length,s.decimalLength,s.isPk,s.seq,s.entityInsertType,'' as dataElementType ,s.id as relevanceId
         $$) as (code TEXT,name TEXT,description TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,entityInsertType TEXT,dataElementType TEXT,relevanceId BIGINT)
         union
         SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_entity_predefined_element_edge]->(s:d_e_data_element)
        where field.id=${id}
        RETURN s.code,s.name,s.description ,s.fieldType,s.length,s.decimalLength,s.isPk,s.seq,'PREDEFINED' as entityInsertType,s.dataElementType,s.id as relevanceId
         $$) as (code TEXT,name TEXT,description TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,entityInsertType TEXT,dataElementType TEXT,relevanceId BIGINT)
         union
         SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_entity_predefined_input_edge]->(s:d_e_data_element_input)
        where field.id=${id}
        RETURN s.code,s.name,s.description ,s.fieldType,s.length,s.decimalLength,s.isPk,s.seq,'PREDEFINED' as entityInsertType,s.dataElementType,s.id as relevanceId
         $$) as (code TEXT,name TEXT,description TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,entityInsertType TEXT,dataElementType TEXT,relevanceId BIGINT)
    </select>
    <select id="createFieldToEntityPredefinedElementEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field),(elem:d_e_data_element)
        where field.id=${id} and elem.id=${relevanceId}
        CREATE (field)-[nme:g_c_template_field_entity_predefined_element_edge
        {startTable:'g_c_template_field',endTable:'d_e_data_element'}]->(elem)-[:g_c_template_field_entity_predefined_element_edge
        {startTable:'d_e_data_element',endTable:'g_c_template_field'}]->(field)
        RETURN id(nme), properties(nme)
         $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delFieldToEntityPredefinedElementEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_entity_predefined_element_edge]-(elem:d_e_data_element)
        where field.id=${id} and elem.id=${relevanceId}
        delete r
        RETURN id(nme), properties(nme)
         $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createFieldToEntityPredefinedIuputEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field),(elem:d_e_data_element_input)
        where field.id=${id} and elem.id=${relevanceId}
        CREATE (field)-[nme:g_c_template_field_entity_predefined_input_edge
        {startTable:'g_c_template_field',endTable:'d_e_data_element_input'}]->(elem)-[:g_c_template_field_entity_predefined_input_edge
        {startTable:'d_e_data_element_input',endTable:'g_c_template_field'}]->(field)
        RETURN id(nme), properties(nme)
         $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delFieldToEntityPredefinedIuputEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (field:g_c_template_field)-[r:g_c_template_field_entity_predefined_input_edge]-(elem:d_e_data_element_input)
        where field.id=${id} and elem.id=${relevanceId}
        delete r
        RETURN id(r), properties(r)
         $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper> 