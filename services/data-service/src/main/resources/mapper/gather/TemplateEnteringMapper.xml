<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.gather.mapper.TemplateEnteringMapper">

    <resultMap id="vlabelItemMapping" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties"
                javaType="com.datalink.fdop.element.api.domain.DataEntityTableMapping"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="executeInsert" resultType="java.lang.Integer">
        ${sql}
    </select>

    <insert id="executeTmpInsert">
        ${sql}
    </insert>

    <update id="executeUpdate">
        ${sql}
    </update>

    <delete id="executeDelete">
        ${sql}
    </delete>

    <select id="executeSelect" resultType="java.util.Map">
        ${sql}
    </select>

    <select id="findMappingFieldInfo" resultMap="vlabelItemMapping">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (det:d_e_data_entity_table) -[]->(dem:d_e_data_entity_mapping)
            where det.id= ${id}
            RETURN id(dem), properties(dem)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="executeSelectCount" resultType="int">
        ${chenckSql}
    </select>

</mapper> 