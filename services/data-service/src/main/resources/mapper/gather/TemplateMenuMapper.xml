<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.gather.mapper.TemplateMenuMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.gather.api.domain.TemplateMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:g_c_template_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="deleteTempalteMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:g_c_template_menu) -[mme1:menu_menu_edge]-(menu:g_c_template_menu)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menu.id = ${pid}
        DELETE mme1 RETURN id(mme1),
        properties(mme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="updateById" parameterType="com.datalink.fdop.gather.api.domain.TemplateMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:g_c_template_menu)
        WHERE menu.id = ${id}
        <set>
            <if test="pid != null">menu.pid = ${pid},</if>
            <if test="code != null and code != ''">menu.code = '${code}',</if>
            <if test="name != null and name != ''">menu.name = '${name}',</if>
            <if test="serialNumber != null">menu.serialNumber = ${serialNumber},</if>
            <if test="description != null and description != ''">menu.description = '${description}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="insertElementMenu" parameterType="com.datalink.fdop.gather.api.domain.TemplateMenu" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$ CREATE (node:g_c_template_menu ${@com.datalink.fdop.gather.utils.DomainAgeUtils@getTempalteMenuAgeStr(tempalteMenu)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:g_c_template_menu) WHERE menu.code = '${code}' RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>
    <select id="createTempalteMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:g_c_template_menu), (menuE:g_c_template_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'g_c_template_menu',endTable:'g_c_template_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'g_c_template_menu',endTable:'g_c_template_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:g_c_template_menu) WHERE menu.id = ${id}
            RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:g_c_template_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id} RETURN id(menu),
                               properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:g_c_template_menu) WHERE menu.pid = ${pid}
             RETURN menu.pid
        $$) as ( pid BIGINT)
    </select>
    <select id="bacthUpdatePidById" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:g_c_template_menu)
        WHERE menu.id IN
        <foreach collection="menuIdList" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectMenuTree" resultType="com.datalink.fdop.gather.api.model.TemplateTree">
        SELECT *
        FROM (
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:g_c_template_menu) WHERE menu.pid = -1
        WITH DISTINCT (menu)
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType, menu.serialNumber
        $$) as (id ag_catalog.agtype,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType TEXT,serialNumber TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template) WHERE node.pid = -1
        <if test="code != null and code != ''">AND node.code =~'.*${code}.*'</if>
        <if test="publish !=null" >AND node.publish=${publish}</if>
        WITH DISTINCT (node)
        RETURN node.id, node.pid, node.code, node.name, node.description, 'NODE' as menuType, node.serialNumber
        $$) as (id ag_catalog.agtype, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT, serialNumber TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:g_c_template_menu) -[]->(menuE:g_c_template_menu)
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType, menuS.serialNumber
        $$) as (id ag_catalog.agtype, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT, serialNumber TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:g_c_template_menu) -[]->(nodeE:g_c_template)
        where 1=1
            <if test="code != null and code != ''">AND nodeE.code =~'.*${code}.*'</if>
            <if test="publish !=null" >AND nodeE.publish=${publish}</if>

        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType, menuS.serialNumber
        $$) as (id ag_catalog.agtype, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT, serialNumber TEXT)
        ) as zjdata_graph_table
        ORDER BY zjdata_graph_table.serialNumber ${sort}
    </select>
    <select id="selectByPid" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:g_c_template_menu) WHERE menu.id = ${pid} RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByPids" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:g_c_template_menu) -[mme:menu_menu_edge]->(menuE:g_c_template_menu)
        WHERE menuE.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN DISTINCT (menuE.id)
        $$) as (id ag_catalog.agtype)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:g_c_template_menu) -[mme:node_menu_edge]->(menuE:g_c_template)
        WHERE menuS.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN DISTINCT (menuS.id)
        $$) as (id ag_catalog.agtype)
    </select>
</mapper> 