<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.gather.mapper.TemplateMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.gather.api.domain.Template"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <resultMap id="vlabelItemEntityTable" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.element.api.domain.DataEntityTable"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <select id="delTemplate" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template)
        WHERE node.id in [
        <foreach collection="ids" item="id" separator=",">
            ${id}
        </foreach>
        ]
        DETACH DELETE node
        RETURN id(node),properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="updateTemplate" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:g_c_template)
        where u.id=${template.id}
        <set>
            <if test="template.pid != null">u.pid = ${template.pid},</if>
            <if test="template.name != null and template.name != ''">u.name = '${template.name}',</if>
            <if test="template.code != null  and template.code != ''">u.code = '${template.code}',</if>
            <if test="template.description != null and  template.description != ''">u.description = '${template.description}',</if>
            <if test="template.templateType != null ">u.templateType = '${template.templateType}',</if>
            <if test="template.elementId != null ">u.elementId = ${template.elementId},</if>
            <if test="template.entityId != null ">u.entityId = ${template.entityId},</if>
            <if test="template.processingMode != null ">u.processingMode = '${template.processingMode}',</if>
            <if test="template.entityTableId != null ">u.entityTableId = ${template.entityTableId},</if>
            <if test="template.publish != null ">u.publish = ${template.publish},</if>
            u.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            u.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delTemplateAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:g_c_template_menu) -[nme1:node_menu_edge]-(node:g_c_template)
        WHERE node.id =${id}
        DELETE nme1
        RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="insertTemplate" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$ CREATE (node:g_c_template ${@com.datalink.fdop.gather.utils.DomainAgeUtils@getTempalteAgeStr(template)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectByPid" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:g_c_template) WHERE menu.id = ${pid} RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:g_c_template) WHERE node.code = '${code}' RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>
    <select id="createTemplateAndMenuEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:g_c_template_menu), (node:g_c_template)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'g_c_template_menu',endTable:'g_c_template'}]->(node)-[:node_menu_edge
        {startTable:'g_c_template',endTable:'g_c_template_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectByCodeAndId" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:g_c_template) WHERE node.code = '${code}'
            and node.id &lt;&gt; ${id}
            RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="list" parameterType="com.datalink.fdop.gather.api.domain.Template" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:g_c_template) WHERE 1=1
            <if test="pid !=null"> and node.pid=${pid}</if>
            <if test="code !=null and code!=''"> and node.code Contains '${code}'</if>
            <if test="name !=null and name!=''"> and node.name Contains '${name}'</if>
            <if test="description !=null and description!=''"> and node.description Contains '${description}'</if>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="overview" parameterType="com.datalink.fdop.gather.api.domain.Template" resultType="com.datalink.fdop.gather.api.domain.Template">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template) WHERE node.pid = -1
        <if test="searchCondition != null ">AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
        </if>
        RETURN node.id, node.pid, node.code, node.name, node.description, '顶级菜单' as
        menuName, node.createTime, node.updateTime
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template) -[:node_menu_edge]->(menu:g_c_template_menu)
        <if test="searchCondition != null ">WHERE
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
        </if>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as menuName
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT)
    </select>
    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template) WHERE
        node.id =${id}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectIdsByPid" resultType="java.lang.Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:g_c_template) WHERE node.pid = ${pid}
             RETURN node.id
        $$) as ( id BIGINT)
    </select>
    <select id="batchUpdatePidById" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template)
        WHERE node.id IN
        <foreach collection="list" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectTemplateTree" resultType="com.datalink.fdop.gather.api.model.TemplateTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (nodeS:g_c_template) -[]->(menuE:g_c_template_menu)
        <where>
            <if test="code != null and code != ''">nodeS.code =~'.*${code}.*'</if>
            <if test="publish !=null" >nodeS.publish=${publish}</if>
        </where>
        RETURN nodeS.id, nodeS.pid, nodeS.code, nodeS.name, nodeS.description, 'NODE' as menuType
        ORDER BY nodeS.code ${sort}
        $$) as (id ag_catalog.agtype,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType TEXT)
    </select>
    <select id="createTemplateAndEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template), (entity:d_e_data_entity)
        WHERE node.id = ${id} AND entity.id = ${entityId}
        CREATE (node)-[edge:g_c_template_entity_edge
        {startTable:'g_c_template',endTable:'d_e_data_entity'}]->(entity)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createTemplateAndEntityTableEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template), (table:d_e_data_entity_table)
        WHERE node.id = ${id} AND table.id =${entityTableId}
        CREATE (node)-[nme:g_c_template_entity_table_edge
        {startTable:'g_c_template',endTable:'d_e_data_entity_table'}]->(table)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createTemplateAndDataElementEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:g_c_template), (elem:d_e_data_element)
                                   WHERE node.id = ${id} AND elem.id =${entityId}
                                   CREATE (node)-[nme:g_c_template_element_edge
                                   {startTable:'g_c_template', endTable:'d_e_data_element'}]->(elem)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delTemplateToEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template)-[edge:g_c_template_entity_edge]-(entity:d_e_data_entity)
        WHERE node.id = ${id}
        DELETE edge
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="delTemplateToEntityTableEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template)-[r:g_c_template_entity_table_edge]- (table:d_e_data_entity_table)
        WHERE node.id = ${id}
        DELETE  r
        RETURN id(r), properties(r)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delTemplateToElementEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template)-[r:g_c_template_element_edge]-(elem:d_e_data_element)
        WHERE node.id = ${id}
        DELETE  r
        RETURN id(r), properties(r)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="findEntityTable" resultMap="vlabelItemEntityTable">
         SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template)-[r:g_c_template_entity_table_edge]->(table:d_e_data_entity_table)
        WHERE node.id = ${templateId}
        RETURN id(table), properties(table)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="findNodeByMenuId" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template)&lt;-[r*1..10]-(menu:g_c_template_menu)
        where menu.pid=${pid}
        RETURN node.id
        $$) as (id BIGINT)
    </select>
    <select id="selectAllTemplate" resultType="com.datalink.fdop.gather.api.domain.Template">
        <if test="pids.contains(-1L)">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:g_c_template)
            WHERE node.pid = -1
            <if test="publish !=null" >and node.publish=${publish}</if>
            <if test="searchCondition != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,'顶级菜单' as
            menuName,node.createTime,node.updateTime,node.templateType,node.elementId,node.entityId,
            node.processingMode,node.entityTableId,node.publish
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime
            TEXT,updateTime TEXT,templateType TEXT,elementId BIGINT,entityId BIGINT,processingMode TEXT,
            entityTableId TEXT,publish TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:g_c_template) -[:node_menu_edge]->(menu:g_c_template_menu)
        WHERE node.pid in
        <foreach collection="pids" item="pid" open="[" separator="," close="]">
            ${pid}
        </foreach>
        <if test="publish !=null" >and node.publish=${publish}</if>
        <if test="searchCondition != null ">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
        </if>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.createTime,node.updateTime,node.templateType,node.elementId,node.entityId,
        node.processingMode,node.entityTableId,node.publish
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime TEXT,updateTime
        TEXT,templateType TEXT,elementId BIGINT,entityId BIGINT,processingMode TEXT,
        entityTableId TEXT,publish TEXT)
    </select>
    <select id="findEntityCode" resultType="java.lang.String">
         SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity)
        where node.id=${entityId}
        RETURN node.code
        $$) as (code TEXT)
    </select>
    <select id="findElementCode" resultType="java.lang.String" >
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element)
        where node.id=${elementId}
        RETURN node.code
        $$) as (code TEXT)
    </select>
    <select id="findEntityTableCode" resultType="java.lang.String" >
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity_table)
        where node.id=${entityTableId}
        RETURN node.code
        $$) as (code TEXT)
    </select>
</mapper> 