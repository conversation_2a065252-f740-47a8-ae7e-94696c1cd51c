<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.gather.mapper.GatherLogMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.gather.api.domain.GatherLog"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insertLog" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:g_c_gather_log ${@com.datalink.fdop.gather.utils.DomainAgeUtils@getGatherLogAgeStr(gatherLog)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createTemplateAndLogEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:g_c_gather_log), (template:g_c_template)
                                   WHERE log.logId = ${logId} AND template.id = ${templateId}
                                   CREATE (log)-[edge:g_c_log_template_edge
                                   {startTable:'g_c_gather_log', endTable:'g_c_template'}]->(template)-[:g_c_log_template_edge
        {startTable:'g_c_template',endTable:'g_c_gather_log'}]->(log)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.gather.api.domain.GatherLog" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:g_c_gather_log)
        WHERE log.logId = ${logId}
        <set>
            <if test="gatherLogStatus != null">log.gatherLogStatus = '${gatherLogStatus}',</if>
            <if test="log != null and log != ''">log.log = '${log}',</if>
            <if test="endTime != null">log.endTime =
                '${@com.datalink.fdop.common.core.utils.DateUtils@getTime(endTime)}',
            </if>
        </set>
        RETURN id(log), properties(log)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateEndTimeAndStatus" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:g_c_gather_log) where log.logId = ${logId}
            SET log.endTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}',
                               log.gatherLogStatus = '${gatherLogStatus}' RETURN id(log), properties(log)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateLog" parameterType="com.datalink.fdop.gather.api.domain.GatherLog" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:g_c_gather_log) where log.logId =${logId}
            SET log.log = '${log}'
            RETURN id(log), properties(log)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteLog" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:g_c_gather_log) -[edge:g_c_log_template_edge]-(template:g_c_template)
            WHERE template.id = ${templateId}
            DETACH DELETE edge, log RETURN id(log),
                               properties(log) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:g_c_gather_log) WHERE log.logId = ${logId}
            RETURN id(log), properties(log)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectList" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:g_c_gather_log) -[edge:g_c_log_template_edge]->(template:g_c_template)
            WHERE template.id = ${templateId}
            WITH log
            ORDER BY log.startTime DESC
            RETURN id(log), properties(log)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper> 