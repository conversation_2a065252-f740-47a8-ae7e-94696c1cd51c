<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataElementStructureMapper">

    <select id="selectByElementId" resultType="com.datalink.fdop.element.api.domain.DataElementStructure">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (deS:d_e_data_element) -[eee:d_e_element_element_edge]->(deE:d_e_data_element)
        WHERE deS.id = ${dataElementId} AND eee.father IS NULL
        <if test="fieldType != null">AND deE.fieldType = '${fieldType}'</if>
        <if test="length != null">AND deE.length = ${length}</if>
        <if test="decimalLength != null">AND deE.decimalLength = ${decimalLength}</if>
        RETURN deE.id, deE.code, deE.name, deE.description, deE.dataElementType, deE.fieldType, deE.length,
        deE.decimalLength, deE.isPk, deE.mapFieldName, eee.seq,
        deE.createTime,
        deE.updateTime
        $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,dataElementType TEXT,fieldType TEXT,length
        BIGINT,decimalLength BIGINT,isPk BOOLEAN,mapFieldName TEXT,seq INT2,createTime TEXT,updateTime TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (deS:d_e_data_element) -[eie:d_e_element_input_edge]->(deiE:d_e_data_element_input)
        WHERE deS.id = ${dataElementId}
        <if test="fieldType != null">AND deiE.fieldType = '${fieldType}'</if>
        <if test="length != null">AND deiE.length = ${length}</if>
        <if test="decimalLength != null">AND deiE.decimalLength = ${decimalLength}</if>
        RETURN deiE.id, deiE.code, deiE.name, deiE.description, deiE.dataElementType, deiE.fieldType,
        deiE.length,
        deiE.decimalLength, deiE.isPk, deiE.mapFieldName, eie.seq,
        deiE.createTime,
        deiE.updateTime
        $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,dataElementType TEXT,fieldType TEXT,length
        BIGINT,decimalLength BIGINT,isPk BOOLEAN,mapFieldName TEXT,seq INT2,createTime TEXT,updateTime TEXT)
    </select>

    <select id="selectMainFatherById" resultType="com.datalink.fdop.element.api.domain.DataElementStructure">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (deS:d_e_data_element) -[eee:d_e_element_element_edge]->(deE:d_e_data_element)
        WHERE deS.id = ${dataElementId}
        AND deE.dataElementType = 'MAIN'
        AND eee.son = ${dataElementId}
        AND deE.id &lt;&gt; ${dataElementId}
        <if test="fieldType != null">AND deE.fieldType = '${fieldType}'</if>
        <if test="length != null">AND deE.length = ${length}</if>
        <if test="decimalLength != null">AND deE.decimalLength = ${decimalLength}</if>
        WITH deE, eee ORDER BY eee.seq asc
        RETURN deE.id, deE.code, deE.name, deE.description, deE.dataElementType, deE.fieldType,
        deE.length,
        deE.decimalLength, deE.isPk, deE.mapFieldName, deE.createTime,
        deE.updateTime, eee.father, eee.son, eee.seq
        $$) as (id BIGINT,code TEXT
        ,name TEXT
        ,description TEXT
        ,dataElementType TEXT
        ,fieldType TEXT
        ,length BIGINT
        ,decimalLength BIGINT
        ,isPk BOOLEAN
        ,mapFieldName TEXT
        ,createTime TEXT
        ,updateTime TEXT
        ,father BIGINT
        ,son BIGINT
        ,seq BIGINT)
    </select>

    <select id="insertStructure" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (deS:d_e_data_element) WHERE deS.id = ${dataElementId}
        <foreach collection="structureList" item="structure">
            <if test="structure.dataElementType.toString() != 'INPUT'.toString()">
                WITH deS
                MATCH (deE:d_e_data_element) WHERE deE.id = ${structure.id}
                CREATE (deS)-[:d_e_element_element_edge{
                startTable:'d_e_data_element',
                endTable:'d_e_data_element',
                isPk:${structure.isPk},
                seq:${structure.seq},
                type:'${structure.dataElementType}'
                <if test="structure.dataElementType.toString() == 'MAIN'.toString()">
                    ,father: ${structure.id}
                    ,son: ${dataElementId}
                </if>
                }]->(deE)-[:d_e_element_element_edge{
                startTable:'d_e_data_element',
                endTable:'d_e_data_element',
                isPk:${structure.isPk},
                seq:${structure.seq},
                type:'${structure.dataElementType}'
                <if test="structure.dataElementType.toString() == 'MAIN'.toString()">
                    ,father: ${structure.id}
                    ,son: ${dataElementId}
                </if>
                }]->(deS)
            </if>
        </foreach>
        WITH deS
        <foreach collection="structureList" item="structure">
            <if test="structure.dataElementType.toString() == 'INPUT'.toString()">
                CREATE (deS)-[:d_e_element_input_edge
                {startTable:'d_e_data_element',endTable:'d_e_data_element_input',isPk:${structure.isPk},seq:${structure.seq},type:'${structure.dataElementType}'}
                ]->(:d_e_data_element_input
                ${@com.datalink.fdop.element.utils.DomainAgeUtils@getStructureAgeStr(structure)})
            </if>
        </foreach>
        RETURN id(deS), properties(deS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateStructure" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (deS:d_e_data_element) WHERE deS.id = ${dataElementId}
        <foreach collection="structureList" item="structure">
            WITH deS
            <if test="structure.dataElementType.toString() != 'INPUT'.toString()">
                MATCH
                (deS)-[eeeS:d_e_element_element_edge]->(deE:d_e_data_element)-[eeeE:d_e_element_element_edge]->(deS)
                WHERE deE.id = ${structure.id}
                SET eeeS.startTable = 'd_e_data_element',
                eeeS.endTable = 'd_e_data_element',
                eeeE.startTable = 'd_e_data_element',
                eeeE.endTable = 'd_e_data_element',
                eeeS.isPk = ${structure.isPk},
                eeeE.isPk = ${structure.isPk},
                eeeS.seq = ${structure.seq},
                eeeE.seq = ${structure.seq},
                deE.length = ${structure.length},
                deE.decimalLength = ${structure.decimalLength},
                deE.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
                deE.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
            </if>
            <if test="structure.dataElementType.toString() == 'INPUT'.toString()">
                MATCH
                (deS)-[eei:d_e_element_input_edge]->(dei:d_e_data_element_input)
                WHERE dei.id = ${structure.id}
                SET eei.seq = ${structure.seq}, dei.code = '${structure.code}', dei.name = '${structure.name}',
                dei.description = '${structure.description}', dei.fieldType = '${structure.fieldType}',dei.length =
                ${structure.length},dei.decimalLength = ${structure.decimalLength},
                dei.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
                dei.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
            </if>
        </foreach>
        RETURN id(deS), properties(deS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="isMainStructureExist" resultType="Boolean">
        WITH count as (select count(*)
                       from ag_catalog.cypher('zjdata_graph', $$
                           MATCH (deS:d_e_data_element) -[:d_e_element_element_edge]->(deE:d_e_data_element)
                           WHERE deS.id = ${dataElementId} AND deE.id = ${mainStructureId}
                           RETURN deE.id
                           $$) as (id BIGINT)
            )
        select case when count = 0 then false else true end
        from count
    </select>

    <select id="deleteStructure" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (deS:d_e_data_element) WHERE deS.id = ${dataElementId}
        <foreach collection="structureList" item="structure">
            WITH deS
            <if test="structure.dataElementType.toString() != 'INPUT'.toString()">
                MATCH
                (deS)-[eeeS:d_e_element_element_edge]->(deE:d_e_data_element)-[eeeE:d_e_element_element_edge]->(deS)
                WHERE deE.id = ${structure.id}
                DELETE eeeS, eeeE
            </if>
            <if test="structure.dataElementType.toString() == 'INPUT'.toString()">
                MATCH
                (deS)-[eei:d_e_element_input_edge]->(dei:d_e_data_element_input)
                WHERE dei.id = ${structure.id}
                DETACH DELETE eei, dei
            </if>
        </foreach>
        RETURN id(deS), properties(deS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectMenuAndElementAndFieldList"
            resultType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo">
        select *
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (deS:d_e_data_element) WHERE deS.id = ${dataElementId}
        <foreach collection="structureList" item="structure">
            WITH deS
            <if test="structure.dataElementType.toString() != 'INPUT'.toString()">
                MATCH
                (deS)-[eeeS:d_e_element_element_edge]->(deE:d_e_data_element)-[eeeE:d_e_element_element_edge]->(deS)
                WHERE deE.id = ${structure.id}
                DELETE eeeS, eeeE
            </if>
            <if test="structure.dataElementType.toString() == 'INPUT'.toString()">
                MATCH
                (deS)-[eei:d_e_element_input_edge]->(dei:d_e_data_element_input)
                WHERE dei.id = ${structure.id}
                DETACH DELETE eei, dei
            </if>
        </foreach>
        RETURN id(deS), properties(deS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteStructureTemplate" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (deS:d_e_data_element)
        <foreach collection="structureList" item="structure">
            WITH deS
            <if test="structure.dataElementType.toString() != 'INPUT'.toString()">
                MATCH
                (deS)-[r:g_c_template_field_structure_edge]-()
                where des.id=${structure.id}
                DELETE r
            </if>
            <if test="structure.dataElementType.toString() == 'INPUT'.toString()">
                MATCH
                (deS)-[r:g_c_template_field_input_edge]-()
                where des.id=${structure.id}
                DELETE r
            </if>
        </foreach>
        RETURN id(deS), properties(deS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectElementAllMinSeq" resultType="Integer">
        WITH seq as (
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (deS:d_e_data_element) -[edge:d_e_element_element_edge]->(deE:d_e_data_element)
                WHERE deS.id = ${dataElementId} AND deE.isPk = ${isPk}
                RETURN MIN (edge.seq)
                $$) as (seq INT2)
        UNION
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (element:d_e_data_element) -[edge:d_e_element_input_edge]->(input :d_e_data_element_input)
            WHERE element.id = ${dataElementId} AND input.isPk = ${isPk}
            RETURN MIN (edge.seq)
            $$) as (seq INT2)
        )
        select case when seq is null then 0 else seq end
        from seq
    </select>

    <select id="selectElementAllMaxSeq" resultType="Integer">
        WITH seq as (
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (deS:d_e_data_element) -[edge:d_e_element_element_edge]->(deE:d_e_data_element)
                WHERE deS.id = ${dataElementId} AND deE.isPk = ${isPk}
                RETURN MAX (edge.seq)
                $$) as (seq INT2)
        UNION
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (element:d_e_data_element) -[edge:d_e_element_input_edge]->(input :d_e_data_element_input)
            WHERE element.id = ${dataElementId} AND input.isPk = ${isPk}
            RETURN MAX (edge.seq)
            $$) as (seq INT2)
        )
        select case when seq is null then 0 else seq end
        from seq
    </select>

</mapper> 