<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.GraphTableMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.element.api.domain.DataElement"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="isExistsTable" resultType="Boolean">
        select case
                   when (select table_name
                         FROM information_schema.tables
                         where table_schema = 'zjdata_element_graph'
                           and table_name = #{tableName}) is null then false
                   else true end
    </select>

    <select id="createGraphTable" resultType="Map">
        select ag_catalog.create_vlabel('zjdata_element_graph', '${tableName}')
    </select>

    <select id="deleteGraphTable" parameterType="String" resultType="String">
        select drop_label('zjdata_element_graph', '${tableName}');
    </select>

    <select id="getData" resultType="Map">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_element_graph', $$
        MATCH (table :${mapTableName})
        RETURN
        <foreach collection="dataElementStructureList" item="dataElementStructure" separator=",">
            table.${dataElementStructure.mapFieldName} as ${dataElementStructure.code}
        </foreach>
        $$) as
        <foreach collection="dataElementStructureList" item="dataElementStructure" open="(" separator="," close=")">
            ${dataElementStructure.code} TEXT
        </foreach>
    </select>

    <select id="getDataCount" resultType="int">
        SELECT count(1)
        FROM
        ag_catalog.cypher('zjdata_element_graph', $$
        MATCH (table :${mapTableName})
        RETURN
        <foreach collection="dataElementStructureList" item="dataElementStructure" separator=",">
            table.${dataElementStructure.mapFieldName} as ${dataElementStructure.code}
        </foreach>
        $$) as
        <foreach collection="dataElementStructureList" item="dataElementStructure" open="(" separator="," close=")">
            ${dataElementStructure.code} TEXT
        </foreach>
    </select>

</mapper> 