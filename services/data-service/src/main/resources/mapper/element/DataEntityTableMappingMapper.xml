<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataEntityTableMappingMapper">

    <select id="deleteMapping" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (dem:d_e_data_entity_table_mapping) WHERE dem.dataTableId = ${tableId}
            DETACH DELETE dem
            RETURN id(dem), properties(dem) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchDeleteMapping" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (dem:d_e_data_entity_table_mapping)
        WHERE dem.dataTableId IN
        <foreach collection="tableIdList" item="tableId" open="[" separator="," close="]">
            ${tableId}
        </foreach>
        DETACH DELETE dem
        RETURN id(dem), properties(dem) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertCustomizeMapping" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (det:d_e_data_entity_table)
        WHERE det.id = ${tableId}
        <foreach collection="dataEntityTableMappingList" item="dataEntityTableMapping">
            CREATE (det)-[:d_e_data_entity_table_mapping_edge
            {startTable:'d_e_data_entity_table',endTable:'d_e_data_entity_table_mapping'}]->
            (:d_e_data_entity_table_mapping
            ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityTableMappingAgeStr(dataEntityTableMapping)})-
            [:d_e_data_entity_table_mapping_edge{startTable:'d_e_data_entity_table_mapping',endTable:'d_e_data_entity_table'}]->(det)
            WITH det
            MATCH (des:d_e_data_entity_structure)
            WHERE des.id = ${dataEntityTableMapping.fieldId}
            CREATE (des)-[:d_e_data_entity_structure_mapping_edge
            {startTable:'d_e_data_entity_structure',endTable:'d_e_data_entity_table_mapping'}]->
            (:d_e_data_entity_table_mapping
            ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityTableMappingAgeStr(dataEntityTableMapping)})-
            [:d_e_data_entity_structure_mapping_edge
            {startTable:'d_e_data_entity_table_mapping',endTable:'d_e_data_entity_structure'}]->(des)
        </foreach>
        RETURN id(det), properties(det) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertPredefinedMapping" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (det:d_e_data_entity_table)
        WHERE det.id = ${tableId}
        <foreach collection="dataEntityTableMappingList" item="dataEntityTableMapping">
            CREATE (det)-[:d_e_data_entity_table_mapping_edge
            {startTable:'d_e_data_entity_table',endTable:'d_e_data_entity_table_mapping'}]->
            (:d_e_data_entity_table_mapping
            ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityTableMappingAgeStr(dataEntityTableMapping)})-
            [:d_e_data_entity_table_mapping_edge{startTable:'d_e_data_entity_table_mapping',endTable:'d_e_data_entity_table'}]->(det)
            <if test="dataEntityTableMapping.dataElementType != null">
                <if test="dataEntityTableMapping.dataElementType.toString() == 'MAIN'.toString()">
                    WITH det
                    MATCH (de:d_e_data_element)
                    WHERE de.id = ${dataEntityTableMapping.fieldId}
                    CREATE (de)-[:d_e_data_entity_element_mapping_edge
                    {startTable:'d_e_data_entity_table_mapping',endTable:'d_e_data_element'}]->
                    (:d_e_data_entity_table_mapping
                    ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityTableMappingAgeStr(dataEntityTableMapping)})-
                    [:d_e_data_entity_element_mapping_edge{startTable:'d_e_data_element',endTable:'d_e_data_entity_table_mapping'}]->(de)
                </if>
                <if test="dataEntityTableMapping.dataElementType.toString() == 'FIELD'.toString()">
                    WITH det
                    MATCH (de:d_e_data_element)
                    WHERE de.id = ${dataEntityTableMapping.fieldId}
                    CREATE (de)-[:d_e_data_entity_element_mapping_edge
                    {startTable:'d_e_data_entity_table_mapping',endTable:'d_e_data_element'}]->
                    (:d_e_data_entity_table_mapping
                    ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityTableMappingAgeStr(dataEntityTableMapping)})-
                    [:d_e_data_entity_element_mapping_edge{startTable:'d_e_data_element',endTable:'d_e_data_entity_table_mapping'}]->(de)
                </if>
                <if test="dataEntityTableMapping.dataElementType.toString() == 'INPUT'.toString()">
                    WITH det
                    MATCH (dei:d_e_data_element_input)
                    WHERE dei.id = ${dataEntityTableMapping.fieldId}
                    CREATE (dei)-[:d_e_data_entity_input_mapping_edge
                    {startTable:'d_e_data_entity_table_mapping',endTable:'d_e_data_element_input'}]->
                    (:d_e_data_entity_table_mapping
                    ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityTableMappingAgeStr(dataEntityTableMapping)})-
                    [:d_e_data_entity_input_mapping_edge{startTable:'d_e_data_element_input',endTable:'d_e_data_entity_table_mapping'}]->(dei)
                </if>
            </if>
        </foreach>
        RETURN id(det), properties(det) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper> 