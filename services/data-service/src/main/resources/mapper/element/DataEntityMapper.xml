<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataEntityMapper">

    <sql id="entity">
        RETURN ${alias}.id, ${alias}.pid,
            ${alias}.code,
            ${alias}.name,
            ${alias}.entityType,
            ${alias}.tagId,
            ${alias}.description,
            ${alias}.tableId,
            ${alias}.createTime,
            ${alias}.updateTime
            $$) as (id BIGINT, pid BIGINT,
            code TEXT,
            name TEXT,
            entityType TEXT,
            tagId BIGINT,
            description TEXT,
            tableId BIGINT,
            createTime TEXT,
            updateTime TEXT)
    </sql>

    <select id="createEntityAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu), (node:d_e_data_entity)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_e_data_entity_menu',endTable:'d_e_data_entity'}]->(node)-[:node_menu_edge
        {startTable:'d_e_data_entity',endTable:'d_e_data_entity_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertEntity" parameterType="com.datalink.fdop.element.api.domain.DataEntity" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_e_data_entity ${@com.datalink.fdop.element.utils.DomainAgeUtils@getDataEntityAgeStr(dataEntity)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.element.api.domain.DataEntity" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity)
        WHERE node.id = ${id}
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null">node.name = '${name}',</if>
            <if test="description != null">node.description = '${description}',</if>
            <if test="tableId != null">node.tableId = '${tableId}',</if>
            <if test="tagId != null">node.tagId = ${tagId},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchUpdatePidById" parameterType="com.datalink.fdop.element.api.domain.DataElementMenu"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteEntityAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity) WHERE node.id = ${id}
        <include refid="entity">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="selectEntityListByTagId" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity) WHERE node.tagId = ${tagId}
        <include refid="entity">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity) WHERE node.code = '${code}'
        <include refid="entity">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity) WHERE node.code = '${code}' AND node.id &lt;&gt; ${id}
        <include refid="entity">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.element.api.domain.DataEntity" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity)
        <where>
            <if test="dataEntity.pid != null">AND node.pid = ${dataEntity.pid},</if>
            <if test="dataEntity.code != null and dataEntity.code != ''">AND node.code =~'.*${dataEntity.code}.*'</if>
            <if test="dataEntity.name != null and dataEntity.name != ''">AND node.name =~'.*${dataEntity.name}.*'</if>
            <if test="dataEntity.description != null and dataEntity.description != ''">AND node.description
                =~'.*${dataEntity.description}.*'
            </if>
        </where>
        <include refid="entity">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="dwdOrDimList" parameterType="com.datalink.fdop.element.api.domain.DataEntity" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity)
        <where>
            <if test="dataEntity.pid != null">AND node.pid = ${dataEntity.pid},</if>
            <if test="dataEntity.code != null and dataEntity.code != ''">AND node.code =~'.*${dataEntity.code}.*'</if>
            <if test="dataEntity.name != null and dataEntity.name != ''">AND node.name =~'.*${dataEntity.name}.*'</if>
            <if test="dataEntity.description != null and dataEntity.description != ''">AND node.description
                =~'.*${dataEntity.description}.*'
            </if>
        </where>
        <include refid="entity">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="selectTotal" resultType="int">
        SELECT count(0)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_e_data_entity) RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectNodeTree" resultType="com.datalink.fdop.element.api.model.DataEntityTree">
        WITH overview as (
            SELECT node.*,entityTable.isRead as isRead,entityTable.dataSourceId as dataSourceId
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_e_data_entity)-[dete:d_e_data_entity_table_edge]->(det:d_e_data_entity_table)
            <where>
                <if test="code != null and code != ''">and toLower(node.code) =~'.*${code}.*'</if>
                and (node.entityType is null or (node.entityType &lt;&gt; 'DWD' and node.entityType &lt;&gt; 'DIM'))
            </where>
            RETURN node.id, node.pid, node.code, node.name, node.description,node.tagId,'NODE' as
            menuType,node.tableId, node.entityType
            $$) as node (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,tagId BIGINT,menuType TEXT,tableId
            BIGINT, entityType TEXT)
            left join ag_catalog.cypher('zjdata_graph', $$ MATCH (entityTable:d_e_data_entity_table) RETURN entityTable.dataEntityId,entityTable.isRead,entityTable.dataSourceId $$) as entityTable (dataEntityId BIGINT,isRead BOOLEAN,dataSourceId BIGINT) on node.id = entityTable.dataEntityId
        )
        select * from overview
        <where>
            <if test="dataSourceId != null">and dataSourceId = ${dataSourceId}</if>
        </where>
        ORDER BY code ${sort}
    </select>

    <select id="selectEntityTableTree" resultType="com.datalink.fdop.element.api.model.DataEntityTree">
        SELECT distinct *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuE:d_e_data_entity_menu) -[]->(nodeS:d_e_data_entity)-[]->(table:d_e_data_entity_table)
        <where>
            <if test="isRead != null">or table.isRead = ${isRead}</if>
            <if test="code != null and code != ''">and toLower(nodeS.code) =~'.*${code}.*'</if>
        </where>
        RETURN nodeS.id, nodeS.pid, nodeS.code, nodeS.name, nodeS.description,nodeS.tagId, 'NODE' as
        menuType,nodeS.tableId
        ORDER BY nodeS.code ${sort}
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,tagId BIGINT,menuType TEXT,tableId
        BIGINT)
    </select>

    <select id="overview" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        WITH overview as (
        <if test="pid == -1">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_e_data_entity)
            WHERE node.pid = -1
            and (node.entityType is null or(node.entityType &lt;&gt; 'DWD' and node.entityType &lt;&gt; 'DIM'))
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,node.tagId,node.l3Id, '顶级菜单' as
            menuName,'topMenu' as menuCode,node.createTime,node.updateTime,node.tableId, node.entityType
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,tagId BIGINT,l3Id BIGINT, menuName TEXT,menuCode TEXT,createTime
            TEXT,updateTime TEXT,tableId BIGINT, entityType TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity) -[:node_menu_edge]->(menu:d_e_data_entity_menu)
        <where>
            and (node.entityType is null or (node.entityType &lt;&gt; 'DWD' and node.entityType &lt;&gt; 'DIM'))
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description,node.tagId,node.l3Id, menu.name as
        menuName,menu.code as menuCode,node.createTime,node.updateTime,node.tableId, node.entityType
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,tagId BIGINT,l3Id BIGINT, menuName TEXT,menuCode TEXT,createTime TEXT,updateTime
        TEXT,tableId BIGINT, entityType TEXT)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu) -[mme:menu_menu_edge]->(node:d_e_data_entity_menu)
        <where>
            and (node.entityType = 'DWD' or node.entityType = 'DIM')
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description,node.tagId,node.l3Id, menu.name as
        menuName,menu.code as menuCode,node.createTime,node.updateTime,node.tableId, node.entityType
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,tagId BIGINT,l3Id BIGINT, menuName TEXT,menuCode TEXT,createTime TEXT,updateTime
        TEXT,tableId BIGINT, entityType TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>

    <select id="list" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity) -[:node_menu_edge]->(menu:d_e_data_entity_menu)
        <where>
            and (node.entityType = 'DWD' or node.entityType = 'DIM')
            and node.pid = ${dataEntityId}
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.createTime,node.updateTime,node.tableId, node.entityType
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime TEXT,updateTime
        TEXT,tableId BIGINT, entityType TEXT)
    </select>

    <select id="selectEntityTableById" resultType="com.datalink.fdop.element.api.domain.DataEntityTable">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_table_edge]->(table :d_e_data_entity_table)
            WHERE entity.id = ${id}
            RETURN table.id, table.dataEntityId, table.code, table.name, table.description, table.entityTableType,
                               table.dataSourceId, table.dataBaseName, table.tableName, table.isRead,
                               table.createTableWay,
                               table.createTablesql, table.isCreateTable, table.status
                                   $$) as (id BIGINT,dataEntityId BIGINT,code TEXT,name TEXT,description TEXT,entityTableType TEXT,dataSourceId BIGINT,dataBaseName TEXT,tableName TEXT,isRead BOOLEAN,
                                   createTableWay TEXT,createTablesql TEXT,isCreateTable TEXT,status BOOLEAN)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_e_data_entity_menu) -[nme:node_menu_edge]->(nodeE:d_e_data_entity)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="selectEntityAndElementRef" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (element:d_e_data_element) -[edge]->(entity:d_e_data_entity)
            WHERE element.id = ${dataElementId}
            RETURN entity.id, entity.pid, entity.code, entity.name, entity.description, entity.tableId
                                   $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,tableId BIGINT)
    </select>

    <select id="selectEntityAndTaskEdge" resultType="com.datalink.fdop.element.model.dto.DataEntityTaskDto">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_e_data_entity) -[edge]->(task:t_ds_task_definition) WHERE entity.id = ${dataEntityId}
            RETURN task.pid, task.code, task.name, task.nameDesc, task.description, task.taskType,
                               task.flag,
                               task.taskPriority,
                               task.workerGroup, task.environmentCode, task.failRetryTimes, task.failRetryInterval,
                               task.timeoutFlag,
                               task.timeoutNotifyStrategy, task.timeout, task.delayTime,
                               task.resourceIds,task.etlStatus $$) as (pid TEXT, code BIGINT, name TEXT, nameDesc TEXT, description TEXT, taskType TEXT, flag TEXT, taskPriority TEXT,
                               workerGroup TEXT, environmentCode BIGINT, failRetryTimes BIGINT, failRetryInterval BIGINT, timeoutFlag TEXT,
                               timeoutNotifyStrategy TEXT, timeout BIGINT, delayTime BIGINT,
                               resourceIds TEXT,etlStatus BIGINT)
    </select>


    <select id="selectDataEntityList" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity)
        <where>
            <if test="ids != null and ids.size > 0">and node.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
            </if>
        </where>
        <include refid="entity">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="overviewNoPage" resultType="com.datalink.fdop.element.api.domain.DataEntity">
        WITH overview as (
        <if test="pid == -1">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_e_data_entity)
            WHERE node.pid = -1
            and (node.entityType is null or(node.entityType &lt;&gt; 'DWD' and node.entityType &lt;&gt; 'DIM'))
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,node.tagId,node.l3Id, '顶级菜单' as
            menuName,node.createTime,node.updateTime,node.tableId, node.entityType
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,tagId BIGINT,l3Id BIGINT, menuName TEXT,createTime
            TEXT,updateTime TEXT,tableId BIGINT, entityType TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity) -[:node_menu_edge]->(menu:d_e_data_entity_menu)
        <where>
            and (node.entityType is null or (node.entityType &lt;&gt; 'DWD' and node.entityType &lt;&gt; 'DIM'))
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description,node.tagId,node.l3Id, menu.code as
        menuName,node.createTime,node.updateTime,node.tableId, node.entityType
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,tagId BIGINT,l3Id BIGINT, menuName TEXT,createTime TEXT,updateTime
        TEXT,tableId BIGINT, entityType TEXT)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_entity_menu) -[mme:menu_menu_edge]->(node:d_e_data_entity_menu)
        <where>
            and (node.entityType = 'DWD' or node.entityType = 'DIM')
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description,node.tagId,node.l3Id, menu.code as
        menuName,node.createTime,node.updateTime,node.tableId, node.entityType
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,tagId BIGINT,l3Id BIGINT, menuName TEXT,createTime TEXT,updateTime
        TEXT,tableId BIGINT, entityType TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>

</mapper> 