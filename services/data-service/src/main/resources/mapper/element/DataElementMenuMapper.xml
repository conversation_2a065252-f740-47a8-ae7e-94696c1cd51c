<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataElementMenuMapper">

    <sql id="elementMenu">
        RETURN ${alias}.id, ${alias}.pid,
            ${alias}.code,
            ${alias}.name,
            ${alias}.description,
            ${alias}.createTime,
            ${alias}.updateTime
            $$) as (id BIGINT, pid BIGINT,
            code TEXT,
            name TEXT,
            description TEXT,
            createTime TEXT,
            updateTime TEXT)
    </sql>

    <select id="createElementMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_e_data_element_menu), (menuE:d_e_data_element_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'d_e_data_element_menu',endTable:'d_e_data_element_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'d_e_data_element_menu',endTable:'d_e_data_element_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertElementMenu" parameterType="String" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:d_e_data_element_menu ${@com.datalink.fdop.element.utils.DomainAgeUtils@getDataElementMenuAgeStr(dataElementMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.element.api.domain.DataElementMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu)
        WHERE menu.id = ${id}
        <set>
            <if test="pid != null">menu.pid = ${pid},</if>
            <if test="code != null and code != ''">menu.code = '${code}',</if>
            <if test="name != null">menu.name = '${name}',</if>
            <if test="description != null">menu.description = '${description}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" parameterType="com.datalink.fdop.element.api.domain.DataElementMenu"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteElementMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[mme1:menu_menu_edge]->(menu)-[mme2:menu_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menu.id = ${pid}
        DELETE mme1, mme2 RETURN id(mme1),
        properties(mme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.element.api.domain.DataElementMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu) WHERE menu.id = ${id}
        <include refid="elementMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.element.api.domain.DataElementMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu) WHERE menu.code = '${code}'
        <include refid="elementMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectByPid" resultType="com.datalink.fdop.element.api.domain.DataElementMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu) WHERE menu.pid = ${pid}
        <include refid="elementMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.element.api.domain.DataElementMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id}
        <include refid="elementMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_e_data_element_menu) -[mme:menu_menu_edge]->(menuE:d_e_data_element_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id BIGINT)
    </select>

    <select id="selectMenuTree" resultType="com.datalink.fdop.element.api.model.DataElementTree">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu)
        <where>
            <if test="code != null and code != ''">and menu.code =~'.*${code}.*'</if>
        </where>
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType, menu.serialNumber
        ORDER BY menu.serialNumber ${sort}
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,serialNumber TEXT)
    </select>

    <select id="selectCiteMenuTree" resultType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo">
        SELECT *
        FROM (
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu)
        WHERE menu.pid = -1
        WITH DISTINCT (menu)
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType,null as fieldType,null as
        length,null as decimalLength,null as isPk,null as mapFieldName
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,fieldType TEXT,length
        INT2,decimalLength INT2,isPk BOOLEAN,mapFieldName TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element) WHERE node.pid = -1 AND node.dataElementType &lt;&gt; 'INLAY'
        <if test="code != null and code != ''">AND node.code =~'.*${code}.*'</if>
        <if test="name != null and name != ''">AND node.name =~'.*${name}.*'</if>
        WITH DISTINCT (node)
        RETURN node.id, node.pid, node.code, node.name, node.description, 'NODE' as menuType, /*node.dataElementType as
        menuType,*/
        node.fieldType,node.length,node.decimalLength,node.isPk,node.mapFieldName
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,menuType TEXT,fieldType TEXT,length
        INT2,decimalLength INT2,isPk BOOLEAN,mapFieldName TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_e_data_element_menu) -[]->(menuE:d_e_data_element_menu)
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType,null as fieldType,null
        as
        length,null as decimalLength,null as isPk,null as mapFieldName
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,fieldType TEXT,length
        INT2,decimalLength INT2,isPk BOOLEAN,mapFieldName TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_e_data_element_menu) -[]->(nodeE:d_e_data_element)
        <where>
            <if test="code != null and code != ''">AND nodeE.code =~'.*${code}.*'</if>
            <if test="name != null and name != ''">AND nodeE.name =~'.*${name}.*'</if>
        </where>
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType,null as fieldType,null
        as
        length,null as decimalLength,null as isPk,null as mapFieldName
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,fieldType TEXT,length
        INT2,decimalLength INT2,isPk BOOLEAN,mapFieldName TEXT)
        ) as zjdata_graph_table
        ORDER BY zjdata_graph_table.code ASC
    </select>


</mapper> 