<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataEntityStructureMapper">

    <sql id="relationDwdOrDim">
        RETURN
            edge.id as id,
            target_entity.id as targetDataEntityId,
            target_entity.code as targetDataEntityCode,
            target_entity.name as targetDataEntityName,
            target_entity.entityType as targetDataEntityType,
            target.id as targetDataEntityStructureId,
            target.code as targetDataEntityStructureCode,
            target.name as targetDataEntityStructureName,
            target.description as targetDataEntityStructureDescription,
            target.fieldType as targetDataEntityStructureFieldType,
            target.length as targetDataEntityStructureLength,
            target.decimalLength as targetDataEntityStructureDecimalLength,
            target.isPk as targetDataEntityStructureIsPk,
            target.entityInsertType as targetEntityInsertType,
            source_entity.pid as sourceDataEntityMenuId,
            source_entity.id as sourceDataEntityId,
            source_entity.code as sourceDataEntityCode,
            source_entity.name as sourceDataEntityName,
            source_entity.entityType as sourceDataEntityType,
            source.id as sourceDataEntityStructureId,
            source.code as sourceDataEntityStructureCode,
            source.name as sourceDataEntityStructureName,
            source.description as sourceDataEntityStructureDescription,
            source.fieldType as sourceDataEntityStructureFieldType,
            source.length as sourceDataEntityStructureLength,
            source.decimalLength as sourceDataEntityStructureDecimalLength,
            source.isPk as sourceDataEntityStructureIsPk,
            source.entityInsertType as sourceEntityInsertType,
            edge.seq as seq
        $$) as rela(
        id text,
        targetDataEntityId text,
        targetDataEntityCode text,
        targetDataEntityName text,
        targetDataEntityType text,
        targetDataEntityStructureId text,
        targetDataEntityStructureCode text,
        targetDataEntityStructureName text,
        targetDataEntityStructureDescription text,
        targetDataEntityStructureFieldType text,
        targetDataEntityStructureLength text,
        targetDataEntityStructureDecimalLength text,
        targetDataEntityStructureIsPk text,
        targetEntityInsertType text,
        sourceDataEntityMenuId text,
        sourceDataEntityId text,
        sourceDataEntityCode text,
        sourceDataEntityName text,
        sourceDataEntityType text,
        sourceDataEntityStructureId text,
        sourceDataEntityStructureCode text,
        sourceDataEntityStructureName text,
        sourceDataEntityStructureDescription text,
        sourceDataEntityStructureFieldType text,
        sourceDataEntityStructureLength text,
        sourceDataEntityStructureDecimalLength text,
        sourceDataEntityStructureIsPk text,
        sourceEntityInsertType text,
        seq text
        )
    </sql>

    <sql id="relationNotDwdOrDim">
        RETURN
            edge.id as id,
            target_entity.id as targetDataEntityId,
            target_entity.code as targetDataEntityCode,
            target_entity.name as targetDataEntityName,
            target_entity.entityType as targetDataEntityType,
            target.id as targetDataEntityStructureId,
            target.code as targetDataEntityStructureCode,
            target.name as targetDataEntityStructureName,
            target.description as targetDataEntityStructureDescription,
            target.fieldType as targetDataEntityStructureFieldType,
            target.length as targetDataEntityStructureLength,
            target.decimalLength as targetDataEntityStructureDecimalLength,
            target.isPk as targetDataEntityStructureIsPk,
            target.entityInsertType as targetEntityInsertType,
            source_entity.id as sourceDataEntityMenuId,
            source_entity.id as sourceDataEntityId,
            source_entity.code as sourceDataEntityCode,
            source_entity.name as sourceDataEntityName,
            source_entity.entityType as sourceDataEntityType,
            source.id as sourceDataEntityStructureId,
            source.code as sourceDataEntityStructureCode,
            source.name as sourceDataEntityStructureName,
            source.description as sourceDataEntityStructureDescription,
            source.fieldType as sourceDataEntityStructureFieldType,
            source.length as sourceDataEntityStructureLength,
            source.decimalLength as sourceDataEntityStructureDecimalLength,
            source.isPk as sourceDataEntityStructureIsPk,
            source.entityInsertType as sourceEntityInsertType,
            edge.seq as seq,
            source_entity.code as sourceDataEntityMenuCode,
            source_entity.name as sourceDataEntityMenuName,
            source_entity.entityType as sourceDataEntityMenuEntityType
            $$) as rela(
            id text,
            targetDataEntityId text,
            targetDataEntityCode text,
            targetDataEntityName text,
            targetDataEntityType text,
            targetDataEntityStructureId text,
            targetDataEntityStructureCode text,
            targetDataEntityStructureName text,
            targetDataEntityStructureDescription text,
            targetDataEntityStructureFieldType text,
            targetDataEntityStructureLength text,
            targetDataEntityStructureDecimalLength text,
            targetDataEntityStructureIsPk text,
            targetEntityInsertType text,
            sourceDataEntityMenuId text,
            sourceDataEntityId text,
            sourceDataEntityCode text,
            sourceDataEntityName text,
            sourceDataEntityType text,
            sourceDataEntityStructureId text,
            sourceDataEntityStructureCode text,
            sourceDataEntityStructureName text,
            sourceDataEntityStructureDescription text,
            sourceDataEntityStructureFieldType text,
            sourceDataEntityStructureLength text,
            sourceDataEntityStructureDecimalLength text,
            sourceDataEntityStructureIsPk text,
            sourceEntityInsertType text,
            seq text,
            sourceDataEntityMenuCode text,
            sourceDataEntityMenuName text,
            sourceDataEntityMenuEntityType text
        )
    </sql>

    <sql id="structure">
        RETURN ${alias}.id, ${alias}.code,
            ${alias}.name,
            ${alias}.description,
            ${alias}.entityInsertType,
            ${alias}.fieldType,
            ${alias}.length,
            ${alias}.decimalLength,
            ${alias}.isPk,
            ${alias}.seq,
            ${alias}.createTime,
            ${alias}.updateTime
            $$) as (id BIGINT,
            code TEXT,
            name TEXT,
            description TEXT,
            entityInsertType TEXT,
            fieldType TEXT,
            length BIGINT,
            decimalLength BIGINT,
            isPk BOOLEAN,
            seq INTEGER,
            createTime TEXT,
            updateTime TEXT)
    </sql>

    <select id="selectById" resultType="com.datalink.fdop.element.api.domain.DataEntityStructure">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity_structure) WHERE node.id = ${id}
        <include refid="structure">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.element.api.domain.DataEntityStructure">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) -[edge:d_e_data_entity_structure_edge]->(des:d_e_data_entity_structure)
        WHERE de.id = ${dataEntityId} AND des.code = '${code}'
        <include refid="structure">
            <property name="alias" value="des"/>
        </include>
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) -[edge:d_e_data_entity_element_edge]-(element:d_e_data_element)
        WHERE de.id = ${dataEntityId} AND element.code = '${code}'
        <include refid="structure">
            <property name="alias" value="element"/>
        </include>
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) -[edge:d_e_data_entity_input_edge]-(dei:d_e_data_element_input)
        WHERE de.id = ${dataEntityId} AND dei.code = '${code}'
        <include refid="structure">
            <property name="alias" value="dei"/>
        </include>
    </select>

    <select id="selectByIdAndCodes" resultType="com.datalink.fdop.element.api.domain.DataEntityStructure">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) -[edge:d_e_data_entity_structure_edge]->(des:d_e_data_entity_structure)
        WHERE de.id = ${dataEntityId} AND des.code in [
            <foreach collection="codes" item="code" separator=",">
                '${code}'
            </foreach>
        ]
        <include refid="structure">
            <property name="alias" value="des"/>
        </include>
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) -[edge:d_e_data_entity_element_edge]-(element:d_e_data_element)
        WHERE de.id = ${dataEntityId} AND element.code in [
        <foreach collection="codes" item="code" separator=",">
            '${code}'
        </foreach>
        ]
        <include refid="structure">
            <property name="alias" value="element"/>
        </include>
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) -[edge:d_e_data_entity_input_edge]-(dei:d_e_data_element_input)
        WHERE de.id = ${dataEntityId} AND dei.code in [
        <foreach collection="codes" item="code" separator=",">
            '${code}'
        </foreach>
        ]
        <include refid="structure">
            <property name="alias" value="dei"/>
        </include>
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.element.api.domain.DataEntityStructure">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (des:d_e_data_entity_structure) WHERE des.code = '${code}' AND des.id &lt;&gt; ${id}
        AND des.dataEntityId = ${dataEntityId}
        <include refid="structure">
            <property name="alias" value="des"/>
        </include>
    </select>

    <select id="insertStructure" parameterType="com.datalink.fdop.element.api.domain.DataEntityStructure"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (de:d_e_data_entity) WHERE de.id = ${dataEntityStructure.dataEntityId}
            WITH de
            CREATE (de)-[deseS:d_e_data_entity_structure_edge{
            startTable:'d_e_data_entity', endTable:'d_e_data_entity_structure', seq:${dataEntityStructure.seq}}]->(des:d_e_data_entity_structure ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityStructureAgeStr(dataEntityStructure)})-[deseE:d_e_data_entity_structure_edge{
            startTable:'d_e_data_entity_structure',endTable:'d_e_data_entity',seq:${dataEntityStructure.seq}
            }]->(de)
        RETURN id(de), properties(de)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchInsertStructure" parameterType="com.datalink.fdop.element.api.domain.DataEntityStructure"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',$$
        <foreach collection="dataEntityStructureList" item="dataEntityStructure">
            CREATE (:d_e_data_entity_structure
            ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityStructureAgeStr(dataEntityStructure)})
        </foreach>
        $$) as (m ag_catalog.agtype);
    </select>

    <select id="batchCreateStructureEdge" resultType="int">
        <foreach collection="dataEntityStructureList" item="dataEntityStructure" separator=" UNION ">
            select COUNT(1)
            from ag_catalog.cypher('zjdata_graph', $$
            MATCH (de:d_e_data_entity),(structure:d_e_data_entity_structure)
            WHERE de.id = ${dataEntityId} AND structure.id = ${dataEntityStructure.id}
            CREATE (de)-[:d_e_data_entity_structure_edge{
            startTable:'d_e_data_entity', endTable:'d_e_data_entity_structure', seq:${dataEntityStructure.seq}}]->
            (structure)-[:d_e_data_entity_structure_edge{
            startTable:'d_e_data_entity_structure',endTable:'d_e_data_entity',seq:${dataEntityStructure.seq}
            }]->(de)
            RETURN id(de), properties(de)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        </foreach>
    </select>

    <select id="updateStructure" parameterType="com.datalink.fdop.element.api.domain.DataEntityStructure"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (des:d_e_data_entity_structure) WHERE des.id = ${id}
        <set>
            <if test="code != null and code != ''">des.code = '${code}',</if>
            <if test="name != null">des.name = '${name}',</if>
            <if test="dataEntityId != dataEntityId">des.dataEntityId = ${dataEntityId},</if>
            <if test="description != null">des.description = '${description}',</if>
            <if test="fieldType != null">des.fieldType = '${fieldType}',</if>
            <if test="length != null">des.length = ${length},</if>
            <if test="decimalLength != null">des.decimalLength = ${decimalLength},</if>
            <if test="isPk != null">des.isPk = ${isPk},</if>
            <if test="seq != null">des.seq = ${seq},</if>
            des.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            des.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(des), properties(des)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteCustomize" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) WHERE de.id = ${dataEntityId}
        WITH de
        MATCH
        (de)-[edge:d_e_data_entity_structure_edge]->(des:d_e_data_entity_structure)
        WHERE des.id IN
        <foreach collection="structureIds" item="structureId" open="[" separator="," close="]">
            ${structureId}
        </foreach>
        WITH des
        DETACH DELETE des
        RETURN id(des), properties(des)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteCustomizeMapping" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (mapping:d_e_data_entity_table_mapping)
        WHERE mapping.dataEntityId = ${dataEntityId} AND mapping.fieldId IN
        <foreach collection="structureIds" item="structureId" open="[" separator="," close="]">
            ${structureId}
        </foreach>
        DETACH DELETE mapping
        RETURN id(mapping), properties(mapping)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deletePredefinedMainAndField" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) WHERE de.id = ${dataEntityId}
        WITH de
        MATCH
        (de)-[edge:d_e_data_entity_element_edge]-(element:d_e_data_element)
        WHERE element.id IN
        <foreach collection="structureIds" item="structureId" open="[" separator="," close="]">
            ${structureId}
        </foreach>
        DETACH DELETE edge
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deletePredefinedMainAndFieldMapping" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) WHERE de.id = ${dataEntityId}
        WITH de
        MATCH
        (de)-[edge:d_e_data_entity_element_edge]-(element:d_e_data_element)
        WHERE element.id IN
        <foreach collection="structureIds" item="structureId" open="[" separator="," close="]">
            ${structureId}
        </foreach>
        WITH element
        MATCH (element)-[edge:d_e_data_entity_element_mapping_edge]-(detm:d_e_data_entity_table_mapping)
        DETACH DELETE detm
        RETURN id(detm), properties(detm)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deletePredefinedInput" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) WHERE de.id = ${dataEntityId}
        WITH de
        MATCH
        (de)-[edge:d_e_data_entity_input_edge]-(input:d_e_data_element_input)
        WHERE input.id IN
        <foreach collection="structureIds" item="structureId" open="[" separator="," close="]">
            ${structureId}
        </foreach>
        DETACH DELETE edge
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deletePredefinedInputMapping" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) WHERE de.id = ${dataEntityId}
        WITH de
        MATCH
        (de)-[edge:d_e_data_entity_input_edge]-(input:d_e_data_element_input)
        WHERE input.id IN
        <foreach collection="structureIds" item="structureId" open="[" separator="," close="]">
            ${structureId}
        </foreach>
        WITH input
        MATCH (input)-[edge:d_e_data_entity_input_mapping_edge]-(detm:d_e_data_entity_table_mapping)
        DETACH DELETE detm
        RETURN id(detm), properties(detm)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteAll" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (de:d_e_data_entity) -[edge:d_e_data_entity_structure_edge]->(des:d_e_data_entity_structure)
            WHERE de.id = ${dataEntityId}
            DETACH DELETE des RETURN id(des), properties(des)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchDeleteAll" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) -[edge:d_e_data_entity_structure_edge]->(des:d_e_data_entity_structure)
        WHERE de.id IN
        <foreach collection="dataEntityIdList" item="dataEntityId" open="[" separator="," close="]">
            ${dataEntityId}
        </foreach>
        DETACH DELETE des RETURN id(des), properties(des)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchDeleteEntityStructureByEntityIds" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (des:d_e_data_entity_structure)
        WHERE des.dataEntityId IN
        <foreach collection="dataEntityIds" item="dataEntityId" open="[" separator="," close="]">
            ${dataEntityId}
        </foreach>
        DETACH DELETE des RETURN id(des), properties(des)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="checkEntityElementEdge" resultType="boolean">
        WITH checkElement as (select count(1) as rowNum
                              from ag_catalog.cypher('zjdata_graph', $$
                                  MATCH (entity:d_e_data_entity) -[:d_e_data_entity_element_edge]-(element:d_e_data_element)
                                  WHERE entity.id = ${dataEntityId} AND element.id = ${dataElementId}
                                  RETURN id(element), properties(element)
                                                         $$) as (id ag_catalog.agtype, properties ag_catalog.agtype)
            )
        select case when rowNum = 0 then true else false end
        from checkElement
    </select>

    <select id="insertEntityElementEdge" parameterType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_e_data_entity) WHERE entity.id = ${dataEntityId}
            WITH entity
            MATCH (element:d_e_data_element) WHERE element.id = ${dataElementStructureVo.id}
            WITH entity, element CREATE (entity)-[edgeS:d_e_data_entity_element_edge {
            startTable:'d_e_data_entity', endTable:'d_e_data_element', entityInsertType:'PREDEFINED', seq:${dataElementStructureVo.seq} }]->(element)-[edgeE:d_e_data_entity_element_edge{
            startTable:'d_e_data_element',endTable:'d_e_data_entity',entityInsertType:'PREDEFINED',seq:${dataElementStructureVo.seq}
            }]->(entity)
        RETURN id(edgeS), properties(edgeS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkEntityInputEdge" resultType="boolean">
        WITH checkInput as (select count(1) as rowNum
                            from ag_catalog.cypher('zjdata_graph', $$
                                MATCH (entity:d_e_data_entity) -[:d_e_data_entity_input_edge]-(input :d_e_data_element_input)
                                WHERE entity.id = ${dataEntityId} AND input.id = ${dataInputId}
                                RETURN id(input),
                                                   properties(input) $$) as (id ag_catalog.agtype, properties ag_catalog.agtype)
            )
        select case when rowNum = 0 then true else false end
        from checkInput
    </select>

    <select id="insertEntityInputEdge" parameterType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_e_data_entity) WHERE entity.id = ${dataEntityId}
            WITH entity
            MATCH (input :d_e_data_element_input) WHERE input.id = ${dataElementStructureVo.id}
            WITH entity, input CREATE (entity)-[edgeS:d_e_data_entity_input_edge{
            startTable:'d_e_data_entity', endTable:'d_e_data_element_input', entityInsertType:'PREDEFINED', seq:${dataElementStructureVo.seq} }]->(input)-[edgeE:d_e_data_entity_input_edge{
            startTable:'d_e_data_element_input',endTable:'d_e_data_entity',entityInsertType:'PREDEFINED',seq:${dataElementStructureVo.seq}
            }]->(entity)
        RETURN id(edgeS), properties(edgeS)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectEntityPkStructure" resultType="com.datalink.fdop.element.api.model.vo.DataEntityStructureVo">
        WITH overview as (
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_element_edge]->(element:d_e_data_element)
                WHERE entity.id = ${dataEntityId} AND element.isPk = true
                RETURN element.id, element.code, element.name, element.description, edge.entityInsertType,
                                   element.dataElementType,
                                   element.fieldType,
                                   element.length,
                                   element.decimalLength, element.isPk, edge.seq, element.createTime, entity.id as dataEntityId
                                       $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,createTime TEXT,dataEntityId BIGINT)
            UNION
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_structure_edge]->(structure:d_e_data_entity_structure)
                WHERE entity.id = ${dataEntityId} AND structure.isPk = true
                RETURN structure.id, structure.code, structure.name, structure.description, structure.entityInsertType,
                                   null as dataElementType,
                                   structure.fieldType,
                                   structure.length,
                                   structure.decimalLength, structure.isPk, edge.seq, structure.createTime, entity.id as dataEntityId
                                       $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,createTime TEXT,dataEntityId BIGINT)
        ) select * from overview ORDER BY seq ASC
    </select>

    <select id="selectEntityNoPkStructure" resultType="com.datalink.fdop.element.api.model.vo.DataEntityStructureVo">
        WITH overview as (
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_element_edge]->(element:d_e_data_element)
                WHERE entity.id = ${dataEntityId} AND element.isPk = false
                RETURN element.id, element.code, element.name, element.description, edge.entityInsertType,
                                   element.dataElementType,
                                   element.fieldType,
                                   element.length,
                                   element.decimalLength, element.isPk, edge.seq, element.createTime, entity.id as dataEntityId
                                   $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,createTime TEXT,dataEntityId BIGINT)
            UNION
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_input_edge]->(input :d_e_data_element_input)
                WHERE entity.id = ${dataEntityId} AND input.isPk = false
                RETURN input.id, input.code, input.name, input.description, edge.entityInsertType,
                                   input.dataElementType,
                                   input.fieldType,
                                   input.length,
                                   input.decimalLength, input.isPk, edge.seq, input.createTime, entity.id as dataEntityId
                                   $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,createTime TEXT,dataEntityId BIGINT)
            UNION
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_structure_edge]->(structure:d_e_data_entity_structure)
                WHERE entity.id = ${dataEntityId} AND structure.isPk = false
                RETURN structure.id, structure.code, structure.name, structure.description, structure.entityInsertType,
                                   null as dataElementType,
                                   structure.fieldType,
                                   structure.length,
                                   structure.decimalLength, structure.isPk, edge.seq, structure.createTime, entity.id as dataEntityId
                                   $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,createTime TEXT,dataEntityId BIGINT)
        ) select * from overview ORDER BY seq ASC
    </select>

    <select id="selectStructureByIdPaging" resultType="com.datalink.fdop.element.api.model.vo.DataEntityStructureVo">
        WITH overview as (
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_element_edge]->(element:d_e_data_element)
                WHERE entity.id = ${dataEntityId}
                RETURN element.id, element.code, element.name, element.description, edge.entityInsertType,
                                   element.dataElementType,
                                   element.fieldType,
                                   element.length,
                                   element.decimalLength, element.isPk, edge.seq, element.createTime, entity.id as dataEntityId
                                       $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,createTime TEXT,dataEntityId BIGINT)
            UNION
            select *
            from ag_catalog.cypher('zjdata_graph', $$
                MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_structure_edge]->(structure:d_e_data_entity_structure)
                WHERE entity.id = ${dataEntityId}
                RETURN structure.id, structure.code, structure.name, structure.description, structure.entityInsertType,
                                   null as dataElementType,
                                   structure.fieldType,
                                   structure.length,
                                   structure.decimalLength, structure.isPk, edge.seq, structure.createTime, entity.id as dataEntityId
                                       $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType TEXT,fieldType TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2,createTime TEXT,dataEntityId BIGINT)
            ) select * from overview
            <where>
                <if test="dataEntityStructureDto.code != null and dataEntityStructureDto.code != ''">
                    and code like '%${dataEntityStructureDto.code}%'
                </if>
                <if test="dataEntityStructureDto.name != null and dataEntityStructureDto.name != ''">
                    and name like '%${dataEntityStructureDto.name}%'
                </if>
                <if test="dataEntityStructureDto.description != null and dataEntityStructureDto.description != ''">
                    and name like '%${dataEntityStructureDto.description}%'
                </if>
                <if test="dataEntityStructureDto.fieldType != null and dataEntityStructureDto.fieldType != ''">
                    and fieldType = '${dataEntityStructureDto.fieldType}'
                </if>
                <if test="dataEntityStructureDto.isPk != null">
                    and isPk = ${dataEntityStructureDto.isPk}
                </if>
            </where>
            ORDER BY seq ${sort}
    </select>

    <select id="selectEntityAllMaxSeq" resultType="Integer">
        WITH seq as (select *
                     from ag_catalog.cypher('zjdata_graph', $$
                         MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_element_edge]->(element:d_e_data_element)
                         WHERE entity.id = ${dataEntityId} AND element.isPk = ${isPk}
                         RETURN MAX (edge.seq)
                         $$) as (seq INT2)
        UNION
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_input_edge]->(input :d_e_data_element_input)
            WHERE entity.id = ${dataEntityId} AND input.isPk = ${isPk}
            RETURN MAX (edge.seq)
            $$) as (seq INT2)
        UNION
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_structure_edge]->(structure:d_e_data_entity_structure)
            WHERE entity.id = ${dataEntityId} AND structure.isPk = ${isPk}
            RETURN MAX (edge.seq)
            $$) as (seq INT2)
        )
        select case when seq is null then 0 else seq end
        from seq
    </select>

    <select id="selectEntityStructureByElementFieldId"
            resultType="com.datalink.fdop.element.api.model.vo.DataEntityStructureVo">
        select *
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_element_edge]->(element:d_e_data_element)
        WHERE element.id IN
        <foreach collection="dataElementStructureIds" item="dataElementStructureId" open="[" separator="," close="]">
            ${dataElementStructureId}
        </foreach>
        RETURN element.id, entity.id as dataEntityId, element.code, element.name, element.description,
        edge.entityInsertType,
        element.dataElementType,
        element.fieldType,
        element.length,
        element.decimalLength, element.isPk, edge.seq
        $$) as (id BIGINT,dataEntityId BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType
        TEXT,fieldType
        TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2)
        UNION
        select *
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_input_edge]->(input :d_e_data_element_input)
        WHERE input.id IN
        <foreach collection="dataElementStructureIds" item="dataElementStructureId" open="[" separator="," close="]">
            ${dataElementStructureId}
        </foreach>
        RETURN input.id, entity.id as dataEntityId, input.code, input.name, input.description,
        edge.entityInsertType,
        input.dataElementType,
        input.fieldType,
        input.length,
        input.decimalLength, input.isPk, edge.seq
        $$) as (id BIGINT,dataEntityId BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType
        TEXT,fieldType
        TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2)
    </select>

    <select id="selectElementStructureByElementFieldId"
            resultType="com.datalink.fdop.element.api.model.vo.DataEntityStructureVo">
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_element_edge]->(element:d_e_data_element)
            WHERE element.id = ${dataElementStructureId} and entity.id = ${dataEntityId}
            RETURN element.id, entity.id as dataEntityId, element.code, element.name, element.description,
                               edge.entityInsertType,
                               element.dataElementType,
                               element.fieldType,
                               element.length,
                               element.decimalLength, element.isPk, edge.seq
                                   $$) as (id BIGINT,dataEntityId BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType
        TEXT,fieldType
        TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2)
        UNION
        select *
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_e_data_entity) -[edge:d_e_data_entity_input_edge]->(input :d_e_data_element_input)
            WHERE input.id = ${dataElementStructureId} and entity.id = ${dataEntityId}
            RETURN input.id,

            .id as dataEntityId, input.code, input.name, input.description,
                               edge.entityInsertType,
                               input.dataElementType,
                               input.fieldType,
                               input.length,
                               input.decimalLength, input.isPk, edge.seq
                                   $$) as (id BIGINT,dataEntityId BIGINT,code TEXT,name TEXT,description TEXT,entityInsertType TEXT,dataElementType
        TEXT,fieldType
        TEXT,length BIGINT,decimalLength BIGINT,isPk BOOLEAN,seq INT2)
    </select>

    <select id="deletePredefinedEdge" parameterType="com.datalink.fdop.element.api.model.vo.DataEntityStructureVo"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
        <foreach collection="dataEntityStructureVoList" item="dataEntityStructureVo">
            <if test="dataEntityStructureVo.dataElementType.toString() == 'MAIN'.toString() OR dataEntityStructureVo.dataElementType.toString() == 'FIELD'.toString()">

            </if>

            <if test="dataEntityStructureVo.dataElementType.toString() == 'INPUT'.toString()">
                MATCH (entity:d_e_data_entity)-[edge:d_e_data_entity_input_edge]-(input:d_e_data_element_input)
                WHERE input.id IN
                <foreach collection="structureIds" item="structureId" open="[" separator="," close="]">
                    ${structureId}
                </foreach>
            </if>
        </foreach>
        DETACH DELETE edge
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectTaskAndFieldEdge" parameterType="Long"
            resultType="com.datalink.fdop.element.api.model.dto.TaskEtlDto">
        SELECT distinct *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (task:t_ds_task_definition)
        -[]->(node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(structure:d_e_data_entity_structure)
        -[:d_e_data_entity_structure_edge]->(entity:d_e_data_entity)
        WHERE edge.isSubField = false AND entity.id = ${dataEntityId}
        <if test="dataEntityStructureId != null ">
            AND structure.id = ${dataEntityStructureId}
        </if>
        RETURN task.code as taskCode, task.name as taskName, task.etlStatus, node.nodeId
        $$) as (taskCode BIGINT,taskName TEXT,etlStatus INTEGER,nodeId TEXT)
    </select>

    <select id="selectTaskAndFieldEdgeByDataEntityIds" resultType="com.datalink.fdop.element.api.model.dto.TaskEtlDto">
        SELECT distinct *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (task:t_ds_task_definition)
        -[]->(node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(structure:d_e_data_entity_structure)
        -[:d_e_data_entity_structure_edge]->(entity:d_e_data_entity)
        WHERE edge.isSubField = false AND entity.id IN
        <foreach collection="dataEntityIds" item="dataEntityId" open="[" separator="," close="]">
            ${dataEntityId}
        </foreach>
        RETURN task.code as taskCode, task.name as taskName, task.etlStatus, node.nodeId, entity.code as entityCode
        $$) as (taskCode BIGINT,taskName TEXT,etlStatus INTEGER,nodeId TEXT,entityCode TEXT)
    </select>


    <select id="selectTaskAndFieldEdgeList" parameterType="Long"
            resultType="com.datalink.fdop.element.api.model.dto.TaskEtlDto">
        SELECT distinct *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (task:t_ds_task_definition)
        -[]->(node:f_g_c_etl_node)-[]->(field:f_g_c_etl_field)-[edge:f_g_c_etl_field_field_edge]->(structure:d_e_data_entity_structure)-[:d_e_data_entity_structure_edge]->(entity:d_e_data_entity)
        WHERE edge.isSubField = false AND entity.id = ${dataEntityId}
        <if test="dataEntityStructureIdss != null and dataEntityStructureIdss.size() > 0">
            AND
            <foreach collection="dataEntityStructureIdss" item="dataEntityStructureIds" index="" open="(" separator="OR" close=")">
                structure.id IN
                <foreach collection="dataEntityStructureIds" item="dataEntityStructureId" index="" open="[" separator="," close="]">
                    ${dataEntityStructureId}
                </foreach>
            </foreach>
        </if>
        RETURN task.code as taskCode, task.name as taskName, task.etlStatus, node.nodeId
        $$) as (taskCode BIGINT,taskName TEXT,etlStatus INTEGER,nodeId TEXT)
    </select>

    <select id="deleteL5Id" resultType="int">
        select count(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (structure:d_e_data_entity_structure)
            WHERE structure.id in
            <foreach collection="ids" item="id" open="[" separator="," close="]">
                ${id}
            </foreach>
            SET structure.l5Id = null
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createEntityRelation" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (source:d_e_data_entity_structure), (target:d_e_data_entity_structure)
            WHERE source.id = ${sourceDataEntityStructureId} and target.id = ${targetDataEntityStructureId}
            CREATE (source)-[edge:d_e_data_entity_structure_structure_edge{ startTable:'d_e_data_entity_structure', endTable:'d_e_data_entity_structure', id: ${id} }]->(target)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteEntityRelation" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (source:d_e_data_entity_structure) -[edge:d_e_data_entity_structure_structure_edge]-> (target:d_e_data_entity_structure)
            WHERE edge.id in
            <foreach collection="ids" item="id" open="[" separator="," close="]">
                ${id}
            </foreach>
            DELETE edge
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="queryNextEntityRelation" resultType="com.datalink.fdop.element.api.domain.DataEntityBlood">

    </select>

    <select id="queryEntityRelation" resultType="com.datalink.fdop.element.api.domain.DataEntityRelation">
        select rela.*, menu.code as sourceDataEntityMenuCode, menu.code as sourceDataEntityMenuName, menu.entityType as sourceDataEntityMenuType
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (source_entity:d_e_data_entity) -[edge1:d_e_data_entity_structure_edge]->(source:d_e_data_entity_structure)
            -[edge:d_e_data_entity_structure_structure_edge]-> (target:d_e_data_entity_structure)
            &lt;-[edge2:d_e_data_entity_structure_edge]-(target_entity:d_e_data_entity)
        <choose>
            <when test="queryType == 'target'">
                WHERE target.id in
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
            </when>
            <otherwise>
                WHERE source.id in
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
            </otherwise>
        </choose>
        and (source_entity.entityType = 'DWD' or source_entity.entityType = 'DIM')
        <include refid="relationDwdOrDim"></include>
        left join
         ag_catalog.cypher('zjdata_graph', $$
             MATCH (menu:d_e_data_entity_menu)
             RETURN menu.id, menu.code, menu.name, menu.entityType $$)
             as menu(id text, code text, name text, entityType TEXT)
        on rela.sourceDataEntityMenuId = menu.id
        union
        select rela.*
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (source_entity:d_e_data_entity) -[edge1:d_e_data_entity_structure_edge]->(source:d_e_data_entity_structure)
        -[edge:d_e_data_entity_structure_structure_edge]-> (target:d_e_data_entity_structure)
        &lt;-[edge2:d_e_data_entity_structure_edge]-(target_entity:d_e_data_entity)
        <choose>
            <when test="queryType == 'target'">
                WHERE target.id in
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
            </when>
            <otherwise>
                WHERE source.id in
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
            </otherwise>
        </choose>
        and (source_entity.entityType &lt;&gt; 'DWD' and source_entity.entityType &lt;&gt; 'DIM')
        <include refid="relationNotDwdOrDim"></include>
    </select>



</mapper>