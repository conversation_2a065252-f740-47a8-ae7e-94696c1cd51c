<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataElementMapper">

    <sql id="element">
        RETURN ${alias}.id, ${alias}.pid,
            ${alias}.code,
            ${alias}.name,
            ${alias}.description,
            ${alias}.dataElementType,
            ${alias}.elementSourceType,
            ${alias}.taskJson,
            ${alias}.fieldType,
            ${alias}.length,
            ${alias}.decimalLength,
            ${alias}.isPk,
            ${alias}.mapTableName,
            ${alias}.mapFieldName,
            ${alias}.createTime,
            ${alias}.updateTime
            $$) as (id BIGINT, pid BIGINT,
            code TEXT,
            name TEXT,
            description TEXT,
            dataElementType TEXT,
            elementSourceType TEXT,
            taskJson TEXT,
            fieldType TEXT,
            length BIGINT,
            decimalLength BIGINT,
            isPk BOOLEAN,
            mapTableName TEXT,
            mapFieldName TEXT,
            createTime TEXT,
            updateTime TEXT)
    </sql>

    <select id="createElementAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_element_menu), (node:d_e_data_element)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_e_data_element_menu',endTable:'d_e_data_element'}]->(node)-[:node_menu_edge
        {startTable:'d_e_data_element',endTable:'d_e_data_element_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertElement" parameterType="com.datalink.fdop.element.api.domain.DataElement" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_e_data_element ${@com.datalink.fdop.element.utils.DomainAgeUtils@getDataElementAgeStr(dataElement)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertElementOwnEdge" parameterType="com.datalink.fdop.element.api.domain.DataElement" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_e_data_element) WHERE node.id = ${id}
            CREATE (node) -[eeeE:d_e_element_element_edge
            {startTable:'d_e_data_element', endTable:'d_e_data_element', seq:1, type:'${dataElementType}' }]->(node)-[eeeS:d_e_element_element_edge
            {startTable:'d_e_data_element', endTable:'d_e_data_element', seq:1, type:'${dataElementType}' }
            ]->(node)
            RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.element.api.domain.DataElement" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element)
        WHERE node.id = ${id}
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null">node.name = '${name}',</if>
            <if test="description != null">node.description = '${description}',</if>
            <if test="dataElementType != null">node.dataElementType = '${dataElementType}',</if>
            <if test="elementSourceType != null">node.elementSourceType = '${elementSourceType}',</if>
            <if test="taskJson != null and taskJson != ''">node.taskJson = '${taskJson}',</if>
            <if test="fieldType != null">node.fieldType = '${fieldType}',</if>
            <if test="length != null">node.length = ${length},</if>
            <if test="decimalLength != null">node.decimalLength = ${decimalLength},</if>
            <if test="isPk != null">node.isPk = ${isPk},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" parameterType="com.datalink.fdop.element.api.domain.DataElementMenu"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteElementAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element)-[edge]-()
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE edge, node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="deleteElementOwnEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (nodeE:d_e_data_element) -[eeeS:d_e_element_element_edge]-(nodeS)-[eeeE:d_e_element_element_edge]->(nodeE)
            WHERE nodeE.id = ${id} and nodeS.id = ${id}
            DELETE eeeS, eeeE RETURN id(nodeE),
                               properties(nodeE) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.element.api.domain.DataElement">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element) WHERE node.id = ${id}
        <include refid="element">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.element.api.domain.DataElement">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element) WHERE node.code = '${code}'
        <include refid="element">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.element.api.domain.DataElement">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element) WHERE node.code = '${code}' AND node.id &lt;&gt; ${id}
        <include refid="element">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="selectList" resultType="com.datalink.fdop.element.api.domain.DataElement">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element)
        <where>
            <if test="dataElement.pid != null">AND node.pid = ${dataElement.pid},</if>
            <if test="dataElement.code != null and dataElement.code != ''">AND node.code =~'.*${dataElement.code}.*'
            </if>
            <if test="dataElement.name != null and dataElement.name != ''">AND node.name =~'.*${dataElement.name}.*'
            </if>
            <if test="dataElement.description != null and dataElement.description != ''">AND node.description
                =~'.*${dataElement.description}.*'
            </if>
            <if test="dataElement.dataElementType != null">AND node.dataElementType
                =~'.*${dataElement.dataElementType}.*'
            </if>
            <if test="dataElement.fieldType != null">AND node.fieldType =~'.*${dataElement.fieldType}.*'</if>
            <if test="dataElement.length != null">AND node.length = ${dataElement.length}</if>
            <if test="dataElement.decimalLength != null">AND node.decimalLength = ${dataElement.decimalLength}</if>
            <if test="dataElement.isPk != null">AND node.isPk = ${dataElement.isPk}</if>
        </where>
        <include refid="element">
            <property name="alias" value="node"/>
        </include>
    </select>

    <select id="overview" resultType="com.datalink.fdop.element.api.domain.DataElement">
        WITH overview as (
        <if test="pid == -1">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_e_data_element)
            WHERE node.pid = -1
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,'顶级菜单' as
            menuName,node.createTime,node.updateTime,node.dataElementType
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime
            TEXT,updateTime TEXT,dataElementType TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element) -[:node_menu_edge]->(menu:d_e_data_element_menu)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.createTime,node.updateTime,node.dataElementType
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime TEXT,updateTime
        TEXT,dataElementType TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_e_data_element_menu) -[nme:node_menu_edge]->(nodeE:d_e_data_element)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="selectElementByIds" resultType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element)
        <where>
            <if test="dataElementIdss != null and dataElementIdss.size() > 0">
                <foreach collection="dataElementIdss" item="dataElementIds" index="" open="(" separator="OR" close=")">
                    node.id IN
                    <foreach collection="dataElementIds" item="dataElementId" index="" open="[" separator="," close="]">
                        ${dataElementId}
                    </foreach>
                </foreach>
            </if>
            <if test="name != null and name != ''">AND node.name =~'.*${name}.*'</if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description,'NODE' as
        menuType,node.dataElementType,node.fieldType,node.length,node.decimalLength,node.isPk,node.mapFieldName
        ORDER BY node.code ASC
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuType TEXT,dataElementType
        TEXT,fieldType TEXT,length
        INT2,decimalLength INT2,isPk BOOLEAN,mapFieldName TEXT)
    </select>

    <select id="selectSonElement" resultType="com.datalink.fdop.element.api.domain.DataElement">
        /*        SELECT *
                FROM ag_catalog.cypher('zjdata_graph', $$
                    MATCH (nodeS:d_e_data_element) -[edge]->(nodeE:d_e_data_element)
                    WHERE nodeS.id = ${dataElementId} AND nodeE.dataElementType = 'MAIN' AND nodeE.id &lt;&gt;
                                                       ${dataElementId} AND edge.son &lt;&gt; ${dataElementId}
                    RETURN id(nodeE), properties(nodeE)
                                           $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)*/
        SELECT *
        FROM
            ag_catalog.cypher('zjdata_graph', $$
                MATCH (deS:d_e_data_element) -[eee:d_e_element_element_edge]->(deE:d_e_data_element)
                WHERE deS.id = ${dataElementId}
                AND deE.dataElementType = 'MAIN'
                AND eee.father = ${dataElementId}
                WITH deE, eee ORDER BY eee.seq asc
                RETURN deE.id, deE.code, deE.name, deE.description, deE.dataElementType, deE.fieldType,
                              deE.length,
                              deE.decimalLength, deE.isPk, deE.mapFieldName, deE.createTime,
                              deE.updateTime
                                  $$) as (id BIGINT,code TEXT
                                  ,name TEXT
                                  ,description TEXT
                                  ,dataElementType TEXT
                                  ,fieldType TEXT
                                  ,length BIGINT
                                  ,decimalLength BIGINT
                                  ,isPk BOOLEAN
                                  ,mapFieldName TEXT
                                  ,createTime TEXT
                                  ,updateTime TEXT)
    </select>

    <select id="selectSonElementField" resultType="com.datalink.fdop.element.api.domain.DataElement">
        SELECT *
        FROM
            ag_catalog.cypher('zjdata_graph', $$
                MATCH (deS:d_e_data_element) -[eee:d_e_element_element_edge]->(deE:d_e_data_element)
                WHERE deS.id = ${dataElementId}
                AND deE.dataElementType &lt;&gt; 'FIELD'
                WITH deE, eee ORDER BY eee.seq asc
                RETURN deE.id, deE.code, deE.name, deE.description, deE.dataElementType, deE.fieldType,
                              deE.length,
                              deE.decimalLength, deE.isPk, deE.mapFieldName, deE.createTime,
                              deE.updateTime
                                  $$) as (id BIGINT,code TEXT
                                  ,name TEXT
                                  ,description TEXT
                                  ,dataElementType TEXT
                                  ,fieldType TEXT
                                  ,length BIGINT
                                  ,decimalLength BIGINT
                                  ,isPk BOOLEAN
                                  ,mapFieldName TEXT
                                  ,createTime TEXT
                                  ,updateTime TEXT)
    </select>

    <select id="selectNodeTree" resultType="com.datalink.fdop.element.api.model.DataElementTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_element)
        WHERE (node.dataElementType &lt;&gt; 'INLAY' OR node.dataElementType is null)
        <if test="code != null and code != ''">and node.code =~'.*${code}.*'</if>
        RETURN node.id, node.pid, node.code, node.name, node.description, node.dataElementType as
        menuType
        ORDER BY node.code ${sort}
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT)
    </select>

    <select id="selectCiteElementTree" resultType="com.datalink.fdop.element.api.model.vo.DataElementStructureVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (nodeS:d_e_data_element) -[]->(menuE:d_e_data_element_menu)
        WHERE (nodeS.dataElementType &lt;&gt; 'INLAY' OR nodeS.dataElementType is null)
        <if test="code != null and code != ''">AND nodeS.code =~'.*${code}.*'</if>
        <if test="name != null and name != ''">AND nodeS.name =~'.*${name}.*'</if>
        RETURN nodeS.id, nodeS.pid, nodeS.code, nodeS.name, nodeS.description,'NODE' as menuType
        ,nodeS.dataElementType,nodeS.fieldType,nodeS.length,nodeS.decimalLength,nodeS.isPk,nodeS.mapFieldName
        ORDER BY nodeS.code ASC
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuType TEXT,dataElementType
        TEXT,fieldType TEXT,length
        INT2,decimalLength INT2,isPk BOOLEAN,mapFieldName TEXT)
    </select>

</mapper>