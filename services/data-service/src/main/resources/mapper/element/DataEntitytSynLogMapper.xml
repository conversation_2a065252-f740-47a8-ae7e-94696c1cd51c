<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataEntitytSynLogMapper">

    <select id="insertSynLog" parameterType="com.datalink.fdop.element.api.domain.DataEntitySynLog" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (log:d_e_data_entity_syn_log ${@com.datalink.fdop.element.utils.DomainAgeUtils@getDataEntitySynLogAgeStr(dataEntitySynLog)})
                                   RETURN id(log), properties(log)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertMenuSynLog" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_e_data_entity_menu), (log:d_e_data_entity_syn_log)
                                   WHERE menu.id = ${pid} AND log.id = ${id}
                                   CREATE (log)-[:d_e_data_entity_syn_log_menu_edge]->(menu)-[:d_e_data_entity_syn_log_menu_edge]->(log)
                                   RETURN id(log), properties(log)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertDataSourceSynLog" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource), (log:d_e_data_entity_syn_log)
                                   WHERE d.id = ${dataSourceId} AND log.id = ${id}
                                   CREATE (log)-[:d_e_data_entity_syn_log_datasource_edge]->(d)-[:d_e_data_entity_syn_log_datasource_edge]->(log)
                                   RETURN id(log), properties(log)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.element.api.domain.DataEntitySynLog" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:d_e_data_entity_syn_log)
        WHERE log.id = ${id}
        <set>
            <if test="error != null and error != ''">log.error = '${error}',</if>
            <if test="synStatus != null">log.synStatus = '${synStatus}'</if>
        </set>
        RETURN id(log), properties(log)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:d_e_data_entity_syn_log)
        WHERE log.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE log RETURN id(log),properties(log) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectList" resultType="com.datalink.fdop.element.api.domain.DataEntitySynLog">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:d_e_data_entity_syn_log) WITH log
            MATCH (log)-[]->(menu:d_e_data_entity_menu)
        WITH log, menu MATCH (log) -[]->(d:datasource)
        <where>
            <if test="Code != null and Code != '' ">AND log.entityCode =~'.*${Code}.*' </if>
            <if test="status != null and status != ''">AND log.synStatus = '${status}'</if>
        </where>
        RETURN log.id, log.pid, log.dataSourceId, log.databaseName, log.tableName,log.viewName,log.entityCode, log.synStatus, log.error, menu.code as
        menuCode, d.code as dataSourceCode, log.createTime ,log.sinkDataBaseName,log.sinkDataSourceId,log.useCreateTable,log.entityType ORDER BY log.createTime desc
        $$) as (id BIGINT,pid BIGINT,dataSourceId BIGINT,databaseName TEXT,tableName TEXT,viewName TEXT,entityCode TEXT,synStatus TEXT,error TEXT,menuCode
        TEXT,dataSourceCode TEXT,createTime TEXT,sinkDataBaseName TEXT,sinkDataSourceId TEXT,useCreateTable BOOLEAN, entityType TEXT)
    </select>

</mapper> 