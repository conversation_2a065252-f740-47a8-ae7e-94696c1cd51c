<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.element.mapper.DataEntityTableMapper">

    <sql id="table">
        RETURN ${alias}.id, ${alias}.dataEntityId,
            ${alias}.code,
            ${alias}.name,
            ${alias}.description,
            ${alias}.dataSourceId,
            ${alias}.databaseName,
            ${alias}.syncFromDataSourceId,
            ${alias}.syncFromDataBaseName,
            ${alias}.syncFromTableName,
            ${alias}.syncIsView,
            ${alias}.tableName,
            ${alias}.isRead,
            ${alias}.createTableWay,
            ${alias}.createTablesql,
            ${alias}.createTableConfig,
            ${alias}.createTime,
            ${alias}.updateTime
            $$) as (id BIGINT,
            dataEntityId BIGINT,
            code TEXT,
            name TEXT,
            description TEXT,
            dataSourceId BIGINT,
            databaseName TEXT,
            syncFromDataSourceId BIGINT,
            syncFromDataBaseName TEXT,
            syncFromTableName TEXT,
            syncIsView BOOLEAN,
            tableName TEXT,
            isRead BOOLEAN,
            createTableWay TEXT,
            createTablesql TEXT,
            createTableConfig TEXT,
            createTime TEXT,
            updateTime TEXT)
    </sql>

    <select id="insertTable" parameterType="com.datalink.fdop.element.api.domain.DataEntityTable" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (de:d_e_data_entity) WHERE de.id = ${dataEntityTable.dataEntityId}
            CREATE (de)-[:d_e_data_entity_table_edge]->(det:d_e_data_entity_table
                                               ${@com.datalink.fdop.element.utils.DomainAgeUtils@getEntityTableAgeStr(dataEntityTable)})-[:d_e_data_element_table_edge]->(de)
            RETURN id(det), properties(det)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.element.api.domain.DataEntityTable" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_e_data_entity_table)
        WHERE node.id = ${id}
        <set>
            <if test="dataSourceId != null">node.dataSourceId = ${dataSourceId},</if>
            <if test="databaseName != null and databaseName != ''">node.databaseName = '${databaseName}',</if>
            <if test="tableName != null and tableName != ''">node.tableName = '${tableName}',</if>
            <if test="isRead != null">node.isRead = ${isRead},</if>
            <if test="createTableWay != null">node.createTableWay = '${createTableWay}',</if>
            <if test="createTablesql != null and createTablesql != ''">node.createTablesql = '${createTablesql}',</if>
            <if test="createTableConfig != null and createTableConfig != ''">node.createTableConfig =
                '${createTableConfig}',
            </if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteDataSourceTable" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (det:d_e_data_entity_table) -[edge:d_e_data_entity_table_datasource_edge]-(d:datasource)
            WHERE det.id = ${tableId}
            DETACH DELETE edge
            RETURN id(edge), properties(edge) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="updateDataSourceTable" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (det:d_e_data_entity_table), (d:datasource)
                                   WHERE det.id = ${tableId} AND d.id = ${dataSourceId}
                                   CREATE (det)-[:d_e_data_entity_table_datasource_edge {startTable:'d_e_data_entity_table',
                               endTable:'datasource'}]->(d)
            -[:d_e_data_entity_table_datasource_edge {startTable:'datasource', endTable:'d_e_data_entity_table'}]->(det)
            RETURN id(det), properties(det) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="deleteEntityTableTableEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (table:d_g_synchronization_table)-[edge:d_e_data_entity_table_syn_table_edge]-(det:d_e_data_entity_table)
        WHERE det.id = ${tableId}
        DETACH DELETE edge
        RETURN id(edge), properties(edge) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="createEntityTableTableEdge" resultType="int">
        <if test="databaseName != null and databaseName != ''">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource) -[edge1]->(database:d_g_synchronization_database)-[edge2]->(schema
            :d_g_synchronization_schema)
            -[edge3]->(table :d_g_synchronization_table), (det:d_e_data_entity_table)
            WHERE det.id = ${tableId} AND d.id = ${dataSourceId} AND database.code = '${databaseName}' AND
            schema.code = '${schemaName}' AND table.code = '${tableName}'
            CREATE (det)-[:d_e_data_entity_table_syn_table_edge {startTable:'d_e_data_entity_table',
            endTable:'d_g_synchronization_table'}]->(table)
            RETURN id(det), properties(det) $$) as (id ag_catalog.agtype,properties
            ag_catalog.agtype)
        </if>
        <if test="databaseName == null or databaseName == ''">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource) -[edge1]->(schema:d_g_synchronization_schema)
            -[edge2]->(table :d_g_synchronization_table), (det:d_e_data_entity_table)
            WHERE det.id = ${tableId} AND d.id = ${dataSourceId} AND
            schema.code = '${schemaName}' AND table.code = '${tableName}'
            CREATE (det)-[:d_e_data_entity_table_syn_table_edge {startTable:'d_e_data_entity_table',
            endTable:'d_g_synchronization_table'}]->(table)
            RETURN id(det), properties(det) $$) as (id ag_catalog.agtype,properties
            ag_catalog.agtype)
        </if>
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (det:d_e_data_entity_table)
        WHERE det.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE det
        RETURN id(det),properties(det) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.element.api.domain.DataEntityTable">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (det:d_e_data_entity_table) WHERE det.id = ${tableId}
        <include refid="table">
            <property name="alias" value="det"/>
        </include>
    </select>

    <select id="selectByIdList" parameterType="java.util.List" resultType="com.datalink.fdop.element.api.domain.DataEntityTable">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (det:d_e_data_entity_table) WHERE det.id in
        <foreach collection="tableIds" item="id" open="[" separator=","  close="]">
            ${id}
        </foreach>
        <include refid="table">
            <property name="alias" value="det"/>
        </include>
    </select>

    <select id="selectDataEntityTableVoByDataEntityIds" parameterType="java.util.List" resultType="com.datalink.fdop.element.api.domain.DataEntityTableVo">
        SELECT
        det.*,entity.code as entityCode,entity.name as entityName,entity.description as entityDesc
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (det:d_e_data_entity_table) WHERE det.dataEntityId in
        <foreach collection="dataEntityIds" item="dataEntityId" open="[" separator=","  close="]">
            ${dataEntityId}
        </foreach>
        RETURN det.id, det.dataEntityId,det.code,det.name,det.description,det.dataSourceId,det.databaseName,det.syncFromDataSourceId,det.syncFromDataBaseName,
        det.syncFromTableName,det.syncIsView,det.tableName,det.isRead,det.createTableWay,det.createTablesql,det.createTableConfig,det.createTime,det.updateTime$$)
        as det (id BIGINT,dataEntityId BIGINT,code TEXT,name TEXT,description TEXT,dataSourceId BIGINT,databaseName TEXT,syncFromDataSourceId BIGINT,syncFromDataBaseName TEXT,syncFromTableName TEXT,syncIsView BOOLEAN,
        tableName TEXT,isRead BOOLEAN,createTableWay TEXT,createTablesql TEXT,createTableConfig TEXT,createTime TEXT,updateTime TEXT)
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (entity:d_e_data_entity) RETURN entity.id,entity.code,entity.name,entity.description $$) as entity (id BIGINT,code TEXT,name TEXT,description TEXT) on det.dataEntityId = entity.id
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.element.api.domain.DataEntityTable">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (det:d_e_data_entity_table) WHERE det.dataEntityId = ${dataEntityId} AND det.code = '${code}'
        <include refid="table">
            <property name="alias" value="det"/>
        </include>
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.element.api.domain.DataEntityTable">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (det:d_e_data_entity_table) WHERE det.dataEntityId = ${dataEntityId}
        AND det.code = '${code}'
        AND det.id &lt;&gt; ${tableId}
        <include refid="table">
            <property name="alias" value="det"/>
        </include>
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.element.api.domain.DataEntityTable" resultType="com.datalink.fdop.element.api.domain.DataEntityTable">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (de:d_e_data_entity) -[dete:d_e_data_entity_table_edge]->(det:d_e_data_entity_table)
        WHERE de.id = ${dataEntityId}
        <!-- <if test="code != null and code != ''">AND det.code = '${code}'</if>-->
        <!-- <if test="entityTableType != null ">AND det.entityTableType = '${entityTableType}'</if>-->
        <!--
                <if test="status != null ">AND det.status = ${status}</if>
        -->
        <if test="searchCondition != null ">AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("det",searchCondition)})
        </if>
        WITH det
        ORDER BY det.createTime desc
        <include refid="table">
            <property name="alias" value="det"/>
        </include>
    </select>

    <select id="selectTotal" resultType="int">
        SELECT count(0)
        FROM
            ag_catalog.cypher('zjdata_graph', $$
                MATCH (de:d_e_data_entity) -[dete:d_e_data_entity_table_edge]->(det:d_e_data_entity_table)
                WHERE de.id = ${dataEntityId}
                RETURN id(det), properties(det) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectTableMapping" resultType="com.datalink.fdop.element.api.domain.DataEntityTableMapping">
        SELECT *
        FROM
            ag_catalog.cypher('zjdata_graph', $$
                MATCH (de:d_e_data_entity) -[:d_e_data_entity_table_edge]->(det:d_e_data_entity_table)-[:d_e_data_entity_table_mapping_edge]->(dem:d_e_data_entity_table_mapping)
                WHERE de.id = ${dataEntityId} AND det.id = ${tableId} AND dem.dataEntityId =
                                              ${dataEntityId} AND dem.dataTableId = ${tableId}
                RETURN dem.dataEntityId, dem.dataTableId, dem.fieldId, dem.entityInsertType,
                              dem.dataElementType,
                              dem.fieldName
                                  $$) as (dataEntityId BIGINT,dataTableId BIGINT,fieldId BIGINT,entityInsertType TEXT,dataElementType TEXT,fieldName TEXT)
    </select>

</mapper> 