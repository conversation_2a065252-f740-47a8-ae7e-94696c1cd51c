<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.drive.mapper.DataSourceMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.drive.api.domain.DataSource"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insertDataSourceGraph" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (d:datasource ${@com.datalink.fdop.drive.utils.DomainAgeUtils@getDataSourceAgeString(dataSource)}) RETURN id(d),
                               properties(d)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateDataSourceGraph" parameterType="com.datalink.fdop.drive.api.domain.DataSource" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (d:datasource)
        WHERE d.id = ${id}
        <set>
            <if test="code != null and code != ''">d.code = '${code}',</if>
            <if test="name != null and name != ''">d.name = '${name}',</if>
            <if test="pid != null and pid != ''">d.pid = ${pid},</if>
            <if test="menuName != null and menuName != ''">d.menuName = '${menuName}',</if>
            <if test="description != null and description != ''">d.description = '${description}',</if>
            <if test="type != null">d.type = '${type}',</if>
            <if test="dataSourceBasicInfo != null and dataSourceBasicInfo != ''">d.dataSourceBasicInfo =
                '${dataSourceBasicInfo}',
            </if>
            <if test="isRead != null">d.isRead = ${isRead},</if>
            d.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            d.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(d), properties(d)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteDataSourceGraph" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (d:datasource) WHERE d.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE d
        RETURN id(d), properties(d)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource) WHERE d.id = ${id}
            RETURN id(d), properties(d)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource) WHERE d.code = '${code}'
            RETURN id(d), properties(d)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.drive.api.domain.DataSource" resultType="com.datalink.fdop.drive.api.domain.DataSource">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (d:datasource)
        <where>
            <if test="dataSource.code != null and dataSource.code != ''">and d.code =~'.*${dataSource.code}.*'</if>
            <if test="dataSource.name != null and dataSource.name != ''">and d.name =~'.*${dataSource.name}.*'</if>
            <if test="dataSource.description != null and dataSource.description != ''">and d.description
                =~'.*${dataSource.description}.*'
            </if>
            <if test="dataSource.type != null and dataSource.type != ''">and d.type = '${dataSource.type}'</if>
            <if test="dataSource.isRead != null">and d.isRead = ${dataSource.isRead}</if>
        </where>
        WITH d
        ORDER BY d.code ${sort}
        RETURN
        d.id,d.code,d.name,d.description,d.type,d.isRead,d.createBy,d.updateBy,d.createTime,d.updateTime,d.dataSourceBasicInfo
        $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,type TEXT,isRead BOOLEAN,createBy TEXT,updateBy
        TEXT,createTime TEXT,updateTime TEXT,dataSourceBasicInfo TEXT)
    </select>

    <select id="selectUnStructuredList" parameterType="com.datalink.fdop.drive.api.domain.DataSource" resultType="com.datalink.fdop.drive.api.domain.DataSource">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (d:datasource)
            where d.type in ['ftp','sftp']
            <if test="dataSource.code != null and dataSource.code != ''">and d.code =~'.*${dataSource.code}.*'</if>
            <if test="dataSource.name != null and dataSource.name != ''">and d.name =~'.*${dataSource.name}.*'</if>
            <if test="dataSource.description != null and dataSource.description != ''">and d.description
                =~'.*${dataSource.description}.*'
            </if>
            <if test="dataSource.isRead != null">and d.isRead = ${dataSource.isRead}</if>
        WITH d
        ORDER BY d.code ${sort}
        RETURN
        d.id,d.code,d.name,d.description,d.type,d.isRead,d.createBy,d.updateBy,d.createTime,d.updateTime,d.dataSourceBasicInfo
        $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,type TEXT,isRead BOOLEAN,createBy TEXT,updateBy
        TEXT,createTime TEXT,updateTime TEXT,dataSourceBasicInfo TEXT)
    </select>

    <select id="selectTotal" resultType="int">
        SELECT count(0)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource) RETURN id(d), properties(d)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectSourceEntityEdge" resultType="String">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource) -[]->(table :d_e_data_entity_table)-[]->(entity:d_e_data_entity)
            WHERE d.id = ${id}
            RETURN entity.code
            $$) as (code TEXT)
    </select>


    <select id="selectDataSourceList" resultType="com.datalink.fdop.drive.api.domain.DataSource">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (d:datasource)
        <where>
            <if test="ids != null and ids.size > 0">and d.id IN
                <foreach collection="ids" item="id" open="[" separator="," close="]">
                    ${id}
                </foreach>
            </if>
        </where>
        WITH d
        RETURN
        d.id,d.code,d.name,d.description,d.type,d.isRead,d.createBy,d.updateBy,d.createTime,d.updateTime,d.dataSourceBasicInfo
        $$) as (id BIGINT,code TEXT,name TEXT,description TEXT,type TEXT,isRead BOOLEAN,createBy TEXT,updateBy
        TEXT,createTime TEXT,updateTime TEXT,dataSourceBasicInfo TEXT)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_e_data_source_menu) -[nme:node_menu_edge]->(nodeE:datasource)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="batchUpdatePidById" parameterType="com.datalink.fdop.drive.api.domain.DataSourceMenu"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (d:datasource)
        WHERE d.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET d.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createEntityAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu), (node:datasource)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_e_data_source_menu',endTable:'datasource'}]->(node)-[:node_menu_edge
        {startTable:'datasource',endTable:'d_e_data_source_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectNodeTree" resultType="com.datalink.fdop.drive.api.model.DataSourceTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:datasource)
        <where>
            <if test="code != null and code != ''">and node.code =~'.*${code}.*'</if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description,'NODE' as menuType,node.type as dataSourceType
        ORDER BY node.code ${sort}
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,dataSourceType TEXT)
    </select>

    <select id="overview" resultType="com.datalink.fdop.drive.api.domain.DataSource">
        WITH overview as (
        <if test="pid == -1">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:datasource)
            WHERE node.pid = -1
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,'顶级菜单' as
            menuName,'topMenu' as menuCode,node.createTime,node.updateTime,node.type
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,menuCode TEXT,createTime
            TEXT,updateTime TEXT,type TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:datasource) -[:node_menu_edge]->(menu:d_e_data_source_menu)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.name as
        menuName,menu.code as menuCode,node.createTime,node.updateTime,node.type
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,menuCode TEXT,createTime TEXT,updateTime
        TEXT,type TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>

    <select id="createElementAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu), (node:datasource)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_e_data_source_menu',endTable:'datasource'}]->(node)-[:node_menu_edge
        {startTable:'datasource',endTable:'d_e_data_source_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteElementAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),properties(nme1) $$)
        as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper>