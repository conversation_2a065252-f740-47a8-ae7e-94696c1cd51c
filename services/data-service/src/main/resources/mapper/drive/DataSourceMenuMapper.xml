<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.drive.mapper.DataSourceMenuMapper">

    <sql id="sourceMenu">
        RETURN ${alias}.id, ${alias}.pid,
        ${alias}.code,
        ${alias}.name,
        ${alias}.description,
        ${alias}.createTime,
        ${alias}.updateTime
        $$) as (id BIGINT, pid BIGINT,
        code TEXT,
        name TEXT,
        description TEXT,
        createTime TEXT,
        updateTime TEXT)
    </sql>

    <select id="selectByCode" resultType="com.datalink.fdop.drive.api.domain.DataSourceMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu) WHERE menu.code = '${code}'
        <include refid="sourceMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="insertDataSourceMenu" parameterType="String" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:d_e_data_source_menu ${@com.datalink.fdop.drive.utils.DomainAgeUtils@getDataSourceMenuAgeStr(dataSourceMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createDataSourceMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_e_data_source_menu), (menuE:d_e_data_source_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'d_e_data_source_menu',endTable:'d_e_data_source_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'d_e_data_source_menu',endTable:'d_e_data_source_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.drive.api.domain.DataSourceMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu) WHERE menu.id = ${id}
        <include refid="sourceMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_e_data_source_menu) -[mme:menu_menu_edge]->(menuE:d_e_data_source_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id BIGINT)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.drive.api.domain.DataSourceMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu)
        WHERE menu.id = ${id}
        <set>
            <if test="pid != null">menu.pid = ${pid},</if>
            <if test="code != null and code != ''">menu.code = '${code}',</if>
            <if test="name != null">menu.name = '${name}',</if>
            <if test="description != null">menu.description = '${description}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" parameterType="com.datalink.fdop.drive.api.domain.DataSourceMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.drive.api.domain.DataSourceMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id}
        <include refid="sourceMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="deleteSourceMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectMenuTree" resultType="com.datalink.fdop.drive.api.model.DataSourceTree">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_e_data_source_menu)
        <where>
            <if test="code != null and code != ''">and menu.code =~'.*${code}.*'</if>
        </where>
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType, menu.serialNumber
        ORDER BY menu.serialNumber ${sort}
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,serialNumber TEXT)
    </select>

</mapper>