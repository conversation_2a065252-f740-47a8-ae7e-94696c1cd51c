<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.data.mapper.DataServeMenuMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.data.api.domain.DataServeMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <sql id="dataServerMenu">
        RETURN ${alias}.id,
            ${alias}.pid,
            ${alias}.code,
            ${alias}.name,
            ${alias}.description,
            ${alias}.createTime,
            ${alias}.updateTime
            $$) as (id BIGINT, pid BIGINT,
            code TEXT,
            name TEXT,
            description TEXT,
            createTime TEXT,
            updateTime TEXT)
    </sql>


    <select id="save" parameterType="string" resultType="int">
        select count(1)
        from ag_catalog.cypher('zjdata_graph', $$ CREATE (u:d_c_data_share_menu ${ageStr}) RETURN id(u), properties(u)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findByCode" parameterType="java.lang.String" resultMap="vlabelItem">
         SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
             MATCH(n:d_c_data_share_menu)
                WHERE n.code='${code}'
             RETURN id(n),properties(n)
                $$)
            as(id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findAll" parameterType="java.lang.String" resultType="com.datalink.fdop.data.vo.DataServeTreeVo">
                WITH graph_query as (
                SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
                    MATCH(n:d_c_data_share_menu)
                    WITH n
                    ORDER BY n.code
                    <if test="sort != null and sort != ''">
                        '${sort}'
                    </if>
                    return n.id ,n.code,n.pid,n.name,n.description
                    $$)
                    as(id TEXT,code TEXT,pid TEXT,name TEXT,description TEXT))
                SELECT  	id,
					        code,
					        pid,
					        description,
					        name,
					'MENU' as menuType
            FROM graph_query
    </select>

    <select id="updateById"  resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:d_c_data_share_menu {id: ${dataServeMenu.id}})
        <set>
            <if test="dataServeMenu.pid != null and dataServeMenu.pid != ''">u.pid = ${dataServeMenu.pid},</if>
            <if test="dataServeMenu.name != null and dataServeMenu.name != ''">u.name = '${dataServeMenu.name}',</if>
            <if test="dataServeMenu.code != null  and dataServeMenu.code != ''">u.code = '${dataServeMenu.code}',</if>
            <if test="dataServeMenu.description != null and  dataServeMenu.description != ''">u.description = '${dataServeMenu.description}',</if>
        </set>
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="deleteAllById" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:d_c_data_share_menu)
        WHERE u.id in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE u
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findAuthGroup" resultMap="vlabelItem">
        SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
	    MATCH(n:d_c_data_share_menu)
        <if test="conditionSql != null and conditionSql != ''">
            where ${conditionSql}
        </if>
        WITH n
        ORDER BY n.code
	    RETURN id(n),properties(n)

        $$)
    as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findAuthGroupTotal" resultType="int">
        SELECT count(1) FROM ag_catalog.cypher( 'zjdata_graph',$$
	    MATCH(n:d_c_data_share_menu)
        <if test="conditionSql != null and conditionSql != ''">
            where ${conditionSql}
        </if>
	    RETURN id(n),properties(n)
        ORDER BY id(n)
        $$)
    as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="saveMenuRelation" resultType="int">
        SELECT count(1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH (g:d_c_data_share_menu), (m:d_c_data_share_menu)
            WHERE g.id = ${id} AND m.id = ${pid}
            CREATE gm = ((g)-[:menu_menu_edge {startTable:'d_c_auth_group_menu',endTable:'d_c_auth_group_menu'}]
            ->(m)-[:menu_menu_edge {startTable:'d_c_auth_group_menu',endTable:'d_c_auth_group_menu'}]->(g))
            RETURN gm
        $$) as (gm agtype)
    </select>


    <select id="findMenuByPidAndDelRelation" resultMap="vlabelItem">
        SELECT *  FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:d_c_data_share_menu)-[r:menu_menu_edge]-(m:d_c_data_share_menu)
        where n.id=${id}
        DETACH DELETE r
        RETURN  id(r),properties(r)
        $$)
        as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="delRelationByPid" resultType="int">
        SELECT count (1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:d_c_data_share_menu)-[r:menu_menu_edge]-(m:d_c_data_share_menu)
        where n.id=${id} and m.id=${pid}
        DETACH DELETE r
        RETURN  id(r),properties(r)
        $$)
        as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteById" resultType="int">
        SELECT count (1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:d_c_data_share_menu)-[r:menu_menu_edge]-()
        where n.id=${id}
        DETACH DELETE r,n
         RETURN  id(r),properties(r)
        $$)
        as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="findById" resultMap="vlabelItem">
        SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
             MATCH(n:d_c_data_share_menu)
                WHERE n.id=${id}
             RETURN id(n),properties(n)
                $$)
            as(id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_c_data_share_menu) -[mme:menu_menu_edge]->(menuE:d_c_data_share_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById"  resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_c_data_share_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createDataShareMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_c_data_share_menu), (menuE:d_c_data_share_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'d_c_data_share_menu',endTable:'d_c_data_share_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'d_c_data_share_menu',endTable:'d_c_data_share_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_c_data_share_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.data.api.domain.DataServeMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_c_data_share_menu) WHERE menu.id = ${id}
        <include refid="dataServerMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

</mapper>
