<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.data.mapper.DataPreviewMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.data.api.domain.DataServe"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>


    <select id="save" parameterType="string" resultType="int">
        select count(1)
        from ag_catalog.cypher('zjdata_graph', $$ CREATE (u:d_c_data_share ${ageStr}) RETURN id(u), properties(u)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findByCode" parameterType="java.lang.String" resultMap="vlabelItem">
         SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
             MATCH(n:d_c_data_share)
                WHERE n.code='${code}'
             RETURN id(n),properties(n)
                $$)
            as(id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="updateById"  resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:d_c_data_share {id: '${dataServe.id}'})
        <set>
            <if test="dataServe.pid != null and dataServe.pid != ''">u.pid = '${dataServe.pid}',</if>
            <if test="dataServe.name != null and dataServe.name != ''">u.name = '${dataServe.name}',</if>
            <if test="dataServe.code != null  and dataServe.code != ''">u.code = '${dataServe.code}',</if>
            <if test="dataServe.description != null and  dataServe.description != ''">u.description = '${dataServe.description}',</if>
            <if test="dataServe.userId != null and  dataServe.userId != ''">u.userId = '${dataServe.userId}',</if>
            <if test="dataServe.type != null and  dataServe.type != ''">u.type = '${dataServe.type}',</if>
            <if test="dataServe.datasourceId != null and  dataServe.datasourceId != ''">u.datasourceId = '${dataServe.datasourceId}',</if>
            <if test="dataServe.schemaName != null and  dataServe.schemaName != ''">u.schemaName = '${dataServe.schemaName}',</if>
            <if test="dataServe.tableName != null and  dataServe.tableName != ''">u.tableName = '${dataServe.tableName}',</if>
            <if test="dataServe.querySql != null and  dataServe.querySql != ''">u.querySql = '${dataServe.querySql}',</if>
            <if test="dataServe.columnPermission != null and  dataServe.columnPermission != ''">u.columnPermission = '${dataServe.columnPermission}',</if>
            <if test="dataServe.isShare != null and  dataServe.isShare != ''">u.isShare = '${dataServe.isShare}',</if>
            <if test="dataServe.isCache != null and  dataServe.isCache != ''">u.isCache = '${dataServe.isCache}',</if>
            <if test="dataServe.cacheUpdateTime != null and  dataServe.cacheUpdateTime != ''">u.cacheUpdateTime = '${dataServe.cacheUpdateTime}',</if>
            <if test="dataServe.isCurrentLimit != null and  dataServe.isCurrentLimit != ''">u.isCurrentLimit = '${dataServe.isCurrentLimit}',</if>
            <if test="dataServe.currentLimitLevel != null and  dataServe.currentLimitLevel != ''">u.currentLimitLevel = '${dataServe.currentLimitLevel}',</if>
            <if test="dataServe.createTime != null and  dataServe.createTime != ''">u.createTime = '${dataServe.createTime}',</if>
            <if test="dataServe.updateTime != null and  dataServe.updateTime != ''">u.updateTime = '${dataServe.updateTime}',</if>
            <if test="dataServe.orderBy != null and  dataServe.orderBy != ''">u.orderBy = '${dataServe.orderBy}',</if>
            <if test="dataServe.shareParams != null and  dataServe.shareParams != ''">u.shareParams = '${dataServe.shareParams}',</if>
        </set>
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="deleteAllById" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:d_c_data_share)
        WHERE u.id in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            '${id}'
        </foreach>
        DELETE u
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>



    <select id="saveMenuRelation" resultType="int">
        SELECT count(1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH (g:d_c_data_share), (m:d_c_data_share_menu)
            WHERE g.id = '${id}' AND m.id = '${pid}'
            CREATE gm = ((g)-[:node_menu_edge {startTable:'d_c_data_share',endTable:'d_c_data_share_menu'}]->(m)-[:node_menu_edge {startTable:'d_c_data_share_menu',endTable:'d_c_data_share'}]->(g))
            RETURN gm
        $$) as (gm agtype)
    </select>


    <select id="findByPidAndDelRelation" resultMap="vlabelItem">
        SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:d_c_data_share)-[r:node_menu_edge]-(m:d_c_data_share_menu)
        where m.id='${id}'
        DELETE r
        RETURN DISTINCT  id(n),properties(n)
        $$)
        as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="delRelationByPid" resultType="int">
        SELECT count (1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:d_c_data_share)-[r:node_menu_edge]-(m:d_c_data_share_menu)
        where d.id='${id}' and m.id='${pid}'
        DELETE r
        $$)
        as (a ag_catalog.agtype)
    </select>

</mapper>
