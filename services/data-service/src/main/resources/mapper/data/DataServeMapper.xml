<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.data.mapper.DataServeMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.data.api.domain.DataServe"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>


    <select id="save" parameterType="string" resultType="int">
        select count(1)
        from ag_catalog.cypher('zjdata_graph', $$ CREATE (u:d_c_data_share ${ageStr}) RETURN id(u), properties(u)
            $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findByCode" parameterType="java.lang.String" resultMap="vlabelItem">
         SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
             MATCH(n:d_c_data_share)
                WHERE n.code='${code}'
             RETURN id(n),properties(n)
                $$)
            as(id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="updateById"  resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:d_c_data_share {id: ${dataServe.id}})
        <set>
            <if test="dataServe.pid != null ">u.pid = ${dataServe.pid},</if>
            u.name = '${dataServe.name}',
            <if test="dataServe.code != null  and dataServe.code != ''">u.code = '${dataServe.code}',</if>
            u.description = '${dataServe.description}',
            <if test="dataServe.userId != null ">u.userId = ${dataServe.userId},</if>
            <if test="dataServe.type != null and  dataServe.type != ''">u.type = '${dataServe.type}',</if>
            <if test="dataServe.datasourceId != null ">u.datasourceId = ${dataServe.datasourceId},</if>
            <if test="dataServe.schemaName != null and  dataServe.schemaName != ''">u.schemaName = '${dataServe.schemaName}',</if>
            <if test="dataServe.tableName != null and  dataServe.tableName != ''">u.tableName = '${dataServe.tableName}',</if>
            <if test="dataServe.querySql != null and  dataServe.querySql != ''">u.querySql = '${dataServe.querySql}',</if>
            <if test="dataServe.columnPermission != null and  dataServe.columnPermission != ''">u.columnPermission = '${dataServe.columnPermission}',</if>
            <if test="dataServe.entityColumnPermission != null and  dataServe.entityColumnPermission != ''">u.entityColumnPermission = '${dataServe.entityColumnPermission}',</if>
            <if test="dataServe.isShare != null">u.isShare = '${dataServe.isShare}',</if>
            <if test="dataServe.isCache != null ">u.isCache = '${dataServe.isCache}',</if>
            <if test="dataServe.cacheUpdateTime != null and  dataServe.cacheUpdateTime != ''">u.cacheUpdateTime = '${dataServe.cacheUpdateTime}',</if>
            <if test="dataServe.isCurrentLimit != null ">u.isCurrentLimit = '${dataServe.isCurrentLimit}',</if>
            <if test="dataServe.currentLimitLevel != null and  dataServe.currentLimitLevel != ''">u.currentLimitLevel = '${dataServe.currentLimitLevel}',</if>
            <if test="dataServe.createTime != null and  dataServe.createTime != ''">u.createTime = '${dataServe.createTime}',</if>
            <if test="dataServe.updateTime != null and  dataServe.updateTime != ''">u.updateTime = '${dataServe.updateTime}',</if>
            <if test="dataServe.orderBy != null and  dataServe.orderBy != ''">u.orderBy = '${dataServe.orderBy}',</if>
            <if test="dataServe.shareParams != null and  dataServe.shareParams != ''">u.shareParams = '${dataServe.shareParams}',</if>
            <if test="dataServe.entityId != null ">u.entityId = ${dataServe.entityId},</if>
            <if test="dataServe.entityTableId != null and  dataServe.entityTableId != ''">u.entityTableId = ${dataServe.entityTableId},</if>
        </set>
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="deleteAllById" resultType="int">
        SELECT count(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (u:d_c_data_share)
        WHERE u.id in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE u
        RETURN id(u), properties(u)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>



    <select id="saveMenuRelation" resultType="int">
        SELECT count(1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH (g:d_c_data_share), (m:d_c_data_share_menu)
            WHERE g.id = ${id} AND m.id = ${pid}
            CREATE gm = ((g)-[:node_menu_edge {startTable:'d_c_data_share',endTable:'d_c_data_share_menu'}]->(m)-[:node_menu_edge {startTable:'d_c_data_share_menu',endTable:'d_c_data_share'}]->(g))
            RETURN gm
        $$) as (gm agtype)
    </select>


    <select id="findByPidAndDelRelation" resultMap="vlabelItem">
        SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:d_c_data_share)-[r:node_menu_edge]-(m:d_c_data_share_menu)
        where m.id=${id}
        DELETE r
        RETURN DISTINCT  id(n),properties(n)
        $$)
        as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createServeEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (share:d_c_data_share), (entity:d_e_data_entity)
        WHERE share.id = ${id} AND entity.id = ${entityId}
        CREATE (share)-[edge:d_c_data_share_entity_edge
        {startTable:'d_c_data_share',endTable:'d_e_data_entity'}]->(entity)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteServeEntityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (share:d_c_data_share) -[edge]- (entity:d_e_data_entity) WHERE share.id = ${id}
            DELETE edge RETURN id(edge),
                               properties(edge) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="deleteShareTableEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (share:d_c_data_share)-[edge:d_c_data_share_syn_table_edge]-(table:d_g_synchronization_table)
            WHERE share.id = ${id}
            DETACH DELETE edge
            RETURN id(edge), properties(edge) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="createShareTableEdge" resultType="int">
        <if test="databaseName != null and databaseName != ''">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource) -[edge1]->(database:d_g_synchronization_database)-[edge2]->(schema
            :d_g_synchronization_schema)
            -[edge3]->(table :d_g_synchronization_table), (share:d_c_data_share)
            WHERE share.id = ${id} AND d.id = ${dataSourceId} AND database.code = '${databaseName}' AND
            schema.code = '${schemaName}' AND table.code = '${tableName}'
            CREATE (share)-[:d_c_data_share_syn_table_edge {startTable:'d_c_data_share',
            endTable:'d_g_synchronization_table'}]->(table)
            RETURN id(share), properties(share) $$) as (id ag_catalog.agtype,properties
            ag_catalog.agtype)
        </if>
        <if test="databaseName == null or databaseName == ''">
            SELECT COUNT(1)
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:datasource) -[edge1]->(schema:d_g_synchronization_schema)
            -[edge2]->(table :d_g_synchronization_table), (share:d_c_data_share)
            WHERE share.id = ${id} AND d.id = ${dataSourceId} AND
            schema.code = '${schemaName}' AND table.code = '${tableName}'
            CREATE (share)-[:d_c_data_share_syn_table_edge {startTable:'d_c_data_share',
            endTable:'d_g_synchronization_table'}]->(table)
            RETURN id(share), properties(share) $$) as (id ag_catalog.agtype,properties
            ag_catalog.agtype)
        </if>
    </select>

    <select id="delRelationByPid" resultType="int">
        SELECT count (1) FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:d_c_data_share)-[r:node_menu_edge]-(m:d_c_data_share_menu)
        where n.id=${id} and m.id=${pid}
        DELETE r
        RETURN  id(r),properties(r)
        $$)
        as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="queryDataShare" parameterType="com.datalink.fdop.data.api.domain.DataServe"
            resultType="com.datalink.fdop.data.api.domain.DataServe">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_c_data_share) WHERE node.pid = -1
        <if test="searchCondition != null ">AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
        </if>
        RETURN node.id, node.pid, node.code, node.name, node.description, '顶级菜单' as
        menuName,node.type,node.isShare
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,type TEXT,isShare TEXT)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_c_data_share) -[:node_menu_edge]->(menu:d_c_data_share_menu)
        <if test="searchCondition != null ">WHERE
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
        </if>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as menuName,node.type,node.isShare
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,type TEXT,isShare TEXT)
    </select>


    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_c_data_share) WHERE node.id = ${id}
             RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_c_data_share) WHERE node.code = '${code}'
            RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="findAll" parameterType="java.lang.String" resultType="com.datalink.fdop.data.vo.DataServeTreeVo">
        WITH graph_query as (
        SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
        MATCH(n:d_c_data_share)
        WITH n
        ORDER BY n.code
        <if test="sort != null and sort != ''">
            ${sort}
        </if>
        return n.id ,n.code,n.pid,n.name,n.description
        $$)
        as(id TEXT,code TEXT,pid TEXT,name TEXT,description TEXT))
        SELECT  	id,
        code,
        pid,
        description,
        name,
        'NODE' as menuType
        FROM graph_query
    </select>
    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_c_data_share_menu) -[nme:node_menu_edge]->(nodeE:d_c_data_share)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>
    <select id="bacthUpdatePidById"  resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_c_data_share)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createDataShareMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_c_data_share_menu), (node:d_c_data_share)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_c_data_share_menu',endTable:'d_c_data_share'}]->(node)-[:node_menu_edge
        {startTable:'d_c_data_share',endTable:'d_c_data_share_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectShareStatusById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_c_data_share) WHERE node.id = ${id}
            and  node.isShare='true'
             RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteDataShareAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menuS.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createDataServeAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_c_data_share_menu), (node:d_c_data_share)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_c_data_share_menu',endTable:'d_c_data_share'}]->(node)-[:node_menu_edge
        {startTable:'d_c_data_share',endTable:'d_c_data_share_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.data.api.domain.DataServe">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_c_data_share) WHERE node.code = '${code}' AND node.id &lt;&gt; ${id}
            RETURN node.id, node.pid, node.code, node.name, node.description, node.type,node.isShare
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, type TEXT,isShare TEXT)
    </select>

</mapper>
