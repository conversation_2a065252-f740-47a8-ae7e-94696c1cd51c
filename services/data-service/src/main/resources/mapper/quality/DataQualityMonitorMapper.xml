<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.quality.mapper.DataQualityMonitorMapper">

    <select id="querySerialNumber" resultType="int">
        SELECT (count(1) + 1) * 10
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality_monitor) RETURN id(node), properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="createQualityMonitorAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_monitor_menu), (node:d_q_data_quality_monitor)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_q_data_quality_monitor_menu',endTable:'d_q_data_quality_monitor'}]->(node)-[:node_menu_edge
        {startTable:'d_q_data_quality_monitor',endTable:'d_q_data_quality_monitor_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertQualityMonitor" parameterType="com.datalink.fdop.quality.api.domain.DataQualityMonitor"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_q_data_quality_monitor ${@com.datalink.fdop.quality.utils.DomainAgeUtils@getDataQualityMonitorAgeStr(dataQualityMonitor)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.quality.api.domain.DataQualityMonitor" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality_monitor)
        WHERE node.id = ${id}
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null">node.code = '${code}',</if>
            <if test="name != null">node.name = '${name}',</if>
            <if test="description != null">node.description = '${description}',</if>
            <if test="scheduleId != null">node.scheduleId = ${scheduleId},</if>
            <if test="serialNumber != null">node.serialNumber = ${serialNumber},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createMonitorAndTaskEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality_monitor), (task:t_ds_task_definition)
                                   WHERE quality.id = ${id} AND task.code = ${taskCode}
                                   CREATE (quality)-[edge:d_q_data_quality_monitor_task_edge
                                   {startTable:'d_q_data_quality_monitor', endTable:'t_ds_task_definition'}]->(task)-[:d_q_data_quality_standard_edge
        {startTable:'t_ds_task_definition',endTable:'d_q_data_quality_monitor'}]->(quality)
        RETURN id(edge), properties(edge)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="createMonitorAndProcessEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality_monitor), (process:t_ds_process_definition)
                                   WHERE quality.id = ${id} AND process.code = ${processCode}
                                   CREATE (quality)-[edge:d_q_data_quality_monitor_process_edge
                                   {startTable:'d_q_data_quality_monitor', endTable:'t_ds_process_definition'}]->(process)-[:d_q_data_quality_standard_edge
        {startTable:'t_ds_process_definition',endTable:'d_q_data_quality_monitor'}]->(quality)
        RETURN id(edge), properties(edge)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality_monitor)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteQualityMonitorAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality_monitor)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.quality.api.domain.DataQualityMonitor">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
                                                   MATCH (task:t_ds_task_definition) &lt; -[]-(node:d_q_data_quality_monitor)-[]->(process:t_ds_process_definition)
            WHERE node.id = ${id}
            RETURN node.id, node.pid, node.code, node.name, node.description, node.serialNumber, task.code as taskCode,
                               process.code as
        processCode, node.scheduleId
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,serialNumber INTEGER, taskCode BIGINT, processCode
        BIGINT, scheduleId INTEGER)
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.quality.api.domain.DataQualityMonitor">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality_monitor) WHERE node.code = '${code}'
            RETURN node.id, node.pid, node.code, node.name, node.description, node.serialNumber, node.scheduleId
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,serialNumber INTEGER, scheduleId INTEGER)
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.quality.api.domain.DataQualityMonitor">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality_monitor) WHERE node.code = '${code}' AND node.id &lt;&gt; ${id}
            RETURN node.id, node.pid, node.code, node.name, node.description, node.serialNumber, node.scheduleId
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,serialNumber INTEGER, scheduleId INTEGER)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_q_data_quality_monitor_menu) -[nme:node_menu_edge]->(nodeE:d_q_data_quality_monitor)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="selectQualityMonitorTree" resultType="com.datalink.fdop.quality.api.domain.DataQualityMonitorTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality_monitor)
        <where>
            <if test="code != null and code != ''">node.code =~'.*${code}.*'</if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, 'NODE' as menuType, node.serialNumber
        ORDER BY node.serialNumber ${sort},node.code ${sort}
        $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT,serialNumber INTEGER)
    </select>

    <select id="overview" resultType="com.datalink.fdop.quality.api.domain.DataQualityMonitor">
        WITH overview as (
        <if test="pid == -1">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality_monitor)
            WHERE node.pid = -1
            <if test="searchVo != null">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description,'顶级菜单' as
            menuName,node.serialNumber,node.createTime,node.updateTime,node.scheduleId
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,serialNumber
            INTEGER,createTime
            TEXT,updateTime TEXT,scheduleId INTEGER)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality_monitor) -[:node_menu_edge]->(menu:d_q_data_quality_monitor_menu)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.serialNumber,node.createTime,node.updateTime,node.scheduleId
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,serialNumber
        INTEGER,createTime TEXT,updateTime
        TEXT,scheduleId INTEGER)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,serialNumber ${sort},code ${sort}
    </select>

    <select id="bindMonitorTable" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (quality:d_q_data_quality_monitor), (table:d_g_synchronization_table)
        WHERE quality.id = ${id}
        <if test="tableIdss != null and tableIdss.size() > 0">
            AND
            <foreach collection="tableIdss" item="tableIds" index="" open="(" separator="OR" close=")">
                table.id IN
                <foreach collection="tableIds" item="tableId" index="" open="[" separator="," close="]">
                    ${tableId}
                </foreach>
            </foreach>
        </if>
        CREATE (quality)-[edge:d_q_data_quality_monitor_table_edge
        {startTable:'d_q_data_quality_monitor',
        endTable:'d_g_synchronization_table'}]->(table)-[:d_q_data_quality_monitor_table_edge
        {startTable:'d_g_synchronization_table',endTable:'d_q_data_quality_monitor'}]->(quality)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="unbindMonitorTable" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (quality:d_q_data_quality_monitor)-[edge:d_q_data_quality_monitor_table_edge]-(table:d_g_synchronization_table)
        WHERE quality.id = ${id}
        <if test="tableIdss != null and tableIdss.size() > 0">
            AND
            <foreach collection="tableIdss" item="tableIds" index="" open="(" separator="OR" close=")">
                table.id IN
                <foreach collection="tableIds" item="tableId" index="" open="[" separator="," close="]">
                    ${tableId}
                </foreach>
            </foreach>
        </if>
        DELETE edge
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bindMonitorView" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (quality:d_q_data_quality_monitor), (view:d_g_synchronization_view)
        WHERE quality.id = ${id}
        <if test="viewIdss != null and viewIdss.size() > 0">
            AND
            <foreach collection="viewIdss" item="viewIds" index="" open="(" separator="OR" close=")">
                view.id IN
                <foreach collection="viewIds" item="viewId" index="" open="[" separator="," close="]">
                    ${viewId}
                </foreach>
            </foreach>
        </if>
        CREATE (quality)-[edge:d_q_data_quality_monitor_view_edge
        {startTable:'d_q_data_quality_monitor',
        endTable:'d_g_synchronization_view'}]->(view)-[:d_q_data_quality_monitor_view_edge
        {startTable:'d_g_synchronization_view',endTable:'d_q_data_quality_monitor'}]->(quality)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="unbindMonitorView" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH
        (quality:d_q_data_quality_monitor)-[edge:d_q_data_quality_monitor_view_edge]-(view:d_g_synchronization_view)
        WHERE quality.id = ${id}
        <if test="viewIdss != null and viewIdss.size() > 0">
            AND
            <foreach collection="viewIdss" item="viewIds" index="" open="(" separator="OR" close=")">
                view.id IN
                <foreach collection="viewIds" item="viewId" index="" open="[" separator="," close="]">
                    ${viewId}
                </foreach>
            </foreach>
        </if>
        DELETE edge
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectMonitorTableList" resultType="String">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality_monitor) -[edge:d_q_data_quality_monitor_table_edge]->(table :d_g_synchronization_table)
            WHERE quality.id = ${id}
            RETURN table.id
            $$) as (id BIGINT)
    </select>

    <select id="selectMonitorViewList" resultType="String">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality_monitor) -[edge:d_q_data_quality_monitor_view_edge]->(view :d_g_synchronization_view)
            WHERE quality.id = ${id}
            RETURN view.id
            $$) as (id BIGINT)
    </select>

</mapper> 