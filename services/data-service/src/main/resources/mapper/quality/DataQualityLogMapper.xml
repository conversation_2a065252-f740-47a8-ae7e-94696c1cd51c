<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.quality.mapper.DataQualityLogMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.quality.api.domain.DataQualityLog"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insertQualityLog" parameterType="com.datalink.fdop.quality.api.domain.DataQualityLog" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_q_data_quality_log ${@com.datalink.fdop.quality.utils.DomainAgeUtils@getQualityLogAgeStr(dataQualityLog)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="creareQualityLogAndEntityEdge" parameterType="com.datalink.fdop.quality.api.domain.DataQualityLog"
            resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph', $$
            MATCH (log:d_q_data_quality_log), (entity:d_e_data_entity)
                                   WHERE log.id = ${id} AND entity.id = ${entityId}
                                   CREATE (log)-[edge:d_q_data_quality_log_entity_edge
                                   {startTable:'d_q_data_quality_log', endTable:'d_e_data_entity'}]->(entity)-[:d_q_data_quality_log_entity_edge
        {startTable:'d_e_data_entity',endTable:'d_q_data_quality_log'}]->(log)
        RETURN id(edge), properties(edge)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.quality.api.domain.DataQualityLog" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality_log)
        WHERE node.id = ${id}
        <set>
            <if test="dataQualityLogType != null">node.dataQualityLogType = '${dataQualityLogType}',</if>
            <if test="processInstanceId != null">node.processInstanceId = '${processInstanceId}',</if>
            <if test="taskInstanceId != null">node.taskInstanceId = '${taskInstanceId}',</if>
            <if test="callTime != null and callTime != ''">node.callTime = '${callTime}',</if>
            <if test="errorLog != null and errorLog != ''">node.errorLog = '${errorLog}',</if>
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds"  resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_q_data_quality_log)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_q_data_quality_log) WHERE node.id = ${id} RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.quality.api.domain.DataQualityLog"
            resultType="com.datalink.fdop.quality.api.domain.DataQualityLog">
        WITH log as (select *
        from ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:d_q_data_quality_log)
        <where>
            <if test="dataQualityLog.id != null">AND node.id = ${dataQualityLog.id}</if>
            <if test="dataQualityLog.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("log",dataQualityLog.searchCondition)})
            </if>
        </where>
        RETURN
        log.id,log.callTime,log.processInstanceId,log.taskInstanceId,log.storageType,log.storageName,log.inspectionType,log.monitorId,log.errorLog,log.errorStatus
        $$) as (id BIGINT,callTime TEXT,processInstanceId INTEGER, taskInstanceId INTEGER,storageType TEXT,storageName
        TEXT,inspectionType TEXT,monitorId TEXT,errorLog TEXT,errorStatus TEXT))
        select log.id,log.callTime as call_time,log.processInstanceId as
        process_instance_id,log.taskInstanceId as
        task_instance_id, tdpi.name as
        process_instance_name,tdti.name as task_instance_name,tdti.task_code as task_definition_code, tdtd.name as
        task_definition_name, tdti.state as
        data_quality_log_status,
        log.storageType as storage_type,
        log.storageName as storage_name,
        log.inspectionType as inspection_type,
        log.monitorId as monitor_id,
        log.errorLog as error_log,
        log.errorStatus as errorStatus
        from log
        JOIN dolphinscheduler.t_ds_process_instance tdpi on log.processInstanceId = tdpi.id
        JOIN dolphinscheduler.t_ds_task_instance tdti on log.taskInstanceId = tdti.id
        JOIN dolphinscheduler.t_ds_task_definition tdtd on tdti.task_code = tdtd.code
        <where>
            <if test="dataQualityLog.dataQualityLogStatus != null">
                <if test="dataQualityLog.dataQualityLogStatus.code == 1 or dataQualityLog.dataQualityLogStatus.code == 7">
                    AND tdti.state = ${dataQualityLog.dataQualityLogStatus.code}
                </if>
                <if test="dataQualityLog.dataQualityLogStatus.code != 1 and dataQualityLog.dataQualityLogStatus.code != 7">
                    AND tdti.state &lt;&gt; 1 AND tdti.state &lt;&gt; 7
                </if>
            </if>
        </where>
    </select>

    <select id="selectStorageNameByIds" resultType="com.datalink.fdop.quality.api.domain.DataQualityLog">
        select * from ag_catalog.cypher('zjdata_graph', $$
        MATCH (log:d_q_data_quality_log)-[edge:d_q_data_quality_log_entity_edge]->(entity:d_e_data_entity)
        WHERE log.id IN
        <foreach collection="dataQualityLogIds" item="dataQualityLogId" open="[" separator="," close="]">
            ${dataQualityLogId}
        </foreach>
        RETURN log.id,entity.code as storageName
        $$) as (id BIGINT,storageName TEXT)
    </select>

</mapper> 