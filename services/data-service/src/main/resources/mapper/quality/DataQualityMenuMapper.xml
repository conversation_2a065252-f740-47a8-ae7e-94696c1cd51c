<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.quality.mapper.DataQualityMenuMapper">

    <select id="createQualityMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_q_data_quality_menu), (menuE:d_q_data_quality_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'d_q_data_quality_menu',endTable:'d_q_data_quality_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'d_q_data_quality_menu',endTable:'d_q_data_quality_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertQualityMenu" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:d_q_data_quality_menu ${@com.datalink.fdop.quality.utils.DomainAgeUtils@getQualityMenuAgeStr(dataQualityMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.quality.api.domain.DataQualityMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu)
        WHERE menu.id = ${id}
        <set>
            <if test="pid != null">menu.pid = ${pid},</if>
            <if test="code != null">menu.code = '${code}',</if>
            <if test="name != null">menu.name = '${name}',</if>
            <if test="description != null">menu.description = '${description}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" parameterType="com.datalink.fdop.quality.api.domain.DataQualityMenu"
            resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteQualityMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[mme1:menu_menu_edge]->(menu)-[mme2:menu_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menu.id = ${pid}
        DELETE mme1, mme2 RETURN id(mme1),
        properties(mme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <sql id="qualityMenu">
        RETURN ${alias}.id, ${alias}.pid,
            ${alias}.code,
            ${alias}.name,
            ${alias}.description,
            ${alias}.createTime,
            ${alias}.updateTime
            $$) as (id BIGINT, pid BIGINT,
            code TEXT,
            name TEXT,
            description TEXT,
            createTime TEXT,
            updateTime TEXT)
    </sql>

    <select id="selectById" resultType="com.datalink.fdop.quality.api.domain.DataQualityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu)
        WHERE menu.id = ${id}
        <include refid="qualityMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectByCode" resultType="com.datalink.fdop.quality.api.domain.DataQualityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu) WHERE menu.code = '${code}'
        <include refid="qualityMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectByPid" resultType="com.datalink.fdop.quality.api.domain.DataQualityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu) WHERE menu.pid = ${pid}
        <include refid="qualityMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="checkCodeIsExists" resultType="com.datalink.fdop.quality.api.domain.DataQualityMenu">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id}
        <include refid="qualityMenu">
            <property name="alias" value="menu"/>
        </include>
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_q_data_quality_menu) -[mme:menu_menu_edge]->(menuE:d_q_data_quality_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="selectMenuTree" resultType="com.datalink.fdop.quality.api.domain.DataQualityTree">
        SELECT * FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_q_data_quality_menu)
        <where>
            <if test="code != null and code != ''">menu.code =~'.*${code}.*'</if>
        </where>
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType
        ORDER BY menu.code ${sort}
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuType TEXT)
    </select>

</mapper> 