<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.ClassifyLevelConfigMapper">

    <insert id="insert" parameterType="com.datalink.fdop.govern.api.domain.ClassifyLevelConfig">
        INSERT INTO zjdata.classify_level_config (
            id, group_name, data_source, schema_name, table_name, table_comment,
            data_volume, field_name, field_business_type, category, level,
            is_sensitive, column_comment, technical_type, is_primary_key,
            column_length, sample_data, create_by, create_time
        ) VALUES (
                     #{id}, #{groupName}, #{dataSource}, #{schemaName}, #{tableName}, #{tableComment},
                     #{dataVolume}, #{fieldName}, #{fieldBusinessType}, #{category}, #{level},
                     #{isSensitive}, #{columnComment}, #{technicalType}, #{isPrimaryKey},
                     #{columnLength}, #{sampleData}, #{createBy}, #{createTime}
                 )
    </insert>

    <update id="updateById" parameterType="com.datalink.fdop.govern.api.domain.ClassifyLevelConfig">
        UPDATE zjdata.classify_level_config
        <set>
            <if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
            <if test="dataSource != null and dataSource != ''">data_source = #{dataSource},</if>
            <if test="schemaName != null and schemaName != ''">schema_name = #{schemaName},</if>
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="tableComment != null and tableComment != ''">table_comment = #{tableComment},</if>
            <if test="dataVolume != null">data_volume = #{dataVolume},</if>
            <if test="fieldName != null and fieldName != ''">field_name = #{fieldName},</if>
            <if test="fieldBusinessType != null and fieldBusinessType != ''">field_business_type = #{fieldBusinessType},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="level != null and level != ''">level = #{level},</if>
            <if test="isSensitive != null">is_sensitive = #{isSensitive},</if>
            <if test="columnComment != null and columnComment != ''">column_comment = #{columnComment},</if>
            <if test="technicalType != null and technicalType != ''">technical_type = #{technicalType},</if>
            <if test="isPrimaryKey != null">is_primary_key = #{isPrimaryKey},</if>
            <if test="columnLength != null">column_length = #{columnLength},</if>
            <if test="sampleData != null and sampleData != ''">sample_data = #{sampleData},</if>
            update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteBatchIds" parameterType="java.util.List">
        DELETE FROM zjdata.classify_level_config
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="overview"
            resultType="com.datalink.fdop.govern.api.domain.ClassifyLevelConfig"
            parameterType="map">
        SELECT *
        FROM zjdata.classify_level_config
        <where>
            <if test="fieldName != null and fieldName != ''">
                AND field_name LIKE CONCAT('%', #{fieldName}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category  LIKE CONCAT('%', #{category}, '%')
            </if>
            <if test="level != null and level != ''">
                AND level  LIKE CONCAT('%', #{level}, '%')
            </if>
        </where>
            ORDER BY create_time ${sort}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zjdata.classify_level_config (
        id, group_name, data_source, schema_name, table_name, table_comment,
        data_volume, field_name, field_business_type, category, level,
        is_sensitive, column_comment, technical_type, is_primary_key,
        column_length, sample_data, create_by, create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.groupName},#{item.dataSource},#{item.schemaName},#{item.tableName},
            #{item.tableComment},#{item.dataVolume},#{item.fieldName},#{item.fieldBusinessType},#{item.category},
            #{item.level},#{item.isSensitive},#{item.columnComment},#{item.technicalType},#{item.isPrimaryKey},
            #{item.columnLength},#{item.sampleData},#{item.createBy},#{item.createTime}
            )
        </foreach>
    </insert>



</mapper>

