<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.datalink.fdop.govern.mapper.DataSecurityClassificationMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataSecurityClassification"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" parameterType="com.datalink.fdop.govern.api.domain.DataSecurityClassification" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (classification:d_g_data_security_classification ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getDataSecurityClassificationAgeStr(classification)}) RETURN id(classification),
                               properties(classification)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.DataSecurityClassification" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (classification:d_g_data_security_classification)
        WHERE classification.id = ${id}
        <set>
            <if test="code != null and code != ''">classification.code = '${code}',</if>
            <if test="name != null and name != ''">classification.name = '${name}',</if>
            <if test="pid != null and pid != ''">classification.pid = ${pid},</if>
            <if test="pname != null and pname != ''">classification.pname = '${pname}',</if>
            <if test="description != null and description != ''">classification.description = '${description}',</if>
            <if test="serialNumber != null">classification.serialNumber = ${serialNumber},</if>
            classification.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            classification.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(classification), properties(classification)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (classification:d_g_data_security_classification) WHERE classification.id = ${id} RETURN id(classification), properties(classification)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (classification:d_g_data_security_classification) WHERE classification.code = '${code}' RETURN id(classification), properties(classification)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (classification:d_g_data_security_classification) WHERE classification.code = '${code}' AND classification.id &lt;&gt; ${id} RETURN id(classification),
                               properties(classification)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (classification:d_g_data_security_classification)
        WHERE classification.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE classification RETURN id(classification),properties(classification) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.DataSecurityClassification" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (classification:d_g_data_security_classification)
        <where>
            <if test="classification.id != null">AND classification.id = ${classification.id}</if>
            <if test="classification.code != null and classification.code != ''">AND classification.code =~'.*${classification.code}.*'
            </if>
            <if test="classification.name != null and classification.name != ''">AND classification.name =~'.*${classification.name}.*'
            </if>
            <if test="classification.description != null and classification.description != ''">AND classification.description
                =~'.*${classification.description}.*'
            </if>
            <if test="classification.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("classification",classification.searchCondition)})
            </if>
        </where>
        WITH classification
        ORDER BY classification.createTime DESC
        RETURN id(classification), properties(classification)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectListAll" parameterType="com.datalink.fdop.govern.api.domain.DataSecurityClassification" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (classification:d_g_data_security_classification)
        <where>
            <if test="classification.id != null">AND classification.id = ${classification.id}</if>
            <if test="classification.pid != null">AND classification.pid = ${classification.pid}</if>
            <if test="classification.pname != null and classification.pname != ''">AND classification.pname =~'.*${classification.pname}.*'
            </if>
            <if test="classification.code != null and classification.code != ''">AND classification.code =~'.*${classification.code}.*'
            </if>
            <if test="classification.name != null and classification.name != ''">AND classification.name =~'.*${classification.name}.*'
            </if>
            <if test="classification.description != null and classification.description != ''">AND classification.description
                =~'.*${classification.description}.*'
            </if>
            <if test="classification.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("classification",classification.searchCondition)})
            </if>
        </where>
        WITH classification
        ORDER BY classification.serialNumber ASC
        RETURN  classification.id as key,
        classification.code as value
        $$) as (key TEXT,value TEXT)
    </select>

    <select id="selectDataSecurityClassificationTree" resultType="com.datalink.fdop.govern.api.domain.DataSecurityClassificationTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (classification:d_g_data_security_classification)
        <where>
            <if test="code != null and code != ''">AND classification.code =~'.*${code}.*'</if>
            <if test="description != null and description != ''">AND classification.description =~'.*${description}.*'</if>
        </where>
        RETURN classification.id, classification.pid,classification.pname, classification.code, classification.name, classification.description, 'MENU' as menuType,classification.createTime
        ,classification.serialNumber
        ORDER BY classification.serialNumber ${sort}
        $$) as (id ag_catalog.agtype,pid ag_catalog.agtype,pname TEXT, code TEXT,name TEXT,description TEXT,menuType
        TEXT,createTime TEXT,serialNumber INTEGER)
    </select>

</mapper>