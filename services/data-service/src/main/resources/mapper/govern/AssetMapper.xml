<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.AssetMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.MetadataMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>



    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <select id="search" resultType="com.datalink.fdop.govern.api.model.vo.AssetVo">
        <foreach collection="assetTypes" item="assetType" open="" close="" index="index" >
            <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@ENTITY or assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL ">
                <if test="index!=0"  >
                    union
                </if>
                SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
                MATCH (v:d_e_data_entity)
                <if test="searchValue !=null and searchValue !=''">
                    where v.code=~'.*${searchValue}.*' or v.name=~'.*${searchValue}.*'   or v.description=~'.*${searchValue}.*'
                </if>
                RETURN 'ENTITY' as assetType,v.code,v.name,v.description
                $$) as (assetType TEXT,code TEXT,name TEXT,description TEXT)
            </if>
            <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@ELEMENT or assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL ">
                <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL or index!=0"  >
                    union
                </if>
                SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
                MATCH (v:d_e_data_element)
                <if test="searchValue !=null and searchValue !=''">
                    where v.code=~'.*${searchValue}.*' or v.name=~'.*${searchValue}.*'   or v.description=~'.*${searchValue}.*'
                </if>
                RETURN 'ELEMENT' as assetType,v.code,v.name,v.description
                $$) as (assetType TEXT,code TEXT,name TEXT,description TEXT)
            </if>
            <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@TABLE or assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL ">
                <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL or index!=0"  >
                    union
                </if>
                SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
                MATCH ()-[r:d_g_metadata_menu_edge]->(v:d_g_synchronization_table)
                <if test="searchValue !=null and searchValue !=''">
                    where r.code=~'.*${searchValue}.*' or v.name=~'.*${searchValue}.*'   or v.description=~'.*${searchValue}.*'
                </if>
                RETURN 'TABLE' as assetType,r.code,v.name,v.description
                $$) as (assetType TEXT,code TEXT,name TEXT,description TEXT)
            </if>
            <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@VIEW or assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL ">
                <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL or index!=0"  >
                    union
                </if>
                SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
                MATCH ()-[r:d_g_metadata_menu_edge]->(v:d_g_synchronization_table)
                <if test="searchValue !=null and searchValue !=''">
                    where r.code=~'.*${searchValue}.*' or v.name=~'.*${searchValue}.*'   or v.description=~'.*${searchValue}.*'
                </if>
                RETURN 'VIEW' as assetType,r.code,v.name,v.description
                $$) as (assetType TEXT,code TEXT,name TEXT,description TEXT)
            </if>
            <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@SCHEMA or assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL ">
                <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL or index!=0"  >
                    union
                </if>
                SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
                MATCH ()-[r:d_g_metadata_menu_edge]->(v:d_g_synchronization_schema)
                <if test="searchValue !=null and searchValue !=''">
                    where r.code=~'.*${searchValue}.*' or v.name=~'.*${searchValue}.*'   or v.description=~'.*${searchValue}.*'
                </if>
                RETURN 'SCHEMA' as assetType,r.code,v.name,v.description
                $$) as (assetType TEXT,code TEXT,name TEXT,description TEXT)
            </if>
            <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@DATABASE or assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL ">
                <if test="assetType==@com.datalink.fdop.govern.api.enums.AssetType@ALL or index!=0"  >
                    union
                </if>
                SELECT * FROM ag_catalog.cypher( 'zjdata_graph',$$
                MATCH ()-[r:d_g_metadata_menu_edge]->(v:d_g_synchronization_database)
                <if test="searchValue !=null and searchValue !=''">
                    where v.code=~'.*${searchValue}.*' or v.name=~'.*${searchValue}.*'   or v.description=~'.*${searchValue}.*'
                </if>
                RETURN 'DATABASE' as assetType,r.code,v.name,v.description
                $$) as (assetType TEXT,code TEXT,name TEXT,description TEXT)
            </if>
        </foreach>
    </select>

</mapper>