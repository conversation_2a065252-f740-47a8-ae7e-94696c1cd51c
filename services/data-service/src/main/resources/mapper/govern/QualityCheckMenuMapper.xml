<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.QualityCheckMenuMapper">
    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.QualityCheckMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>


    <select id="createSeaTunnelCmdMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:s_c_createqualitycheck_menu), (menuE:s_c_createqualitycheck_menu)
        WHERE menuS.id = ${pid}
        AND menuE.pid = ${pid} AND menuE.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'s_c_createqualitycheck_menu',endTable:'s_c_createqualitycheck_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'s_c_createqualitycheck_menu',endTable:'s_c_createqualitycheck_menu'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>



    <select id="createMenuAndPlanEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:s_c_createqualitycheck_plan), (menuE:s_c_createqualitycheck_menu)
        WHERE menuE.id = ${pid}
        AND menuS.pid = ${pid} AND menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menuS)-[mme:menu_menu_edge
        {startTable:'s_c_createqualitycheck_plan',endTable:'s_c_createqualitycheck_menu'}]->(menuE)-[:menu_menu_edge
        {startTable:'s_c_createqualitycheck_menu',endTable:'s_c_createqualitycheck_plan'}]->(menuS)
        RETURN id(mme), properties(mme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertSeaTunnelCmdMenu" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:s_c_createqualitycheck_menu ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getSeaTunnelCmdMenuAgeStr(seaTunnelCmdMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectMenuTree" resultType="com.datalink.fdop.govern.api.domain.QualityCheckMenuTree">
        SELECT * FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_createqualitycheck_menu)
        <where>
            <if test="code != null and code != ''">menu.code =~'.*${code}.*'</if>
        </where>
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, menu.menuType,menu.taskFlowId,menu.taskFlowCode
        ORDER BY menu.code ${sort}
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuType TEXT,taskFlowId TEXT,taskFlowCode TEXT)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:s_c_createqualitycheck_menu) WHERE menu.code = '${code}' RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_createqualitycheck_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:s_c_createqualitycheck_menu) -[mme:menu_menu_edge]->(menuE:s_c_createqualitycheck_menu)
            WHERE menuE.pid = ${pid}
            RETURN DISTINCT (menuE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" parameterType="com.datalink.fdop.govern.api.domain.QualityCheckMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_createqualitycheck_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET menu.pid = ${pid}
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:s_c_createqualitycheck_menu) WHERE menu.id = ${id} RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_createqualitycheck_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="overview" resultType="com.datalink.fdop.govern.api.domain.QualityCheckMenu">
        WITH overview as (
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_createqualitycheck_plan) -[:menu_menu_edge]->(menu:s_c_createqualitycheck_menu)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND  node.menuType='Plan'
            </if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.createTime,node.updateTime,node.serialNumber,node.type,node.jobId,node.status
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime TEXT,updateTime
        TEXT,serialNumber BIGINT,type TEXT,jobId TEXT,status TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>




    <select id="overviewNode" resultType="com.datalink.fdop.govern.api.domain.QualityCheck">
        WITH overview as (
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_createqualitycheck) -[:node_menu_edge]->(menu:s_c_createqualitycheck_plan)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as
        menuName,node.createTime,node.updateTime,node.serialNumber,node.type,node.jobId,node.status,node.taskCode,node.checkType,node.checkTargetId,
        node.checkTargetProperty,node.inspectionMethod,node.checkDimensions,node.entityId
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime TEXT,updateTime
        TEXT,serialNumber BIGINT,type TEXT,jobId TEXT,status TEXT,taskCode TEXT,checkType TEXT,checkTargetId TEXT,checkTargetProperty TEXT,inspectionMethod TEXT,checkDimensions TEXT,entityId TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,code ${sort}
    </select>
</mapper>
