<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.DirectoryStructureMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DirectoryStructure"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_directory_structure ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getDirectoryStructureAgeStr(entity)})
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_directory_structure)
                                   WHERE node.code = '${code}'
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="selectById" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_directory_structure)
                                   WHERE node.id = ${id}
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="codeIsOnly" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_directory_structure)
                                   WHERE node.code = '${code}' and node.id &lt;&gt; ${id}
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.DirectoryStructure" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_directory_structure)
        WHERE node.id = ${id}
        <set>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="level != null">node.dataLevel = '${level}',</if>
            <if test="serialNumber != null">node.serialNumber = ${serialNumber},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    
    <select id="queryExistChildrenNodes" resultType="java.lang.String">
        select *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_directory_structure)
        where node.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN node.code
        $$) as (code text)
        UNION
        select *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_business_object)
        where node.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN node.code
        $$) as (code text)
    </select>

    <select id="delete" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_directory_structure)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    
    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.DirectoryStructure" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_directory_structure)
        <where>
            <if test="entity.id != null">AND entity.id = ${entity.id}</if>
            <if test="entity.pid != null">AND entity.pid = ${entity.pid}</if>
            <if test="entity.code != null and entity.code != ''">AND entity.code =~'.*${entity.code}.*'</if>
            <if test="entity.name != null and entity.name != ''">AND entity.name =~'.*${entity.name}.*'</if>
            <if test="entity.level != null">AND entity.level = '${entity.level}'</if>
        </where>
        WITH entity
        ORDER BY entity.createTime ${sort}
        RETURN id(entity), properties(entity)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="getAllNode" resultType="com.datalink.fdop.govern.api.domain.DirectoryStructureTree">
        select * from (
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_process_framework)
            RETURN node.id, node.pid, node.code, node.name, node.description, node.level as level
                                   $$) as (id text, pid text, code text, name text, description text, level text)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_business_object)
            RETURN node.id, node.pid, node.code, node.name, node.description, 'L3' as level
                                   $$) as (id text, pid text, code text, name text, description text, level text)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_logical_object)
            WHERE node.activeState = true
            RETURN node.id, node.pid, node.code, node.name, node.description, 'L4' as level
                                   $$) as (id text, pid text, code text, name text, description text, level text)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_object_properties)
            WHERE node.activeState = true
            RETURN node.id, node.pid, node.code, node.name, node.description, 'L5' as level
                                   $$) as (id text, pid text, code text, name text, description text, level text)
                      ) a order by code ${sort}
    </select>

    <select id="getApplicationAllNode" resultType="com.datalink.fdop.govern.api.domain.DirectoryStructureTree">
        select * from (
                          SELECT *
                          FROM ag_catalog.cypher('zjdata_graph', $$
                              MATCH (node:d_g_data_application_framework)
                              RETURN node.id, node.pid, node.code, node.name, node.description, node.level as level, node.pid as applicationId
                                   $$) as (id text, pid text, code text, name text, description text, level text, applicationId TEXT)
                          union
                          SELECT *
                          FROM ag_catalog.cypher('zjdata_graph', $$
                              MATCH (node:d_g_business_object)
                              RETURN node.id, node.pid, node.code, node.name, node.description, 'L3' as level, node.sourceSystemId as applicationId
                              $$) as (id text, pid text, code text, name text, description text, level text, applicationId text)
                          union
                          SELECT *
                          FROM ag_catalog.cypher('zjdata_graph', $$
                              MATCH (node:d_g_logical_object)
                              WHERE node.activeState = true
                              RETURN node.id, node.pid, node.code, node.name, node.description, 'L4' as level, node.pid as applicationId
                              $$) as (id text, pid text, code text, name text, description text, level text, applicationId text)
                          union
                          SELECT *
                          FROM ag_catalog.cypher('zjdata_graph', $$
                              MATCH (node:d_g_object_properties)
                              WHERE node.activeState = true
                              RETURN node.id, node.pid, node.code, node.name, node.description, 'L5' as level, node.pid as applicationId
                              $$) as (id text, pid text, code text, name text, description text, level text, applicationId text)
                      ) a order by code ${sort}
    </select>

</mapper>