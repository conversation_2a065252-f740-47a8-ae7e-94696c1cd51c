<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.LogicalObjectMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.LogicalObject"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_logical_object ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getLogicalObjectAgeStr(entity)})
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_logical_object)
                                   WHERE node.code = '${code}'
                                   and node.activeState = true
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="selectById" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_logical_object)
                                   WHERE node.id = ${id}
                                   and node.activeState = true
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="codeIsOnly" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_logical_object)
                                   WHERE node.code = '${code}' and node.id &lt;&gt; ${id}
                                   and node.activeState = true
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.LogicalObject" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_logical_object)
        WHERE node.id = ${id}
        <set>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="description != null and description != ''">node.description = '${description}',</if>
            <if test="lockStatus != null">node.lockStatus = ${lockStatus},</if>
            <if test="activeState != null">node.activeState = ${activeState},</if>
            <if test="serialNumber != null">node.serialNumber = ${serialNumber},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="queryExistChildrenNodes" resultType="java.lang.String">
        select *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_object_properties)
        where node.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        and node.activeState = true
        RETURN node.code
        $$) as (code text)
    </select>

    <select id="queryExistUnLockChildrenNodes" resultType="java.lang.String">
        select *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_object_properties)
        where node.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        and node.lockStatus = false and node.activeState = true
        RETURN node.code
        $$) as (code text)
    </select>

    <select id="delete" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_logical_object)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.LogicalObject" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_logical_object)
        <where>
            <if test="entity.id != null">AND entity.id = ${entity.id}</if>
            <if test="entity.pid != null">AND entity.pid = ${entity.pid}</if>
            <if test="entity.code != null and entity.code != ''">AND entity.code =~'.*${entity.code}.*'</if>
            <if test="entity.name != null and entity.name != ''">AND entity.name =~'.*${entity.name}.*'</if>
            <if test="entity.version != null and entity.version != ''">AND entity.version =~'.*${entity.version}.*'</if>
            <if test="entity.lockStatus != null">AND entity.lockStatus = ${lockStatus}</if>
            AND entity.activeState = true
        </where>
        WITH entity
        ORDER BY entity.createTime ${sort}
        RETURN id(entity), properties(entity)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="getExportList" parameterType="com.datalink.fdop.govern.api.domain.LogicalObject" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_logical_object)
        <where>
            <if test="entity.id != null">AND entity.id = ${entity.id}</if>
            <if test="entity.pid != null">AND entity.pid = ${entity.pid}</if>
            <if test="entity.code != null and entity.code != ''">AND entity.code =~'.*${entity.code}.*'</if>
            <if test="entity.name != null and entity.name != ''">AND entity.name =~'.*${entity.name}.*'</if>
            <if test="entity.version != null and entity.version != ''">AND entity.version =~'.*${entity.version}.*'</if>
            <if test="entity.lockStatus != null">AND entity.lockStatus = ${lockStatus}</if>
            AND entity.activeState = true
        </where>
        WITH entity
        ORDER BY entity.createTime ${sort}
        RETURN id(entity), properties(entity)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateActiveState" resultType="java.lang.Integer">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_g_logical_object)
            WHERE entity.id = ${id}
            SET entity.activeState = false
            RETURN id(entity), properties(entity)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateActiveStateByIds" resultType="java.lang.Integer">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (entity:d_g_logical_object)
            WHERE entity.id in
            <foreach collection="ids" item="id" open="[" separator="," close="]">
                ${id}
            </foreach>
            SET entity.activeState = false
            RETURN id(entity), properties(entity)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByIds" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_logical_object)
                                   WHERE node.id in
                                <foreach collection="ids" item="id" open="[" separator="," close="]">
                                    ${id}
                                </foreach>
                                   and node.activeState = true
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByPid" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
        $$ MATCH (node:d_g_logical_object)
        WHERE node.pid = ${pid}
        and node.activeState = true
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="lock" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_logical_object)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.lockStatus = true
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="unlock" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_logical_object)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.lockStatus = false
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper>