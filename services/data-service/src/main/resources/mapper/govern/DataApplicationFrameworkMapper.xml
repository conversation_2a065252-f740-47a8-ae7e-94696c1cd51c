<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.datalink.fdop.govern.mapper.DataApplicationFrameworkMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataApplicationFramework"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" parameterType="com.datalink.fdop.govern.api.domain.DataApplicationFramework" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (application:d_g_data_application_framework ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getDataApplicationFrameworkAgeStr(application)}) RETURN id(application),
                               properties(application)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.DataApplicationFramework" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (application:d_g_data_application_framework)
        WHERE application.id = ${id}
        <set>
            <if test="code != null and code != ''">application.code = '${code}',</if>
            <if test="name != null and name != ''">application.name = '${name}',</if>
            <if test="pid != null and pid != ''">application.pid = ${pid},</if>
            <if test="pname != null and pname != ''">application.pname = '${pname}',</if>
            <if test="level != null and level != ''">application.level = '${level}',</if>
            <if test="description != null and description != ''">application.description = '${description}',</if>
            <if test="appType != null">application.appType = '${appType}',</if>
            <if test="appTypeLabel != null and appTypeLabel != ''">application.appTypeLabel = '${appTypeLabel}',</if>
            <if test="sourceId != null and sourceId != ''">application.sourceId = ${sourceId},</if>
            <if test="sourceName != null and sourceName != ''">application.sourceName = '${sourceName}',</if>
            <if test="shortName != null and shortName != ''">application.shortName = '${shortName}',</if>
            <if test="shortNameEn != null and shortNameEn != ''">application.shortNameEn = '${shortNameEn}',</if>
            <if test="fullName != null and fullName != ''">application.fullName = '${fullName}',</if>
            <if test="fullNameEn != null and fullNameEn != ''">application.fullNameEn = '${fullNameEn}',</if>
            <if test="itManager != null and itManager != ''">application.itManager = '${itManager}',</if>
            <if test="productLevel != null and productLevel != ''">application.productLevel = '${productLevel}',</if>
            <if test="productManager != null and productManager != ''">application.productManager = '${productManager}',</if>
            <if test="opsManager != null and opsManager != ''">application.opsManager = '${opsManager}',</if>
            <if test="techManager != null and techManager != ''">application.techManager = '${techManager}',</if>
            <if test="serialNumber != null">application.serialNumber = ${serialNumber},</if>
            application.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            application.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(application), properties(application)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (application:d_g_data_application_framework) WHERE application.id = ${id} RETURN id(application), properties(application)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (application:d_g_data_application_framework) WHERE application.code = '${code}' RETURN id(application), properties(application)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (application:d_g_data_application_framework) WHERE application.code = '${code}' AND application.id &lt;&gt; ${id} RETURN id(application),
                               properties(application)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (application:d_g_data_application_framework)
        WHERE application.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE application RETURN id(application),properties(application) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.DataApplicationFramework" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (application:d_g_data_application_framework)
        <where>
            <if test="application.id != null">AND application.id = ${application.id}</if>
            <if test="application.code != null and application.code != ''">AND application.code =~'.*${application.code}.*'
            </if>
            <if test="application.name != null and application.name != ''">AND application.name =~'.*${application.name}.*'
            </if>
            <if test="application.description != null and application.description != ''">AND application.description
                =~'.*${application.description}.*'
            </if>
            <if test="application.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("application",application.searchCondition)})
            </if>
        </where>
        WITH application
        ORDER BY application.createTime DESC
        RETURN id(application), properties(application)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectListAll" parameterType="com.datalink.fdop.govern.api.domain.DataApplicationFramework" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (application:d_g_data_application_framework)
        <where>
            <if test="application.id != null">AND application.id = ${application.id}</if>
            <if test="application.pid != null">AND application.pid = ${application.pid}</if>
            <if test="application.level != null and application.level != ''">AND application.level = '${application.level}'</if>
            <if test="application.code != null and application.code != ''">AND application.code =~'.*${application.code}.*'
            </if>
            <if test="application.name != null and application.name != ''">AND application.name =~'.*${application.name}.*'
            </if>
            <if test="application.description != null and application.description != ''">AND application.description
                =~'.*${application.description}.*'
            </if>
            <if test="application.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("application",application.searchCondition)})
            </if>
        </where>
        WITH application
        ORDER BY application.serialNumber ASC
        RETURN  application.id as key,
        application.code as value
        $$) as (key TEXT,value TEXT)
    </select>

    <select id="selectTree" resultType="com.datalink.fdop.govern.api.domain.DataApplicationFrameworkTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (application:d_g_data_application_framework)
        <where>
            <if test="code != null and code != ''">application.code =~'.*${code}.*'</if>
            <if test="name != null and name != ''">AND application.name =~'.*${name}.*'</if>
            <if test="description != null and description != ''">AND application.description =~'.*${description}.*'</if>
        </where>
        RETURN application.id, application.pid, application.pname, application.code, application.name, application.level, application.description, 'MENU' as menuType
        ORDER BY application.code ${sort}
        $$) as (id BIGINT,pid BIGINT,pname TEXT,code TEXT,name TEXT,level TEXT,description TEXT,menuType TEXT)
    </select>

    <select id="overview" resultType="com.datalink.fdop.govern.api.domain.DataApplicationFramework">
        WITH overview as (
            SELECT application.*, datasource.name as sourceName
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_application_framework)
            <where>
                <if test="pid != null and pid != -1">AND node.pid = ${pid}</if>
                <if test="searchVo != null ">
                    AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
                </if>
            </where>
            RETURN node.id, node.pid, node.code, node.name, node.description, node.appType, node.appTypeLabel, node.shortName, node.shortNameEn, node.fullName, node.fullNameEn, node.itManager, node.productLevel, node.productManager, node.opsManager, node.techManager, node.pname as
            menuName,node.createTime,node.updateTime,node.serialNumber,node.level,node.sourceId
            $$) as application (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, appType TEXT, appTypeLabel TEXT, shortName TEXT, shortNameEn TEXT, fullName TEXT, fullNameEn TEXT, itManager TEXT, productLevel TEXT, productManager TEXT, opsManager TEXT, techManager TEXT, menuName TEXT,createTime
            TEXT,updateTime TEXT,serialNumber BIGINT,level TEXT,sourceId BIGINT)
            left join ag_catalog.cypher('zjdata_graph', $$ MATCH (datasource:datasource) RETURN datasource.id,datasource.code,datasource.name $$) as datasource (id BIGINT,code TEXT,name TEXT) on application.sourceId = datasource.id
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,serialNumber ${sort}
    </select>

    <select id="querySerialNumber" resultType="int">
        SELECT (count(1) + 1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (application:d_g_data_application_framework) RETURN id(application), properties(application) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

</mapper>