<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.QualityCheckPlanMapper">
    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.QualityCheckPlan"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>


    <select id="insertQualityCheckPlan" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:s_c_createqualitycheck_plan ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getPlanStr(qualityCheckPlan)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:s_c_createqualitycheck_plan) WHERE menu.code = '${code}' RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectMenuTree" resultType="com.datalink.fdop.govern.api.domain.QualityCheckMenuTree">
        SELECT * FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_createqualitycheck_plan)
        <where>
            <if test="code != null and code != ''">menu.code =~'.*${code}.*'</if>
        </where>
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, menu.menuType,menu.taskFlowId,menu.taskFlowCode
        ORDER BY menu.code ${sort}
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuType TEXT,taskFlowId TEXT,taskFlowCode TEXT)
    </select>


    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:s_c_createqualitycheck) -[mme:node_menu_edge]->(menuE:s_c_createqualitycheck_plan)
            WHERE menuS.pid = ${pid}
            RETURN DISTINCT (menuS.id)
            $$) as (id ag_catalog.agtype)
    </select>






    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:s_c_createqualitycheck_plan) WHERE menu.id = ${id} RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>



    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_createqualitycheck_plan)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>



</mapper>
