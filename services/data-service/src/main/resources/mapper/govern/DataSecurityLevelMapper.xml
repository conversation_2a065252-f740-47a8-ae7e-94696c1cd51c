<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.datalink.fdop.govern.mapper.DataSecurityLevelMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataSecurityLevel"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" parameterType="com.datalink.fdop.govern.api.domain.DataSecurityLevel" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (dataSecurityLevel:d_g_data_security_level ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getDataSecurityLevelAgeStr(dataSecurityLevel)}) RETURN id(dataSecurityLevel),
                               properties(dataSecurityLevel)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.DataSecurityLevel" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (dataSecurityLevel:d_g_data_security_level)
        WHERE dataSecurityLevel.id = ${id}
        <set>
            <if test="code != null and code != ''">dataSecurityLevel.code = '${code}',</if>
            <if test="name != null and name != ''">dataSecurityLevel.name = '${name}',</if>
            <if test="dataLevel != null and dataLevel != ''">dataSecurityLevel.dataLevel = '${dataLevel}',</if>
            <if test="affectedObject != null and affectedObject != ''">dataSecurityLevel.affectedObject = '${affectedObject}',</if>
            <if test="description != null and description != ''">dataSecurityLevel.description = '${description}',</if>
            <if test="safetyRequirement != null and safetyRequirement != ''">dataSecurityLevel.safetyRequirement = '${safetyRequirement}',</if>
            <if test="serialNumber != null">dataSecurityLevel.serialNumber = ${serialNumber},</if>
            dataSecurityLevel.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            dataSecurityLevel.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(dataSecurityLevel), properties(dataSecurityLevel)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (dataSecurityLevel:d_g_data_security_level) WHERE dataSecurityLevel.id = ${id} RETURN id(dataSecurityLevel), properties(dataSecurityLevel)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (dataSecurityLevel:d_g_data_security_level) WHERE dataSecurityLevel.code = '${code}' RETURN id(dataSecurityLevel), properties(dataSecurityLevel)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (dataSecurityLevel:d_g_data_security_level) WHERE dataSecurityLevel.code = '${code}' AND dataSecurityLevel.id &lt;&gt; ${id} RETURN id(dataSecurityLevel),
                               properties(dataSecurityLevel)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (dataSecurityLevel:d_g_data_security_level)
        WHERE dataSecurityLevel.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE dataSecurityLevel RETURN id(dataSecurityLevel),properties(dataSecurityLevel) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.DataSecurityLevel" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (dataSecurityLevel:d_g_data_security_level)
        <where>
            <if test="dataSecurityLevel.id != null">AND dataSecurityLevel.id = ${dataSecurityLevel.id}</if>
            <if test="dataSecurityLevel.code != null and dataSecurityLevel.code != ''">AND dataSecurityLevel.code =~'.*${dataSecurityLevel.code}.*'
            </if>
            <if test="dataSecurityLevel.name != null and dataSecurityLevel.name != ''">AND dataSecurityLevel.name =~'.*${dataSecurityLevel.name}.*'
            </if>
            <if test="dataSecurityLevel.description != null and dataSecurityLevel.description != ''">AND dataSecurityLevel.description
                =~'.*${dataSecurityLevel.description}.*'
            </if>
            <if test="dataSecurityLevel.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("dataSecurityLevel",dataSecurityLevel.searchCondition)})
            </if>
        </where>
        WITH dataSecurityLevel
        ORDER BY dataSecurityLevel.serialNumber ASC
        RETURN id(dataSecurityLevel), properties(dataSecurityLevel)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectListAll" parameterType="com.datalink.fdop.govern.api.domain.DataSecurityLevel" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (dataSecurityLevel:d_g_data_security_level)
        <where>
            <if test="dataSecurityLevel.id != null">AND dataSecurityLevel.id = ${dataSecurityLevel.id}</if>
            <if test="dataSecurityLevel.code != null and dataSecurityLevel.code != ''">AND dataSecurityLevel.code =~'.*${dataSecurityLevel.code}.*'
            </if>
            <if test="dataSecurityLevel.name != null and dataSecurityLevel.name != ''">AND dataSecurityLevel.name =~'.*${dataSecurityLevel.name}.*'
            </if>
            <if test="dataSecurityLevel.description != null and dataSecurityLevel.description != ''">AND dataSecurityLevel.description
                =~'.*${dataSecurityLevel.description}.*'
            </if>
            <if test="dataSecurityLevel.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("dataSecurityLevel",dataSecurityLevel.searchCondition)})
            </if>
        </where>
        WITH dataSecurityLevel
        ORDER BY dataSecurityLevel.serialNumber ASC
        RETURN  dataSecurityLevel.id as key,
        dataSecurityLevel.code as value
        $$) as (key TEXT,value TEXT)
    </select>

</mapper>