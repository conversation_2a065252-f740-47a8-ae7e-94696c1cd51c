<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.DataStandardMenuMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataStandardMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <select id="insertStandardMenu" parameterType="com.datalink.fdop.govern.api.domain.DataStandardMenu" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:d_g_data_standard_menu ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getStandardMenuAgeStr(dataStandardMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="deleteBatchIds" resultType="int">
    SELECT COUNT(1)
    FROM ag_catalog.cypher('zjdata_graph', $$
    MATCH (menu:d_g_data_standard_menu)
    WHERE menu.id IN
    <foreach collection="ids" item="id" open="[" separator="," close="]">
        ${id}
    </foreach>
    DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
</select>
    <select id="deleteStandardMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[mme1:menu_menu_edge]->(menu)-[mme2:menu_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menu.id = ${pid}
        DELETE mme1, mme2 RETURN id(mme1),
        properties(mme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectById"  resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_g_data_standard_menu) WHERE menu.id = ${id}
        RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode"  resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_g_data_standard_menu) WHERE menu.code = '${code}'
            RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_g_data_standard_menu) -[nme:menu_menu_edge]->(nodeE:d_g_data_standard_menu)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>

    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard_menu)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createStandardMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_standard_menu), (node:d_g_data_standard_menu)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_g_data_standard_menu',endTable:'d_g_data_standard_menu'}]->(node)-[:node_menu_edge
        {startTable:'d_g_data_standard_menu',endTable:'d_g_data_standard_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_g_data_standard_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id} RETURN id(menu),
                               properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.DataStandardMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_standard_menu)
        WHERE menu.id = ${id}
        <set>
            <if test="pid != null">menu.pid = ${pid},</if>
            <if test="code != null and code != ''">menu.code = '${code}',</if>
            <if test="name != null and name != ''">menu.name = '${name}',</if>
            <if test="description != null and description != ''">menu.description = '${description}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectMenuTree" resultType="com.datalink.fdop.govern.api.domain.DataStandardTree">
        SELECT *
        FROM (
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_standard_menu) WHERE menu.pid = -1
        WITH DISTINCT (menu)
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType
        $$) as (id ag_catalog.agtype,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_g_data_standard_menu) -[]->(menuE:d_g_data_standard_menu)
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType
        $$) as (id ag_catalog.agtype, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_g_data_standard_menu) -[]->(nodeE:d_g_data_standard)
        <where>
            <if test="code != null and code != ''">nodeE.code =~'.*${code}.*'</if>
        </where>
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType
        $$) as (id ag_catalog.agtype, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_g_data_standard_menu) -[]->(nodeE:d_g_data_standard_temporary)
        <where>
            <if test="code != null and code != ''">nodeE.code =~'.*${code}.*'</if>
        </where>
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType
        $$) as (id ag_catalog.agtype, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT)
        ) as zjdata_graph_table
        ORDER BY zjdata_graph_table.code ${sort}
    </select>
    <select id="selectByPids" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_standard_menu) -[nme:node_menu_edge]->(node:d_g_data_standard)
        WHERE menu.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN DISTINCT (menu.id)
        $$) as (id ag_catalog.agtype)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_g_data_govern_menu) -[mme:menu_menu_edge]->(menuE:d_g_data_govern_menu)
        WHERE menuE.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN DISTINCT (menuE.id)
        $$) as (id ag_catalog.agtype)
    </select>
</mapper>