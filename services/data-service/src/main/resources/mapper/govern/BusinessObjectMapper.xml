<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.BusinessObjectMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.BusinessObject"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_business_object ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getBusinessObjectAgeStr(entity)})
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_business_object)
                                   WHERE node.code = '${code}'
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="selectById" resultType="com.datalink.fdop.govern.api.domain.BusinessObject">
        SELECT business_object.*,
        data_process_framework1.name as l1, data_process_framework2.name as l2,
        data_application_framework.code as sourceSystemCode, data_application_framework.name as sourceSystemName,
        data_resources.code as dataResourcesCode, data_resources.name as dataResourcesName,
        data_security_classification.code as securitySortCode, data_security_classification.name as securitySortName,
        data_security_level.code as securityLevelCode, data_security_level.name as securityLevelName
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_business_object)
        where entity.id = ${id}
        RETURN
        entity.id,
        entity.pid,
        entity.code,
        entity.name,
        entity.version,
        entity.dataResourcesId,
        entity.isInformation,
        entity.informationLabel,
        entity.sourceSystemId,
        entity.description,
        entity.priority,
        entity.priorityLabel,
        entity.securitySortId,
        entity.securityLevelId,
        entity.businessObjectClassification,
        entity.businessObjectClassificationLabel,
        entity.governStatus,
        entity.governStatusLabel,
        entity.registerStatus,
        entity.registerStatusLabel,
        entity.dataOwnerPost,
        entity.dataOwner,
        entity.dataManagePost,
        entity.dataManage,
        entity.dataStewardPost,
        entity.dataSteward,
        entity.itEngineerPost,
        entity.itEngineer,
        entity.dataEngineerPost,
        entity.dataEngineer,
        entity.dataDevelopPost,
        entity.dataDeveloper,
        entity.serialNumber,
        entity.createBy,
        entity.createTime,
        entity.updateBy,
        entity.updateTime
        $$) as business_object(
        id text,
        pid text,
        code text,
        name text,
        version text,
        dataResourcesId text,
        isInformation text,
        informationLabel text,
        sourceSystemId text,
        description text,
        priority text,
        priorityLabel text,
        securitySortId text,
        securityLevelId text,
        businessObjectClassification text,
        businessObjectClassificationLabel text,
        governStatus text,
        governStatusLabel text,
        registerStatus text,
        registerStatusLabel text,
        dataOwnerPost text,
        dataOwner text,
        dataManagePost text,
        dataManage text,
        dataStewardPost text,
        dataSteward text,
        itEngineerPost text,
        itEngineer text,
        dataEngineerPost text,
        dataEngineer text,
        dataDevelopPost text,
        dataDeveloper text,
        serialNumber text,
        createBy text,
        createTime text,
        updateBy text,
        updateTime text
        )
             left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_process_framework)
        RETURN entity.id, entity.pid, entity.code, entity.name $$)
        as data_process_framework2(id text, pid text, code text, name text) on data_process_framework2.id = business_object.pid
             left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_process_framework)
        RETURN entity.id, entity.code, entity.name $$)
        as data_process_framework1(id text, code text, name text) on data_process_framework2.pid = data_process_framework1.id
             left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_application_framework)
        RETURN entity.id, entity.code, entity.name $$)
        as data_application_framework(id text, code text, name text) on business_object.sourceSystemId = data_application_framework.id
             left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_resources)
        RETURN entity.id, entity.code, entity.name $$)
        as data_resources(id text, code text, name text) on business_object.dataResourcesId = data_resources.id
             left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_classification)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_classification(id text, code text, name text) on business_object.securitySortId = data_security_classification.id
             left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_level)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_level(id text, code text, name text) on business_object.securityLevelId = data_security_level.id
    </select>

    <select id="codeIsOnly" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_business_object)
                                   WHERE node.code = '${code}' and node.id &lt;&gt; ${id}
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype) limit 1
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.BusinessObject" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_business_object)
        WHERE node.id = ${id}
        <set>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="version != null and version != ''">node.version = '${version}',</if>
            <if test="dataResourcesId != null">node.dataResourcesId = ${dataResourcesId},</if>
            <if test="isInformation != null">node.isInformation = '${isInformation}',</if>
            <if test="informationLabel != null and informationLabel != ''">node.informationLabel = '${informationLabel}',</if>
            <if test="sourceSystemId != null">node.sourceSystemId = ${sourceSystemId},</if>
            <if test="description != null and description != ''">node.description = '${description}',</if>
            <if test="priority != null">node.priority = '${priority}',</if>
            <if test="priorityLabel != null and priorityLabel != ''">node.priorityLabel = '${priorityLabel}',</if>
            <if test="securitySortId != null">node.securitySortId = ${securitySortId},</if>
            <if test="securityLevelId != null">node.securityLevelId = ${securityLevelId},</if>
            <if test="governStatus != null">node.governStatus ='${governStatus}',</if>
            <if test="governStatusLabel != null and governStatusLabel != ''">node.governStatusLabel = '${governStatusLabel}',</if>
            <if test="registerStatus != null">node.registerStatus = '${registerStatus}',</if>
            <if test="registerStatusLabel != null and registerStatusLabel != ''">node.registerStatusLabel = '${registerStatusLabel}',</if>
            <if test="businessObjectClassification != null">node.businessObjectClassification = '${businessObjectClassification}',</if>
            <if test="businessObjectClassificationLabel != null and businessObjectClassificationLabel != ''">node.businessObjectClassificationLabel = '${businessObjectClassificationLabel}',</if>
            <if test="dataOwnerPost != null and dataOwnerPost != ''">node.dataOwnerPost = '${dataOwnerPost}',</if>
            <if test="dataOwner != null and dataOwner != ''">node.dataOwner = '${dataOwner}',</if>
            <if test="dataManagePost != null and dataManagePost != ''">node.dataManagePost = '${dataManagePost}',</if>
            <if test="dataManage != null and dataManage != ''">node.dataManage = '${dataManage}',</if>
            <if test="dataStewardPost != null and dataStewardPost != ''">node.dataStewardPost = '${dataStewardPost}',</if>
            <if test="dataSteward != null and dataSteward != ''">node.dataSteward = '${dataSteward}',</if>
            <if test="itEngineerPost != null and itEngineerPost != ''">node.itEngineerPost = '${itEngineerPost}',</if>
            <if test="itEngineer != null and itEngineer != ''">node.itEngineer = '${itEngineer}',</if>
            <if test="dataEngineerPost != null and dataEngineerPost != ''">node.dataEngineerPost = '${dataEngineerPost}',</if>
            <if test="dataEngineer != null and dataEngineer != ''">node.dataEngineer = '${dataEngineer}',</if>
            <if test="dataDevelopPost != null and dataDevelopPost != ''">node.dataDevelopPost = '${dataDevelopPost}',</if>
            <if test="dataDeveloper != null and dataDeveloper != ''">node.dataDeveloper = '${dataDeveloper}',</if>
            <if test="serialNumber != null">node.serialNumber = ${serialNumber},</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="queryExistChildrenNodes" resultType="java.lang.String">
        select *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_logical_object)
        where node.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN node.code
        $$) as (code text)
    </select>

    <select id="queryExistUnLockChildrenNodes" resultType="java.lang.String">
        select *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_logical_object)
        where node.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.lockStatus = false and node.activeState = true
        RETURN node.code
        $$) as (code text)
    </select>

    <select id="delete" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_business_object)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.BusinessObject" resultType="com.datalink.fdop.govern.api.domain.BusinessObject">
        SELECT business_object.*,
               data_resources.code as dataResourcesCode, data_resources.name as dataResourcesName,
               data_application_framework.code as sourceSystemCode, data_application_framework.name as sourceSystemName,
               data_security_classification.code as securitySortCode, data_security_classification.name as securitySortName,
               data_security_level.code as securityLevelCode, data_security_level.name as securityLevelName
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_business_object)
        <where>
            <if test="entity.id != null">AND entity.id = ${entity.id}</if>
            <if test="entity.pid != null">AND entity.pid = ${entity.pid}</if>
            <if test="entity.code != null and entity.code != ''">AND entity.code =~'.*${entity.code}.*'</if>
            <if test="entity.name != null and entity.name != ''">AND entity.name =~'.*${entity.name}.*'</if>
            <if test="entity.version != null and entity.version != ''">AND entity.version =~'.*${entity.version}.*'</if>
            <if test="entity.isInformation != null">AND entity.isInformation = '${entity.isInformation}'</if>
            <if test="entity.informationLabel != null and entity.informationLabel != ''">AND entity.informationLabel = '${entity.informationLabel}'</if>
            <if test="entity.description != null and entity.description != ''">AND entity.description =~'.*${entity.description}.*'</if>
            <if test="entity.priority != null">AND entity.priority ='${entity.priority}'</if>
            <if test="entity.priorityLabel != null and entity.priorityLabel != ''">AND entity.priorityLabel = '${entity.priorityLabel}'</if>
            <if test="entity.governStatus != null">AND entity.governStatus ='${entity.governStatus}'</if>
            <if test="entity.governStatusLabel != null and entity.governStatusLabel != ''">AND entity.governStatusLabel = '${entity.governStatusLabel}'</if>
            <if test="entity.registerStatus != null">AND entity.registerStatus ='${entity.registerStatus}'</if>
            <if test="entity.registerStatusLabel != null and entity.registerStatusLabel != ''">AND entity.registerStatusLabel = '${entity.registerStatusLabel}'</if>
            <if test="entity.dataOwnerPost != null and entity.dataOwnerPost != ''">AND entity.dataOwnerPost =~'.*${entity.dataOwnerPost}.*'</if>
            <if test="entity.dataOwner != null and entity.dataOwner != ''">AND entity.dataOwner =~'.*${entity.dataOwner}.*'</if>
            <if test="entity.dataManagePost != null and entity.dataManagePost != ''">AND entity.dataManagePost =~'.*${entity.dataManagePost}.*'</if>
            <if test="entity.dataManage != null and entity.dataManage != ''">AND entity.dataManage =~'.*${entity.dataManage}.*'</if>
            <if test="entity.dataStewardPost != null and entity.dataStewardPost != ''">AND entity.dataStewardPost =~'.*${entity.dataStewardPost}.*'</if>
            <if test="entity.dataSteward != null and entity.dataSteward != ''">AND entity.dataSteward =~'.*${entity.dataSteward}.*'</if>
            <if test="entity.itEngineerPost != null and entity.itEngineerPost != ''">AND entity.itEngineerPost =~'.*${entity.itEngineerPost}.*'</if>
            <if test="entity.itEngineer != null and entity.itEngineer != ''">AND entity.itEngineer =~'.*${entity.itEngineer}.*'</if>
            <if test="entity.dataEngineerPost != null and entity.dataEngineerPost != ''">AND entity.dataEngineerPost =~'.*${entity.dataEngineerPost}.*'</if>
            <if test="entity.dataEngineer != null and entity.dataEngineer != ''">AND entity.dataEngineer =~'.*${entity.dataEngineer}.*'</if>
            <if test="entity.dataDevelopPost != null and entity.dataDevelopPost != ''">AND entity.dataDevelopPost =~'.*${entity.dataDevelopPost}.*'</if>
            <if test="entity.dataDeveloper != null and entity.dataDeveloper != ''">AND entity.dataDeveloper =~'.*${entity.dataDeveloper}.*'</if>
        </where>
        RETURN
        entity.id,
        entity.pid,
        entity.code,
        entity.name,
        entity.version,
        entity.dataResourcesId,
        entity.isInformation,
        entity.informationLabel,
        entity.sourceSystemId,
        entity.description,
        entity.priority,
        entity.priorityLabel,
        entity.securitySortId,
        entity.securityLevelId,
        entity.businessObjectClassification,
        entity.businessObjectClassificationLabel,
        entity.governStatus,
        entity.governStatusLabel,
        entity.registerStatus,
        entity.registerStatusLabel,
        entity.dataOwnerPost,
        entity.dataOwner,
        entity.dataManagePost,
        entity.dataManage,
        entity.dataStewardPost,
        entity.dataSteward,
        entity.itEngineerPost,
        entity.itEngineer,
        entity.dataEngineerPost,
        entity.dataEngineer,
        entity.dataDevelopPost,
        entity.dataDeveloper,
        entity.serialNumber,
        entity.createBy,
        entity.createTime,
        entity.updateBy,
        entity.updateTime
        $$) as business_object(
        id text,
        pid text,
        code text,
        name text,
        version text,
        dataResourcesId text,
        isInformation text,
        informationLabel text,
        sourceSystemId text,
        description text,
        priority text,
        priorityLabel text,
        securitySortId text,
        securityLevelId text,
        businessObjectClassification text,
        businessObjectClassificationLabel text,
        governStatus text,
        governStatusLabel text,
        registerStatus text,
        registerStatusLabel text,
        dataOwnerPost text,
        dataOwner text,
        dataManagePost text,
        dataManage text,
        dataStewardPost text,
        dataSteward text,
        itEngineerPost text,
        itEngineer text,
        dataEngineerPost text,
        dataEngineer text,
        dataDevelopPost text,
        dataDeveloper text,
        serialNumber text,
        createBy text,
        createTime text,
        updateBy text,
        updateTime text
        )
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_resources)
        RETURN entity.id, entity.code, entity.name $$)
        as data_resources(id text, code text, name text) on business_object.dataResourcesId = data_resources.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_application_framework)
        RETURN entity.id, entity.code, entity.name $$)
        as data_application_framework(id text, code text, name text) on business_object.sourceSystemId = data_application_framework.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_classification)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_classification(id text, code text, name text) on business_object.securitySortId = data_security_classification.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_level)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_level(id text, code text, name text) on business_object.securityLevelId = data_security_level.id
        <where>
            <if test="entity.dataResourcesCode != null and entity.dataResourcesCode != ''">AND data_resources.code = '${entity.dataResourcesCode}'</if>
            <if test="entity.dataResourcesName != null and entity.dataResourcesName != ''">AND data_resources.name = '${entity.dataResourcesName}'</if>
            <if test="entity.sourceSystemCode != null and entity.sourceSystemCode != ''">AND data_application_framework.code = '${entity.sourceSystemCode}'</if>
            <if test="entity.sourceSystemName != null and entity.sourceSystemName != ''">AND data_application_framework.name = '${entity.sourceSystemName}'</if>
            <if test="entity.securitySortCode != null and entity.securitySortCode != ''">AND data_security_classification.code = '${entity.securitySortCode}'</if>
            <if test="entity.securitySortName != null and entity.securitySortName != ''">AND data_security_classification.name = '${entity.securitySortName}'</if>
            <if test="entity.securityLevelCode != null and entity.securityLevelCode != ''">AND data_security_level.code = '${entity.securityLevelCode}'</if>
            <if test="entity.securityLevelName != null and entity.securityLevelName != ''">AND data_security_level.name = '${entity.securityLevelName}'</if>
        </where>
        ORDER BY createTime ${sort}
    </select>

    <select id="getExportList" parameterType="com.datalink.fdop.govern.api.domain.BusinessObject" resultType="com.datalink.fdop.govern.api.domain.BusinessObject">
        SELECT business_object.*,
        data_resources.code as dataResourcesCode, data_resources.name as dataResourcesName,
        data_application_framework.code as sourceSystemCode, data_application_framework.name as sourceSystemName,
        data_security_classification.code as securitySortCode, data_security_classification.name as securitySortName,
        data_security_level.code as securityLevelCode, data_security_level.name as securityLevelName
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_business_object)
        <where>
            <if test="entity.id != null">AND entity.id = ${entity.id}</if>
            <if test="entity.pid != null">AND entity.pid = ${entity.pid}</if>
            <if test="entity.code != null and entity.code != ''">AND entity.code =~'.*${entity.code}.*'</if>
            <if test="entity.name != null and entity.name != ''">AND entity.name =~'.*${entity.name}.*'</if>
            <if test="entity.version != null and entity.version != ''">AND entity.version =~'.*${entity.version}.*'</if>
            <if test="entity.isInformation != null">AND entity.isInformation = '${entity.isInformation}'</if>
            <if test="entity.informationLabel != null and entity.informationLabel != ''">AND entity.informationLabel = '${entity.informationLabel}'</if>
            <if test="entity.description != null and entity.description != ''">AND entity.description =~'.*${entity.description}.*'</if>
            <if test="entity.priority != null">AND entity.priority ='${entity.priority}'</if>
            <if test="entity.priorityLabel != null and entity.priorityLabel != ''">AND entity.priorityLabel = '${entity.priorityLabel}'</if>
            <if test="entity.governStatus != null">AND entity.governStatus ='${entity.governStatus}'</if>
            <if test="entity.governStatusLabel != null and entity.governStatusLabel != ''">AND entity.governStatusLabel = '${entity.governStatusLabel}'</if>
            <if test="entity.registerStatus != null">AND entity.registerStatus ='${entity.registerStatus}'</if>
            <if test="entity.registerStatusLabel != null and entity.registerStatusLabel != ''">AND entity.registerStatusLabel = '${entity.registerStatusLabel}'</if>
            <if test="entity.dataOwnerPost != null and entity.dataOwnerPost != ''">AND entity.dataOwnerPost =~'.*${entity.dataOwnerPost}.*'</if>
            <if test="entity.dataOwner != null and entity.dataOwner != ''">AND entity.dataOwner =~'.*${entity.dataOwner}.*'</if>
            <if test="entity.dataManagePost != null and entity.dataManagePost != ''">AND entity.dataManagePost =~'.*${entity.dataManagePost}.*'</if>
            <if test="entity.dataManage != null and entity.dataManage != ''">AND entity.dataManage =~'.*${entity.dataManage}.*'</if>
            <if test="entity.dataStewardPost != null and entity.dataStewardPost != ''">AND entity.dataStewardPost =~'.*${entity.dataStewardPost}.*'</if>
            <if test="entity.dataSteward != null and entity.dataSteward != ''">AND entity.dataSteward =~'.*${entity.dataSteward}.*'</if>
            <if test="entity.itEngineerPost != null and entity.itEngineerPost != ''">AND entity.itEngineerPost =~'.*${entity.itEngineerPost}.*'</if>
            <if test="entity.itEngineer != null and entity.itEngineer != ''">AND entity.itEngineer =~'.*${entity.itEngineer}.*'</if>
            <if test="entity.dataEngineerPost != null and entity.dataEngineerPost != ''">AND entity.dataEngineerPost =~'.*${entity.dataEngineerPost}.*'</if>
            <if test="entity.dataEngineer != null and entity.dataEngineer != ''">AND entity.dataEngineer =~'.*${entity.dataEngineer}.*'</if>
            <if test="entity.dataDevelopPost != null and entity.dataDevelopPost != ''">AND entity.dataDevelopPost =~'.*${entity.dataDevelopPost}.*'</if>
            <if test="entity.dataDeveloper != null and entity.dataDeveloper != ''">AND entity.dataDeveloper =~'.*${entity.dataDeveloper}.*'</if>
        </where>
        RETURN
        entity.id,
        entity.pid,
        entity.code,
        entity.name,
        entity.version,
        entity.dataResourcesId,
        entity.isInformation,
        entity.informationLabel,
        entity.sourceSystemId,
        entity.description,
        entity.priority,
        entity.priorityLabel,
        entity.securitySortId,
        entity.securityLevelId,
        entity.businessObjectClassification,
        entity.businessObjectClassificationLabel,
        entity.governStatus,
        entity.governStatusLabel,
        entity.registerStatus,
        entity.registerStatusLabel,
        entity.dataOwnerPost,
        entity.dataOwner,
        entity.dataManagePost,
        entity.dataManage,
        entity.dataStewardPost,
        entity.dataSteward,
        entity.itEngineerPost,
        entity.itEngineer,
        entity.dataEngineerPost,
        entity.dataEngineer,
        entity.dataDevelopPost,
        entity.dataDeveloper,
        entity.serialNumber,
        entity.createBy,
        entity.createTime,
        entity.updateBy,
        entity.updateTime
        $$) as business_object(
        id text,
        pid text,
        code text,
        name text,
        version text,
        dataResourcesId text,
        isInformation text,
        informationLabel text,
        sourceSystemId text,
        description text,
        priority text,
        priorityLabel text,
        securitySortId text,
        securityLevelId text,
        businessObjectClassification text,
        businessObjectClassificationLabel text,
        governStatus text,
        governStatusLabel text,
        registerStatus text,
        registerStatusLabel text,
        dataOwnerPost text,
        dataOwner text,
        dataManagePost text,
        dataManage text,
        dataStewardPost text,
        dataSteward text,
        itEngineerPost text,
        itEngineer text,
        dataEngineerPost text,
        dataEngineer text,
        dataDevelopPost text,
        dataDeveloper text,
        serialNumber text,
        createBy text,
        createTime text,
        updateBy text,
        updateTime text
        )
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_resources)
        RETURN entity.id, entity.code, entity.name $$)
        as data_resources(id text, code text, name text) on business_object.dataResourcesId = data_resources.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_application_framework)
        RETURN entity.id, entity.code, entity.name $$)
        as data_application_framework(id text, code text, name text) on business_object.sourceSystemId = data_application_framework.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_classification)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_classification(id text, code text, name text) on business_object.securitySortId = data_security_classification.id
        left join ag_catalog.cypher('zjdata_graph', $$
        MATCH (entity:d_g_data_security_level)
        RETURN entity.id, entity.code, entity.name $$)
        as data_security_level(id text, code text, name text) on business_object.securityLevelId = data_security_level.id
        <where>
            <if test="entity.dataResourcesCode != null and entity.dataResourcesCode != ''">AND data_resources.code = '${entity.dataResourcesCode}'</if>
            <if test="entity.dataResourcesName != null and entity.dataResourcesName != ''">AND data_resources.name = '${entity.dataResourcesName}'</if>
            <if test="entity.sourceSystemCode != null and entity.sourceSystemCode != ''">AND data_application_framework.code = '${entity.sourceSystemCode}'</if>
            <if test="entity.sourceSystemName != null and entity.sourceSystemName != ''">AND data_application_framework.name = '${entity.sourceSystemName}'</if>
            <if test="entity.securitySortCode != null and entity.securitySortCode != ''">AND data_security_classification.code = '${entity.securitySortCode}'</if>
            <if test="entity.securitySortName != null and entity.securitySortName != ''">AND data_security_classification.name = '${entity.securitySortName}'</if>
            <if test="entity.securityLevelCode != null and entity.securityLevelCode != ''">AND data_security_level.code = '${entity.securityLevelCode}'</if>
            <if test="entity.securityLevelName != null and entity.securityLevelName != ''">AND data_security_level.name = '${entity.securityLevelName}'</if>
        </where>
        ORDER BY createTime ${sort}
    </select>

    <select id="register" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_business_object)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.registerStatus = 'REGISTER' and node.registerStatusLabel = '注册'
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="unregister" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_business_object)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.registerStatus = 'UNREGISTER' and node.registerStatusLabel = '未注册'
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="registerById" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_business_object)
        WHERE node.id = ${id}
        SET node.version = '${version}' and node.registerStatus = 'REGISTER' and node.registerStatusLabel = '注册'
        RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByIds" resultMap="vlabelItem">
        select *
        from ag_catalog.cypher('zjdata_graph',
                               $$ MATCH (node:d_g_business_object)
                                   WHERE node.id IN
                                <foreach collection="ids" item="id" open="[" separator="," close="]">
                                    ${id}
                                </foreach>
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


</mapper>