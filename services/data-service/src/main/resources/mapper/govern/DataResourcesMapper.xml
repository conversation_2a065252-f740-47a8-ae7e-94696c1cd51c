<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.DataResourcesMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataResources"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <sql id="dataResourceSelectFields">
        resources.*,areas.code as areasCode, areas.name as areasName, theme.code as themeCode, theme.name as themeName, sort.code as securitySortCode, sort.name as securitySortName,
            level.code as securityLevelCode, level.name as securityLevelName, application.code as sourceSystemCode, application.name as sourceSystemName
    </sql>

    <sql id="dataResourceListReturn">
        RETURN node.id, node.code, node.name, node.version, node.description, node.dataStatus,node.dataStatusLabel, node.isInformation ,node.informationLabel, node.isInner, node.innerLabel, node.priority,node.priorityLabel, node.isStructured, node.structuredLabel,
            node.dataType, node.dataTypeLabel, node.areasId, node.themeId, node.sourceSystemId, node.businessUnit, node.securitySortId, node.securityLevelId, node.departCode, node.dataOwnerPost, node.dataOwner, node.dataManagePost,
            node.dataManage, node.dataStewardPost, node.dataSteward, node.itEngineerPost, node.itEngineer, node.dataEngineerPost, node.dataEngineer, node.dataDevelopPost, node.dataDeveloper,
            node.disuseStatus, node.createTime, node.updateTime $$) as resources (id BIGINT, code TEXT, name TEXT, version TEXT, description TEXT, dataStatus TEXT, dataStatusLabel TEXT, isInformation TEXT, informationLabel TEXT,
            isInner TEXT, innerLabel TEXT, priority TEXT, priorityLabel TEXT, isStructured TEXT, structuredLabel TEXT, dataType TEXT, dataTypeLabel TEXT, areasId BIGINT, themeId BIGINT, sourceSystemId BIGINT, businessUnit TEXT, securitySortId BIGINT, securityLevelId BIGINT,
            departCode TEXT, dataOwnerPost TEXT, dataOwner TEXT, dataManagePost TEXT, dataManage TEXT, dataStewardPost TEXT, dataSteward TEXT, itEngineerPost TEXT,
            itEngineer TEXT, dataEngineerPost TEXT, dataEngineer TEXT, dataDevelopPost TEXT, dataDeveloper TEXT, disuseStatus BOOLEAN, createTime TEXT, updateTime TEXT
        )
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (areas:d_g_data_process_framework) RETURN areas.id,areas.code,areas.name $$) as areas (id BIGINT,code TEXT,name TEXT) on resources.areasId = areas.id
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (theme:d_g_data_process_framework) RETURN theme.id,theme.code,theme.name $$) as theme (id BIGINT,code TEXT,name TEXT) on resources.themeId = theme.id
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (sort:d_g_data_security_classification) RETURN sort.id,sort.code,sort.name $$) as sort (id BIGINT,code TEXT,name TEXT) on resources.securitySortId = sort.id
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (level:d_g_data_security_level) RETURN level.id,level.code,level.name $$) as level (id BIGINT,code TEXT,name TEXT) on resources.securityLevelId = level.id
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (application:d_g_data_application_framework) RETURN application.id,application.code,application.name $$) as application (id BIGINT,code TEXT,name TEXT) on resources.sourceSystemId = application.id
    </sql>

    <select id="insert" parameterType="com.datalink.fdop.govern.api.domain.DataResources" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (resources:d_g_data_resources ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getResourcesAgeStr(resources)}) RETURN id(resources),
                               properties(resources)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.DataResources" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (resources:d_g_data_resources)
        WHERE resources.id = ${id}
        <set>
            <if test="code != null and code != ''">resources.code = '${code}',</if>
            <if test="name != null and name != ''">resources.name = '${name}',</if>
            <if test="version != null and version != ''">resources.version = '${version}',</if>
            <if test="description != null and description != ''">resources.description = '${description}',</if>
            <if test="dataStatus != null">resources.dataStatus = '${dataStatus}',</if>
            <if test="dataStatusLabel != null and dataStatusLabel != ''">resources.dataStatusLabel = '${dataStatusLabel}',</if>
            <if test="isInformation != null">resources.isInformation = '${isInformation}',</if>
            <if test="informationLabel != null and informationLabel != ''">resources.informationLabel = '${informationLabel}',</if>
            <if test="isInner != null">resources.isInner = '${isInner}',</if>
            <if test="innerLabel != null and innerLabel != ''">resources.innerLabel = '${innerLabel}',</if>
            <if test="priority != null">resources.priority = '${priority}',</if>
            <if test="priorityLabel != null and priorityLabel != ''">resources.priorityLabel = '${priorityLabel}',</if>
            <if test="isStructured != null">resources.isStructured = '${isStructured}',</if>
            <if test="structuredLabel != null and structuredLabel != ''">resources.structuredLabel = '${structuredLabel}',</if>
            <if test="dataType != null">resources.dataType = '${dataType}',</if>
            <if test="dataTypeLabel != null and dataTypeLabel != ''">resources.dataTypeLabel = '${dataTypeLabel}',</if>
            <if test="areasId != null and areasId != ''">resources.areasId = ${areasId},</if>
            <if test="areasCode != null and areasCode != ''">resources.areasCode = '${areasCode}',</if>
            <if test="areasName != null and areasName != ''">resources.areasName = '${areasName}',</if>
            <if test="themeId != null and themeId != ''">resources.themeId = ${themeId},</if>
            <if test="themeCode != null and themeCode != ''">resources.themeCode = '${themeCode}',</if>
            <if test="themeName != null and themeName != ''">resources.themeName = '${themeName}',</if>
            <if test="sourceSystemId != null and sourceSystemId != ''">resources.sourceSystemId = ${sourceSystemId},</if>
            <if test="sourceSystemCode != null and sourceSystemCode != ''">resources.sourceSystemCode = '${sourceSystemCode}',</if>
            <if test="sourceSystemName != null and sourceSystemName != ''">resources.sourceSystemName = '${sourceSystemName}',</if>
            <if test="businessUnit != null and businessUnit != ''">resources.businessUnit = '${businessUnit}',</if>
            <if test="securitySortId != null and securitySortId != ''">resources.securitySortId = ${securitySortId},</if>
            <if test="securitySortCode != null and securitySortCode != ''">resources.securitySortCode = '${securitySortCode}',</if>
            <if test="securitySortName != null and securitySortName != ''">resources.securitySortName = '${securitySortName}',</if>
            <if test="securityLevelId != null and securityLevelId != ''">resources.securityLevelId = ${securityLevelId},</if>
            <if test="securityLevelCode != null and securityLevelCode != ''">resources.securityLevelCode = '${securityLevelCode}',</if>
            <if test="securityLevelName != null and securityLevelName != ''">resources.securityLevelName = '${securityLevelName}',</if>
            <if test="departCode != null and departCode != ''">resources.departCode = '${departCode}',</if>
            <if test="dataOwnerPost != null and dataOwnerPost != ''">resources.dataOwnerPost = '${dataOwnerPost}',</if>
            <if test="dataOwner != null and dataOwner != ''">resources.dataOwner = '${dataOwner}',</if>
            <if test="dataManagePost != null and dataManagePost != ''">resources.dataManagePost = '${dataManagePost}',</if>
            <if test="dataManage != null and dataManage != ''">resources.dataManage = '${dataManage}',</if>
            <if test="dataStewardPost != null and dataStewardPost != ''">resources.dataStewardPost = '${dataStewardPost}',</if>
            <if test="dataSteward != null and dataSteward != ''">resources.dataSteward = '${dataSteward}',</if>
            <if test="itEngineerPost != null and itEngineerPost != ''">resources.itEngineerPost = '${itEngineerPost}',</if>
            <if test="itEngineer != null and itEngineer != ''">resources.itEngineer = '${itEngineer}',</if>
            <if test="dataEngineerPost != null and dataEngineerPost != ''">resources.dataEngineerPost = '${dataEngineerPost}',</if>
            <if test="dataEngineer != null and dataEngineer != ''">resources.dataEngineer = '${dataEngineer}',</if>
            <if test="dataDevelopPost != null and dataDevelopPost != ''">resources.dataDevelopPost = '${dataDevelopPost}',</if>
            <if test="dataDeveloper != null and dataDeveloper != ''">resources.dataDeveloper = '${dataDeveloper}',</if>
            resources.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            resources.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(resources), properties(resources)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateDataResourcesStatusById" parameterType="com.datalink.fdop.govern.api.domain.DataResources" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (resources:d_g_data_resources)
        WHERE resources.id = ${id}
        <set>
            <if test="dataStatus != null and dataStatus != ''">resources.dataStatus = '${dataStatus}',</if>
            resources.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            resources.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(resources), properties(resources)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (resources:d_g_data_resources) WHERE resources.id = ${id} RETURN id(resources), properties(resources)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (resources:d_g_data_resources) WHERE resources.code = '${code}' RETURN id(resources), properties(resources)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (resources:d_g_data_resources) WHERE resources.code = '${code}' AND resources.id &lt;&gt; ${id} RETURN id(resources),
                               properties(resources)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (resources:d_g_data_resources)
        WHERE resources.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE resources RETURN id(resources),properties(resources) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="overview" resultType="com.datalink.fdop.govern.api.domain.DataResources">
        WITH overview as (
            SELECT
                <include refid="dataResourceSelectFields"></include>
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_resources)
            <where>
                <if test="searchVo != null ">
                    AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
                </if>
            </where>
            <include refid="dataResourceListReturn"></include>
        )
        select * from overview ORDER BY updateTime is null,updateTime ${sort}
    </select>

    <select id="getList" resultType="com.datalink.fdop.govern.api.domain.DataResources">
        WITH overview as (
        SELECT
        <include refid="dataResourceSelectFields"></include>
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_resources)
        <where>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        <include refid="dataResourceListReturn"></include>
        )
        select * from overview ORDER BY updateTime is null,updateTime ${sort}
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.DataResources" resultType="com.datalink.fdop.govern.api.domain.DataResources">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (resources:d_g_data_resources)
        <where>
            <if test="resources.id != null">AND resources.id = ${resources.id}</if>
            <if test="resources.code != null and resources.code != ''">AND resources.code =~'.*${resources.code}.*'
            </if>
            <if test="resources.name != null and resources.name != ''">AND resources.name =~'.*${resources.name}.*'
            </if>
            <if test="resources.description != null and resources.description != ''">AND resources.description
                =~'.*${resources.description}.*'
            </if>
            <if test="resources.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("resources",resources.searchCondition)})
            </if>
        </where>
        WITH resources
        ORDER BY resources.createTime DESC
        RETURN id(resources), properties(resources)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="listByDataTypeIsReport" parameterType="com.datalink.fdop.govern.api.domain.DataResources" resultType="com.datalink.fdop.govern.api.domain.DataResources">
        SELECT
            <include refid="dataResourceSelectFields"></include>
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_resources)
        WHERE (node.dataType = 'reportData' OR node.dataType = 'reportData_KPI')
            <if test="resources.code != null and resources.code != ''">AND node.code =~'.*${resources.code}.*'</if>
            <if test="resources.name != null and resources.name != ''">AND node.name =~'.*${resources.name}.*'</if>
            <if test="resources.description != null and resources.description != ''">AND resources.description =~'.*${resources.description}.*'</if>
        WITH node
        ORDER BY node.createTime DESC
        <include refid="dataResourceListReturn"></include>
    </select>

    <select id="listByNotReference" parameterType="com.datalink.fdop.govern.api.domain.DataResources" resultType="com.datalink.fdop.govern.api.domain.DataResources">
        SELECT
        <include refid="dataResourceSelectFields"></include>
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_resources)
        <where>
            <if test="ids != null and ids.size() > 0">
                not node.id in [
                <foreach collection="ids" item="id" separator=",">
                    ${id}
                </foreach>
                ]</if>
            <if test="resources.code != null and resources.code != ''">AND node.code =~'.*${resources.code}.*'</if>
            <if test="resources.name != null and resources.name != ''">AND node.name =~'.*${resources.name}.*'</if>
            <if test="resources.description != null and resources.description != ''">AND resources.description =~'.*${resources.description}.*'</if>
        </where>
        WITH node
        ORDER BY node.createTime DESC
        <include refid="dataResourceListReturn"></include>
    </select>

    <select id="selectListAll" parameterType="com.datalink.fdop.govern.api.domain.DataResources" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (resources:d_g_data_resources)
        <where>
            <if test="resources.id != null">AND resources.id = ${resources.id}</if>
            <if test="resources.code != null and resources.code != ''">AND resources.code =~'.*${resources.code}.*'
            </if>
            <if test="resources.name != null and resources.name != ''">AND resources.name =~'.*${resources.name}.*'
            </if>
            <if test="resources.description != null and resources.description != ''">AND resources.description
                =~'.*${resources.description}.*'
            </if>
            <if test="resources.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("resources",resources.searchCondition)})
            </if>
        </where>
        WITH resources
        ORDER BY resources.createTime DESC
        RETURN  resources.code as key,
        resources.name as value
        $$) as (key TEXT,value TEXT)
    </select>

    <select id="insertVersion" parameterType="com.datalink.fdop.govern.api.domain.DataResources" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (resources:d_g_data_resources_version ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getResourcesAgeStr(resources)}) RETURN id(resources),
                               properties(resources)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteVersion" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (resources:d_g_data_resources_version)
        WHERE resources.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE resources RETURN id(resources),properties(resources) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectVersionList" resultType="com.datalink.fdop.govern.api.domain.DataResources">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_resources_version)
        WHERE node.code = '${code}'
        RETURN node.id, node.code, node.name, node.version,node.createTime,node.updateTime
        $$) as (id BIGINT, code TEXT, name TEXT, version TEXT,createTime TEXT,updateTime TEXT)
    </select>

    <select id="selectVersionByCodeAndVersionNo" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (resources:d_g_data_resources_version) WHERE resources.code = '${code}' AND resources.version = '${version}' RETURN id(resources), properties(resources)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectReferenceIds" resultType="java.lang.Long">
        select *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_business_object)
            where node.dataResourcesId is not null
            RETURN node.dataResourcesId
                                            $$) as (dataResourcesId ag_catalog.agtype)
        union
        select *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_tag)
            where node.dataResourcesId is not null
            RETURN node.dataResourcesId
                                            $$) as (dataResourcesId ag_catalog.agtype)
    </select>

</mapper>