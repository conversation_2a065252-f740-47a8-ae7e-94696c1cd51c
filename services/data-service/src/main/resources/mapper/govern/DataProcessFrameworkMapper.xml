<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.datalink.fdop.govern.mapper.DataProcessFrameworkMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataProcessFramework"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="createNodeAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_process_framework_menu), (node:d_g_data_process_framework)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_g_data_process_framework_menu',endTable:'d_g_data_process_framework'}]->(node)-[:node_menu_edge
        {startTable:'d_g_data_process_framework',endTable:'d_g_data_process_framework_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="deleteNodeAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DELETE nme1, nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insert" parameterType="com.datalink.fdop.govern.api.domain.DataProcessFramework" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (process:d_g_data_process_framework ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getDataProcessFrameworkAgeStr(process)}) RETURN id(process),
                               properties(process)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.DataProcessFramework" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (process:d_g_data_process_framework)
        WHERE process.id = ${id}
        <set>
            <if test="code != null and code != ''">process.code = '${code}',</if>
            <if test="name != null and name != ''">process.name = '${name}',</if>
            <if test="pid != null and pid != ''">process.pid = ${pid},</if>
            <if test="pname != null and pname != ''">process.pname = '${pname}',</if>
            <if test="level != null and level != ''">process.level = '${level}',</if>
            <if test="description != null and description != ''">process.description = '${description}',</if>
            <if test="serialNumber != null">process.serialNumber = ${serialNumber},</if>
            process.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            process.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(process), properties(process)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (process:d_g_data_process_framework) WHERE process.id = ${id} RETURN id(process), properties(process)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (process:d_g_data_process_framework) WHERE process.code = '${code}' RETURN id(process), properties(process)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (process:d_g_data_process_framework) WHERE process.code = '${code}' AND process.id &lt;&gt; ${id} RETURN id(process),
                               properties(process)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (process:d_g_data_process_framework)
        WHERE process.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE process RETURN id(process),properties(process) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.DataProcessFramework" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (process:d_g_data_process_framework)
        <where>
            <if test="process.id != null">AND process.id = ${process.id}</if>
            <if test="process.code != null and process.code != ''">AND process.code =~'.*${process.code}.*'
            </if>
            <if test="process.name != null and process.name != ''">AND process.name =~'.*${process.name}.*'
            </if>
            <if test="process.description != null and process.description != ''">AND process.description
                =~'.*${process.description}.*'
            </if>
            <if test="process.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("process",process.searchCondition)})
            </if>
        </where>
        WITH process
        ORDER BY process.createTime DESC
        RETURN id(process), properties(process)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectListAll" parameterType="com.datalink.fdop.govern.api.domain.DataProcessFramework" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (process:d_g_data_process_framework)
        <where>
            <if test="process.id != null">AND process.id = ${process.id}</if>
            <if test="process.pid != null">AND process.pid = ${process.pid}</if>
            <if test="process.level != null and process.level != ''">AND process.level = '${process.level}'</if>
            <if test="process.code != null and process.code != ''">AND process.code =~'.*${process.code}.*'
            </if>
            <if test="process.name != null and process.name != ''">AND process.name =~'.*${process.name}.*'
            </if>
            <if test="process.description != null and process.description != ''">AND process.description
                =~'.*${process.description}.*'
            </if>
            <if test="process.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("process",process.searchCondition)})
            </if>
        </where>
        WITH process
        ORDER BY process.serialNumber ASC
        RETURN  process.id as key,
        process.code as value
        $$) as (key TEXT,value TEXT)
    </select>

    <select id="selectTree" resultType="com.datalink.fdop.govern.api.domain.DataProcessFrameworkTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (process:d_g_data_process_framework)
        <where>
            <if test="code != null and code != ''">process.code =~'.*${code}.*'</if>
            <if test="name != null and name != ''">AND process.name =~'.*${name}.*'</if>
            <if test="description != null and description != ''">AND process.description =~'.*${description}.*'</if>
        </where>
        RETURN process.id, process.pid, process.pname, process.code, process.name, process.level, process.description, 'MENU' as menuType
        ORDER BY process.code ${sort}
        $$) as (id BIGINT,pid BIGINT,pname TEXT,code TEXT,name TEXT,level TEXT,description TEXT,menuType TEXT)
    </select>

    <select id="overview" resultType="com.datalink.fdop.govern.api.domain.DataProcessFramework">
        WITH overview as (
            SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_process_framework)
            <where>
                <if test="pid != -1">AND node.pid = ${pid}</if>
                <if test="searchVo != null ">
                    AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
                </if>
            </where>
            RETURN node.id, node.pid, node.code, node.name, node.description,node.pname as
            menuName,node.createTime,node.updateTime,node.serialNumber,node.level
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,createTime
            TEXT,updateTime TEXT,serialNumber BIGINT,level TEXT)
        )
        select * from overview ORDER BY updateTime is null,updateTime desc,serialNumber ${sort}
    </select>

    <select id="querySerialNumber" resultType="int">
        SELECT (count(1) + 1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (process:d_g_data_process_framework) RETURN id(process), properties(process) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

</mapper>