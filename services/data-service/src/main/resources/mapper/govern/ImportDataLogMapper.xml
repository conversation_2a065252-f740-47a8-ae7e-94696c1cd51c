<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.ImportDataLogMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.ImportDataLog"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:${entity.tableName}
                                   ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getImportDataLogAgeStr(entity)})
                                   RETURN id(node), properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.ImportDataLog" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:${tableName})
        WHERE node.id = ${id}
        <set>
            <if test="status != null and status != ''">node.status = '${status}',</if>
            <if test="log != null and log != ''">node.log = '${log}',</if>
            node.endTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectById" resultType="com.datalink.fdop.govern.api.domain.ImportDataLog">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:${tableName})
        WHERE node.id = ${id}
        RETURN node.id, node.fileName, node.importBy, node.importTime, node.endTime, node.status, node.log
        $$) as (id text, fileName text, importBy text, importTime text, endTime text, status text, log text)
    </select>

    <select id="selectList" resultType="com.datalink.fdop.govern.api.domain.ImportDataLog">
        select * from (
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:${entity.tableName})
        <where>
            <if test="entity.fileName != null and entity.fileName != ''">
                AND node.fileName = '${entity.fileName}'
            </if>
            <if test="entity.status != null and entity.status != ''">
                AND node.status = '${entity.status}'
            </if>
        </where>
        RETURN node.id, node.fileName, node.importBy, node.importTime, node.endTime, node.status, node.log
        $$) as (id text, fileName text, importBy text, importTime text, endTime text, status text, log text)
                      ) a
        ORDER BY importTime ${sort}
    </select>

</mapper>