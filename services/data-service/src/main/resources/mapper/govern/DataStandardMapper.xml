<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.DataStandardMapper">

    <resultMap id="dataStandard" type="com.datalink.fdop.govern.api.domain.DataStandard">
        <id property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="standardId" column="standardId"/>
        <result property="fieldType" column="fieldType"/>
        <result property="length" column="length"/>
        <result property="decimalLength" column="decimalLength"/>
        <result property="actModelId" column="actModelId"/>
        <result property="approvalStatusType" column="approvalStatusType"/>
        <result property="version" column="version"/>
        <result property="versionName" column="versionName"/>
        <result property="instanceId" column="instanceId"/>
        <result property="menuName" column="menuName"/>
        <result property="regexRule" column="regexRule"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>



    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>
    <select id="deleteStandardMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[nme1:node_menu_edge]->(node)-[nme2:node_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND node.id = ${pid}
        DETACH DELETE  nme2 RETURN id(nme1),
        properties(nme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="updateContentById" parameterType="com.datalink.fdop.govern.api.domain.DataStandard" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard)
        WHERE node.id = ${dataStandard.id}
        <set>
            <if test="dataStandard.fieldType != null">node.fieldType = '${dataStandard.fieldType}',</if>
            <if test="dataStandard.length != null">node.length = ${dataStandard.length},</if>
            <if test="dataStandard.decimalLength != null">node.decimalLength = ${dataStandard.decimalLength},</if>
            <if test="dataStandard.regexRule != null and dataStandard.regexRule != ''">node.regexRule = ${dataStandard.regexRule.ageStr},</if>
            <if test="dataStandard.actModelId != null">node.actModelId = ${dataStandard.actModelId},</if>
            <if test="dataStandard.instanceId != null and dataStandard.instanceId != ''">node.instanceId = '${dataStandard.instanceId}',</if>
            <if test="dataStandard.approvalStatusType != null">node.approvalStatusType = '${dataStandard.approvalStatusType}',</if>
            <if test="dataStandard.version != null and dataStandard.version != ''">node.version = '${dataStandard.version}',</if>
            <if test="dataStandard.versionName != null and dataStandard.versionName != ''">node.versionName = '${dataStandard.versionName}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="updateShellByStandardId" parameterType="com.datalink.fdop.govern.api.domain.DataStandard" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard)
        WHERE node.standardId = ${dataStandard.standardId}
        <set>
            <if test="dataStandard.pid != null">node.pid = ${dataStandard.pid},</if>
            <if test="dataStandard.code != null and dataStandard.code != ''">node.code = '${dataStandard.code}',</if>
            <if test="dataStandard.name != null and dataStandard.name != ''">node.name = '${dataStandard.name}',</if>
            <if test="dataStandard.description != null and dataStandard.description != ''">node.description = '${dataStandard.description}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="insertStandard" parameterType="com.datalink.fdop.govern.api.domain.DataStandard" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_data_standard ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getStandardAgeStr(dataStandard)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectByCode" resultMap="dataStandard">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard) WHERE node.code = '${code}'  and node.version='${version}'
       RETURN node.id, node.pid, node.code, node.name, node.description,node.standardId, node.fieldType, node.length,
                               node.decimalLength, node.actModelId, node.approvalStatusType, node.version, node.versionName,node.instanceId, node.menuName,
                               node.regexRule LIMIT 1
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,standardId BIGINT, fieldType TEXT, length BIGINT,
                               decimalLength BIGINT, actModelId BIGINT, approvalStatusType TEXT, version TEXT, versionName TEXT,instanceId TEXT, menuName TEXT,
                               regexRule VARCHAR)

    </select>

    <select id="findByCode" resultMap="dataStandard">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard) WHERE node.code = '${code}'    WITH node ORDER BY node.createTime desc  RETURN node.id, node.pid, node.code, node.name, node.description,node.standardId, node.fieldType, node.length,
                               node.decimalLength, node.actModelId, node.approvalStatusType, node.version, node.versionName,node.instanceId, node.menuName,
                               node.regexRule LIMIT 1
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,standardId BIGINT, fieldType TEXT, length BIGINT,
                               decimalLength BIGINT, actModelId BIGINT, approvalStatusType TEXT, version TEXT, versionName TEXT,instanceId TEXT, menuName TEXT,
                               regexRule VARCHAR)
    </select>

    <select id="createStandardAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_standard_menu), (node:d_g_data_standard)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_g_data_standard_menu',endTable:'d_g_data_standard'}]->(node)-[:node_menu_edge
        {startTable:'d_g_data_standard',endTable:'d_g_data_standard_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="checkCodeIsExists" resultMap="dataStandard">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard) WHERE node.code = '${code}' AND node.standardId &lt;&gt; ${standardId}
                                                   RETURN node.id, node.pid, node.code, node.name, node.description,node.standardId, node.fieldType, node.length,
                               node.decimalLength, node.actModelId, node.approvalStatusType, node.version, node.versionName,node.instanceId, node.menuName,
                               node.regexRule LIMIT 1
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,standardId BIGINT, fieldType TEXT, length BIGINT,
                               decimalLength BIGINT, actModelId BIGINT, approvalStatusType TEXT, version TEXT, versionName TEXT,instanceId TEXT, menuName TEXT,
                               regexRule VARCHAR)
    </select>
    <select id="selectById" resultMap="dataStandard">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard) WHERE  node.id =${id}
                                                   RETURN node.id, node.pid, node.code, node.name, node.description,node.standardId, node.fieldType, node.length,
                               node.decimalLength, node.actModelId, node.approvalStatusType, node.version, node.versionName,node.instanceId, node.menuName,
                               node.regexRule LIMIT 1
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,standardId BIGINT, fieldType TEXT, length BIGINT,
                               decimalLength BIGINT, actModelId BIGINT, approvalStatusType TEXT, version TEXT, versionName TEXT,instanceId TEXT, menuName TEXT,
                               regexRule VARCHAR)
    </select>

    <select id="selectQualityByStandardId" resultType="com.datalink.fdop.quality.api.domain.DataQuality">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality) -[edge:d_q_data_quality_standard_edge]->(standard:d_g_data_standard)
            WHERE standard.id = ${id}
            RETURN quality.id, quality.code $$) as (id BIGINT,code TEXT)
    </select>

    <select id="createStandardMenuEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_standard_menu), (node:d_g_data_standard)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'d_g_data_standard_menu',endTable:'d_g_data_standard'}]->(node)-[:node_menu_edge
        {startTable:'d_g_data_standard',endTable:'d_g_data_standard_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectIdsByPid" resultType="java.lang.Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_g_data_standard_menu) -[nme:node_menu_edge]->(nodeE:d_g_data_standard)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>
    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectStandardTree" resultType="com.datalink.fdop.govern.api.domain.DataStandardTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (nodeS:d_g_data_standard) -[]->(menuE:d_g_data_standard_menu)
        where nodeS.id &lt;&gt; 1
            <if test="code != null and code != ''">AND nodeS.code =~'.*${code}.*'</if>
            <if test="approvalStatusType != null ">AND nodeS.approvalStatusType ='${approvalStatusType}'</if>
        RETURN nodeS.id, nodeS.pid, nodeS.code, nodeS.name, nodeS.description, 'NODE' as menuType,nodeS.createTime
        ,nodeS.version,nodeS.versionName
        ORDER BY nodeS.code ${sort}
        $$) as (id ag_catalog.agtype,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType
        TEXT,createTime TEXT,version TEXT,versionName TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard) WHERE node.pid = -1
        <if test="code != null and code != ''">AND node.code =~'.*${code}.*'</if>
        <if test="approvalStatusType != null ">AND node.approvalStatusType =~'${approvalStatusType}'</if>
        WITH DISTINCT (node)
        RETURN node.id, node.pid, node.code, node.name, node.description, 'NODE' as menuType,node.createTime
        ,node.version,node.versionName
        ORDER BY node.code ${sort}
        $$) as (id ag_catalog.agtype,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType
        TEXT,createTime TEXT,version TEXT,versionName TEXT)
        </select>
    <select id="selectByStandardId" resultMap="dataStandard">
        WITH standard AS (
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard)
            WHERE node.pid = -1 and node.standardId=${standardId}
            RETURN node.id, node.pid, node.code, node.name, node.description, '顶级菜单' as menuName,node.standardId,node.createTime,node.updateTime,node.version
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,  standardId BIGINT,createTime TEXT,updateTime TEXT,version TEXT)
            UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard) -[:node_menu_edge]->(menu:d_g_data_standard_menu)
        WHERE node.standardId=${standardId}
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as menuName,node.standardId,node.createTime,node.updateTime,node.version
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT, standardId BIGINT,createTime TEXT,updateTime TEXT,version TEXT)
        ) SELECT * FROM standard order by createTime
    </select>
    <select id="selectAll" parameterType="com.datalink.fdop.govern.api.domain.DataStandard"
            resultType="com.datalink.fdop.govern.api.domain.DataStandard">
        WITH standard AS (
        <if test="pids.contains(-1L)">
            SELECT *
            FROM
            ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard)
            WHERE node.pid = -1
            <if test="searchCondition != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
            </if>
            RETURN node.id, node.pid, node.code, node.name, node.description, '顶级菜单' as menuName,node.standardId,node.createTime,node.updateTime,node.version,node.versionName
            $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT,  standardId BIGINT,createTime TEXT,updateTime TEXT,version TEXT,versionName TEXT)
            UNION
        </if>
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard) -[:node_menu_edge]->(menu:d_g_data_standard_menu)
        WHERE node.pid in
        <foreach collection="pids" item="pid" open="[" separator="," close="]">
            ${pid}
        </foreach>
        <if test="searchCondition != null ">AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchCondition)})
        </if>
        RETURN node.id, node.pid, node.code, node.name, node.description, menu.code as menuName,node.standardId,node.createTime,node.updateTime,node.version,node.versionName
        $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT, menuName TEXT, standardId BIGINT,createTime TEXT,updateTime TEXT,version TEXT,versionName TEXT)
        ) SELECT distinct on(standard.code)* FROM standard  ORDER BY standard.code ${sort}
    </select>
    <select id="getFieldStandard" resultMap="dataStandard">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:d_g_synchronization_field)-[r:d_g_sync_field_standard_edge]->(node:d_g_data_standard)
            WHERE d.id = ${fieldId}
            RETURN node.id, node.pid, node.code, node.name, node.description,node.standardId, node.fieldType, node.length,
                               node.decimalLength, node.actModelId, node.approvalStatusType, node.version, node.versionName,node.instanceId, node.menuName,
                               node.regexRule
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,standardId BIGINT, fieldType TEXT, length BIGINT,
                               decimalLength BIGINT, actModelId BIGINT, approvalStatusType TEXT, version TEXT, versionName TEXT,instanceId TEXT, menuName TEXT,
                               regexRule VARCHAR)
    </select>


</mapper>