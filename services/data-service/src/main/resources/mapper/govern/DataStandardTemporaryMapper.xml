<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.DataStandardTemporaryMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataStandardTemporary"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>



    <resultMap id="dataStandardTemporary" type="com.datalink.fdop.govern.api.domain.DataStandardTemporary">
        <id property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="standardId" column="standardId"/>
        <result property="fieldType" column="fieldType"/>
        <result property="length" column="length"/>
        <result property="decimalLength" column="decimalLength"/>
        <result property="actModelId" column="actModelId"/>
        <result property="approvalStatusType" column="approvalStatusType"/>
        <result property="version" column="version"/>
        <result property="versionName" column="versionName"/>
        <result property="instanceId" column="instanceId"/>
        <result property="menuName" column="menuName"/>
        <result property="userId" column="userId"/>
        <result property="regexRule" column="regexRule"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <select id="deleteByIdAndUserId" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard_temporary)
        WHERE node.id =${id} and node.userId=${userId}
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)

    </select>

    <select id="insertStandardTemporary" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_data_standard_temporary ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getStandardTemporaryAgeStr(dataStandardTemporary)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectByCode" resultMap="dataStandardTemporary">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard_temporary) WHERE node.code = '${code}'  and node.version='${version}' and node.userId=${userId}
            RETURN node.id, node.pid, node.code, node.name, node.description,node.standardId, node.fieldType, node.length,
                               node.decimalLength, node.actModelId, node.approvalStatusType, node.version, node.versionName,node.instanceId, node.menuName,
                               node.regexRule,node.userId LIMIT 1
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,standardId BIGINT, fieldType TEXT, length BIGINT,
                               decimalLength BIGINT, actModelId BIGINT, approvalStatusType TEXT, version TEXT, versionName TEXT,instanceId TEXT, menuName TEXT,
                               regexRule VARCHAR,userId BIGINT)

    </select>

    <select id="checkCodeIsExists" resultMap="dataStandardTemporary">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard_temporary) WHERE node.code = '${code}' AND node.standardId &lt;&gt; ${standardId}
                                                   RETURN node.id, node.pid, node.code, node.name, node.description,node.standardId, node.fieldType, node.length,
                               node.decimalLength, node.actModelId, node.approvalStatusType, node.version, node.versionName,node.instanceId, node.menuName,
                               node.regexRule,node.userId  LIMIT 1
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,standardId BIGINT, fieldType TEXT, length BIGINT,
                               decimalLength BIGINT, actModelId BIGINT, approvalStatusType TEXT, version TEXT, versionName TEXT,instanceId TEXT, menuName TEXT,
                               regexRule VARCHAR,userId BIGINT)
    </select>

    <select id="updateContentById" parameterType="com.datalink.fdop.govern.api.domain.DataStandardTemporary" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard_temporary)
        WHERE node.id = ${id} and node.userId=${userId}
        <set>
            <if test="fieldType != null">node.fieldType = '${fieldType}',</if>
            <if test="length != null">node.length = ${length},</if>
            <if test="decimalLength != null">node.decimalLength = ${decimalLength},</if>
            <if test="regexRule != null and regexRule != ''">node.regexRule = ${regexRule.ageStr},</if>
            <if test="actModelId != null">node.actModelId = ${actModelId},</if>
            <if test="userId != null">node.userId = ${userId},</if>
            <if test="instanceId != null and instanceId!=''">node.instanceId = '${instanceId}',</if>
            <if test="approvalStatusType != null">node.approvalStatusType = '${approvalStatusType}',</if>
            <if test="version != null and version != ''">node.version = '${version}',</if>
            <if test="versionName != null and versionName != ''">node.versionName = '${versionName}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="updateShellByStandardId"  parameterType="com.datalink.fdop.govern.api.domain.DataStandardTemporary" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard_temporary)
        WHERE node.standardId = ${standardId} and node.userId=${userId}
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="name != null and name != ''">node.name = '${name}',</if>
            <if test="description != null and description != ''">node.description = '${description}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_standard_temporary)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>
    <select id="selectALLByCode" resultMap="dataStandardTemporary">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_standard_temporary) WHERE node.code = '${code}'   and node.version='${version}'
            RETURN node.id, node.pid, node.code, node.name, node.description,node.standardId, node.fieldType, node.length,
                               node.decimalLength, node.actModelId, node.approvalStatusType, node.version, node.versionName,node.instanceId, node.menuName,
                               node.regexRule,node.userId
                                   $$) as (id BIGINT, pid BIGINT, code TEXT, name TEXT, description TEXT,standardId BIGINT, fieldType TEXT, length BIGINT,
                               decimalLength BIGINT, actModelId BIGINT, approvalStatusType TEXT, version TEXT, versionName TEXT,instanceId TEXT, menuName TEXT,
                               regexRule VARCHAR,userId BIGINT)
    </select>


</mapper>