<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.QualityCheckMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.QualityCheck"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>




    <select id="selectTree" resultType="com.datalink.fdop.govern.api.domain.QualityCheckMenuTree">
    SELECT *
    FROM ag_catalog.cypher('zjdata_graph', $$
    MATCH (node:s_c_createqualitycheck)
    <where>
        <if test="code != null and code != ''">node.code =~'.*${code}.*'</if>
    </where>
    RETURN node.id, node.pid, node.code, node.name, node.description, 'NODE' as menuType
    ORDER BY node.code ${sort}
    $$) as (id BIGINT,pid BIGINT,code TEXT,name TEXT,description TEXT,menuType TEXT)
    </select>



    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:s_c_seatunnel_cmd_menu) -[nme:node_menu_edge]->(nodeE:s_c_seatunnel_cmd)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>




    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_createqualitycheck)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="createSeaTunnelCmdAndMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:s_c_createqualitycheck_plan), (node:s_c_createqualitycheck)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:node_menu_edge
        {startTable:'s_c_createqualitycheck_plan',endTable:'s_c_createqualitycheck'}]->(node)-[:node_menu_edge
        {startTable:'s_c_createqualitycheck',endTable:'s_c_createqualitycheck_plan'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="insertSeaTunnelCmd" parameterType="com.datalink.fdop.govern.api.domain.QualityCheck" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:s_c_createqualitycheck ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getSeaTunnelCmdAgeStr(seaTunnelCmd)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_createqualitycheck)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE node RETURN id(node),properties(node) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>



    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.QualityCheck" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:s_c_createqualitycheck)
        WHERE node.id = ${id}
        <set>
            <if test="pid != null">node.pid = ${pid},</if>
            <if test="code != null and code != ''">node.code = '${code}',</if>
            <if test="projectCode != null and projectCode != ''">node.projectCode = '${projectCode}',</if>
            <if test="taskCode != null and taskCode != ''">node.taskCode = '${taskCode}',</if>
            <if test="checkType != null and checkType != ''">node.checkType = '${checkType}',</if>
            <if test="checkTargetId != null and checkTargetId != ''">node.checkTargetId = '${checkTargetId}',</if>
            <if test="checkTargetProperty != null and checkTargetProperty != ''">node.checkTargetProperty = '${checkTargetProperty}',</if>
            <if test="retentionPeriodOfErroneous != null">node.retentionPeriodOfErroneous = '${retentionPeriodOfErroneous}',</if>
            <if test="inspectionMethod != null">node.inspectionMethod = '${inspectionMethod}',</if>
            <if test="checkDimensions != null">node.checkDimensions = '${checkDimensions}',</if>
            <if test="checkTargetName != null">node.checkTargetName = '${checkTargetName}',</if>
            <if test="errorCheckingSql != null">node.errorCheckingSql = '${errorCheckingSql}',</if>
            <if test="sql != null and sql != ''">node.sql = '${sql}',</if>
            <if test="entityId != null and entityId != ''">node.entityId = '${entityId}',</if>
            <if test="errorTableJson != null and errorTableJson != ''">node.errorTableJson = '${errorTableJson}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>



    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:s_c_createqualitycheck) WHERE node.id = ${id} RETURN id(node), properties(node) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


</mapper>
