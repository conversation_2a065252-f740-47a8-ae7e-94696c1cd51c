<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.DataTagMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataTag"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="insert" parameterType="com.datalink.fdop.govern.api.domain.DataTag" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (tag:d_g_data_tag ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getDataTagAgeStr(tag)}) RETURN id(tag),
                               properties(tag)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="updateInsertById" parameterType="com.datalink.fdop.govern.api.domain.DataTag" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (tag:d_g_data_tag)
        WHERE tag.id = ${id}
        <set>
            <if test="dataEngineerPost != null and dataEngineerPost != ''">tag.dataEngineerPost = '${dataEngineerPost}',</if>
            <if test="dataEngineer != null and dataEngineer != ''">tag.dataEngineer = '${dataEngineer}',</if>
            <if test="securitySortId != null">tag.securitySortId = ${securitySortId},</if>
            <if test="securitySortCode != null and securitySortCode != ''">tag.securitySortCode = '${securitySortCode}',</if>
            <if test="securitySortName != null and securitySortName != ''">tag.securitySortName = '${securitySortName}',</if>
            <if test="securityLevelId != null">tag.securityLevelId = ${securityLevelId},</if>
            <if test="securityLevelCode != null and securityLevelCode != ''">tag.securityLevelCode = '${securityLevelCode}',</if>
            <if test="securityLevelName != null and securityLevelName != ''">tag.securityLevelName = '${securityLevelName}',</if>
            <if test="range != null and range != ''">tag.range = '${range}',</if>
            <if test="scope != null and scope != ''">tag.scope = '${scope}',</if>
            <if test="threshold != null and threshold != ''">tag.threshold = '${threshold}',</if>
            <if test="synchronizationFrequency != null and synchronizationFrequency != ''">tag.synchronizationFrequency = '${synchronizationFrequency}',</if>
            <if test="tagFactor != null and tagFactor != ''">tag.tagFactor = '${tagFactor}',</if>
            <if test="businessCalculationFormula != null and businessCalculationFormula != ''">tag.businessCalculationFormula = '${businessCalculationFormula}',</if>
            <if test="tagFactorCalculationFormula != null and tagFactorCalculationFormula != ''">tag.tagFactorCalculationFormula = '${tagFactorCalculationFormula}',</if>
            <if test="calculationUnit != null and calculationUnit != ''">tag.calculationUnit = '${calculationUnit}',</if>
            <if test="calculationPrecision != null and calculationPrecision != ''">tag.calculationPrecision = '${calculationPrecision}',</if>
            <if test="dataPrecision != null and dataPrecision != ''">tag.dataPrecision = '${dataPrecision}',</if>
            <if test="analysisMethod != null and analysisMethod != ''">tag.analysisMethod = '${analysisMethod}',</if>
            <if test="statisticalCycle != null and statisticalCycle != ''">tag.statisticalCycle = '${statisticalCycle}',</if>
            <if test="analysisDimension != null and analysisDimension != ''">tag.analysisDimension = '${analysisDimension}',</if>
            <if test="relatedBusinessObject != null and relatedBusinessObject != ''">tag.relatedBusinessObject = '${relatedBusinessObject}',</if>
            <if test="dataSource != null and dataSource != ''">tag.dataSource = '${dataSource}',</if>
            <if test="valueField != null and valueField != ''">tag.valueField = '${valueField}',</if>
            <if test="dataEfficacy != null and dataEfficacy != ''">tag.dataEfficacy = '${dataEfficacy}',</if>
            <if test="maintainer != null and maintainer != ''">tag.maintainer = '${maintainer}',</if>
            <if test="status != null">tag.status = ${status},</if>
            <if test="dimensionRange != null and dimensionRange != ''">tag.dimensionRange = '${dimensionRange}',</if>
            <if test="fillUnit != null and fillUnit != ''">tag.fillUnit = '${fillUnit}',</if>
            <if test="sourceDataDesc != null and sourceDataDesc != ''">tag.sourceDataDesc = '${sourceDataDesc}',</if>
            <if test="companyLevel != null and companyLevel != ''">tag.companyLevel = '${companyLevel}',</if>
            <if test="tagUnit != null and tagUnit != ''">tag.tagUnit = '${tagUnit}',</if>
            <if test="tagDepartment != null and tagDepartment != ''">tag.tagDepartment = '${tagDepartment}',</if>
            <if test="tagManagePost != null and tagManagePost != ''">tag.tagManagePost = '${tagManagePost}',</if>
            <if test="tagConsumeDepartment != null and tagConsumeDepartment != ''">tag.tagConsumeDepartment = '${tagConsumeDepartment}',</if>
            <if test="tagProcessLogic != null and tagProcessLogic != ''">tag.tagProcessLogic = '${tagProcessLogic}',</if>
            <if test="tagFillDepartment != null and tagFillDepartment != ''">tag.tagFillDepartment = '${tagFillDepartment}',</if>
            <if test="tagFillPost != null and tagFillPost != ''">tag.tagFillPost = '${tagFillPost}',</if>

            <if test="indicatorType != null and indicatorType != ''">tag.indicatorType = '${indicatorType}',</if>
            <if test="indicatorData != null and indicatorData != ''">tag.indicatorData = '${indicatorData}',</if>
            <if test="monitor != null and monitor != ''">tag.monitor = '${monitor}',</if>
            <if test="monitorPersonnel != null and monitorPersonnel != ''">tag.monitorPersonnel = '${monitorPersonnel}',</if>
            <if test="submissionTime != null">tag.submissionTime = '${submissionTime}',</if>
            <if test="businessType != null and businessType != ''">tag.businessType = '${businessType}',</if>
            <if test="dataLength != null and dataLength != ''">tag.dataLength = '${dataLength}',</if>
            <if test="isEnum != null and isEnum != ''">tag.isEnum = '${isEnum}',</if>
            <if test="isSupport != null and isSupport != ''">tag.isSupport = '${isSupport}',</if>
            <if test="physicalTable != null and physicalTable != ''">tag.physicalTable = '${physicalTable}',</if>
            <if test="systemDescribe != null and systemDescribe != ''">tag.systemDescribe = '${systemDescribe}',</if>
            <if test="indicatorMethod != null and indicatorMethod != ''">tag.indicatorMethod = '${indicatorMethod}',</if>

        </set>
        RETURN id(tag), properties(tag)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.DataTag" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (tag:d_g_data_tag)
        WHERE tag.id = ${id}
        <set>
            <if test="pid != null and pid != -1">tag.pid = ${pid},</if>
            <if test="dataResourceId != null and dataResourceId != -1">tag.dataResourceId = ${dataResourceId},</if>
            <if test="code != null and code != ''">tag.code = '${code}',</if>
            <if test="name != null and name != ''">tag.name = '${name}',</if>
            <if test="registerStatus != null">tag.registerStatus = '${registerStatus}',</if>
            <if test="registerStatusLabel != null and registerStatusLabel != ''">tag.registerStatusLabel = '${registerStatusLabel}',</if>
            <if test="governStatus != null">tag.governStatus = '${governStatus}',</if>
            <if test="governStatusLabel != null and governStatusLabel != ''">tag.governStatusLabel = '${governStatusLabel}',</if>
            <if test="priority != null">tag.priority = '${priority}',</if>
            <if test="priorityLabel != null and priorityLabel != ''">tag.priorityLabel = '${priorityLabel}',</if>
            <if test="dataType != null">tag.dataType = '${dataType}',</if>
            <if test="dataTypeLabel != null and dataTypeLabel != ''">tag.dataTypeLabel = '${dataTypeLabel}',</if>
            <if test="tagAlias != null and tagAlias != ''">tag.tagAlias = '${tagAlias}',</if>
            <if test="isInformation != null">tag.isInformation = '${isInformation}',</if>
            <if test="informationLabel != null and informationLabel != ''">tag.informationLabel = '${informationLabel}',</if>
            <if test="systemDataRetrieval != null and systemDataRetrieval != ''">tag.systemDataRetrieval = '${systemDataRetrieval}',</if>
            <if test="dataOwner != null and dataOwner != ''">tag.dataOwner = '${dataOwner}',</if>
            <if test="dataOwnerPost != null and dataOwnerPost != ''">tag.dataOwnerPost = '${dataOwnerPost}',</if>
            <if test="dataManagePost != null and dataManagePost != ''">tag.dataManagePost = '${dataManagePost}',</if>
            <if test="dataManage != null and dataManage != ''">tag.dataManage = '${dataManage}',</if>
            <if test="dataStewardPost != null and dataStewardPost != ''">tag.dataStewardPost = '${dataStewardPost}',</if>
            <if test="dataSteward != null and dataSteward != ''">tag.dataSteward = '${dataSteward}',</if>
            <if test="itEngineerPost != null and itEngineerPost != ''">tag.itEngineerPost = '${itEngineerPost}',</if>
            <if test="itEngineer != null and itEngineer != ''">tag.itEngineer = '${itEngineer}',</if>
            <if test="dataEngineerPost != null and dataEngineerPost != ''">tag.dataEngineerPost = '${dataEngineerPost}',</if>
            <if test="dataEngineer != null and dataEngineer != ''">tag.dataEngineer = '${dataEngineer}',</if>
            <if test="dataDevelopPost != null and dataDevelopPost != ''">tag.dataDevelopPost = '${dataDevelopPost}',</if>
            <if test="dataDeveloper != null and dataDeveloper != ''">tag.dataDeveloper = '${dataDeveloper}',</if>
            <if test="securitySortId != null">tag.securitySortId = ${securitySortId},</if>
            <if test="securitySortCode != null and securitySortCode != ''">tag.securitySortCode = '${securitySortCode}',</if>
            <if test="securitySortName != null and securitySortName != ''">tag.securitySortName = '${securitySortName}',</if>
            <if test="securityLevelId != null">tag.securityLevelId = ${securityLevelId},</if>
            <if test="securityLevelCode != null and securityLevelCode != ''">tag.securityLevelCode = '${securityLevelCode}',</if>
            <if test="securityLevelName != null and securityLevelName != ''">tag.securityLevelName = '${securityLevelName}',</if>
            <if test="range != null and range != ''">tag.range = '${range}',</if>
            <if test="scope != null and scope != ''">tag.scope = '${scope}',</if>
            <if test="threshold != null and threshold != ''">tag.threshold = '${threshold}',</if>
            <if test="synchronizationFrequency != null and synchronizationFrequency != ''">tag.synchronizationFrequency = '${synchronizationFrequency}',</if>
            <if test="tagFactor != null and tagFactor != ''">tag.tagFactor = '${tagFactor}',</if>
            <if test="businessCalculationFormula != null and businessCalculationFormula != ''">tag.businessCalculationFormula = '${businessCalculationFormula}',</if>
            <if test="tagFactorCalculationFormula != null and tagFactorCalculationFormula != ''">tag.tagFactorCalculationFormula = '${tagFactorCalculationFormula}',</if>
            <if test="calculationUnit != null and calculationUnit != ''">tag.calculationUnit = '${calculationUnit}',</if>
            <if test="calculationPrecision != null and calculationPrecision != ''">tag.calculationPrecision = '${calculationPrecision}',</if>
            <if test="dataPrecision != null and dataPrecision != ''">tag.dataPrecision = '${dataPrecision}',</if>
            <if test="analysisMethod != null and analysisMethod != ''">tag.analysisMethod = '${analysisMethod}',</if>
            <if test="statisticalCycle != null and statisticalCycle != ''">tag.statisticalCycle = '${statisticalCycle}',</if>
            <if test="analysisDimension != null and analysisDimension != ''">tag.analysisDimension = '${analysisDimension}',</if>
            <if test="relatedBusinessObject != null and relatedBusinessObject != ''">tag.relatedBusinessObject = '${relatedBusinessObject}',</if>
            <if test="dataSource != null and dataSource != ''">tag.dataSource = '${dataSource}',</if>
            <if test="valueField != null and valueField != ''">tag.valueField = '${valueField}',</if>
            <if test="dataEfficacy != null and dataEfficacy != ''">tag.dataEfficacy = '${dataEfficacy}',</if>
            <if test="maintainer != null and maintainer != ''">tag.maintainer = '${maintainer}',</if>
            <if test="status != null">tag.status = ${status},</if>
            <if test="dimensionRange != null and dimensionRange != ''">tag.dimensionRange = '${dimensionRange}',</if>
            <if test="fillUnit != null and fillUnit != ''">tag.fillUnit = '${fillUnit}',</if>
            <if test="sourceDataDesc != null and sourceDataDesc != ''">tag.sourceDataDesc = '${sourceDataDesc}',</if>
            <if test="companyLevel != null and companyLevel != ''">tag.companyLevel = '${companyLevel}',</if>
            <if test="tagUnit != null and tagUnit != ''">tag.tagUnit = '${tagUnit}',</if>
            <if test="tagDepartment != null and tagDepartment != ''">tag.tagDepartment = '${tagDepartment}',</if>
            <if test="tagManagePost != null and tagManagePost != ''">tag.tagManagePost = '${tagManagePost}',</if>
            <if test="tagConsumeDepartment != null and tagConsumeDepartment != ''">tag.tagConsumeDepartment = '${tagConsumeDepartment}',</if>
            <if test="tagProcessLogic != null and tagProcessLogic != ''">tag.tagProcessLogic = '${tagProcessLogic}',</if>
            <if test="tagFillDepartment != null and tagFillDepartment != ''">tag.tagFillDepartment = '${tagFillDepartment}',</if>
            <if test="tagFillPost != null and tagFillPost != ''">tag.tagFillPost = '${tagFillPost}',</if>

            <if test="indicatorType != null and indicatorType != ''">tag.indicatorType = '${indicatorType}',</if>
            <if test="indicatorData != null and indicatorData != ''">tag.indicatorData = '${indicatorData}',</if>
            <if test="monitor != null and monitor != ''">tag.monitor = '${monitor}',</if>
            <if test="monitorPersonnel != null and monitorPersonnel != ''">tag.monitorPersonnel = '${monitorPersonnel}',</if>
            <if test="submissionTime != null ">tag.submissionTime = '${submissionTime}',</if>
            <if test="businessType != null and businessType != ''">tag.businessType = '${businessType}',</if>
            <if test="dataLength != null and dataLength != ''">tag.dataLength = '${dataLength}',</if>
            <if test="isEnum != null and isEnum != ''">tag.isEnum = '${isEnum}',</if>
            <if test="isSupport != null and isSupport != ''">tag.isSupport = '${isSupport}',</if>
            <if test="physicalTable != null and physicalTable != ''">tag.physicalTable = '${physicalTable}',</if>
            <if test="systemDescribe != null and systemDescribe != ''">tag.systemDescribe = '${systemDescribe}',</if>
            <if test="indicatorMethod != null and indicatorMethod != ''">tag.indicatorMethod = '${indicatorMethod}',</if>
            tag.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            tag.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(tag), properties(tag)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    
    <select id="selectById" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (tag:d_g_data_tag) WHERE tag.id = ${id} RETURN id(tag), properties(tag)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (tag:d_g_data_tag) WHERE tag.code = '${code}' RETURN id(tag), properties(tag)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (tag:d_g_data_tag) WHERE tag.code = '${code}' AND tag.id &lt;&gt; ${id} RETURN id(tag),
                               properties(tag)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (tag:d_g_data_tag)
        WHERE tag.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE tag RETURN id(tag),properties(tag) $$) as (id ag_catalog.agtype,properties
        ag_catalog.agtype)
    </select>

    <select id="batchRegister" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (tag:d_g_data_tag)
        WHERE tag.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET
            tag.registerStatus = 'REGISTER',
            tag.registerStatusLabel = '注册',
            tag.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            tag.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        RETURN id(tag), properties(tag)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="batchUnRegister" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (tag:d_g_data_tag)
        WHERE tag.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET
        tag.registerStatus = 'UNREGISTER',
        tag.registerStatusLabel = '未注册',
        tag.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        tag.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        RETURN id(tag), properties(tag)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="overview" resultType="com.datalink.fdop.govern.api.domain.DataTag">
        WITH overview as (
        SELECT tag.*, sort.code as securitySortCode, sort.name as securitySortName, level.code as securityLevelCode, level.name as securityLevelName, resouces.name as dataResourceName
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_tag)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
            RETURN node.id,node.pid, node.code, node.name, node.dataStatus,node.dataStatusLabel, node.dataResourceId, node.registerStatus, node.registerStatusLabel, node.governStatus, node.governStatusLabel, node.priority,node.priorityLabel, node.dataType, node.dataTypeLabel, node.tagAlias, node.scope, node.isInformation ,node.informationLabel,
                node.systemDataRetrieval, node.dataOwnerPost, node.dataOwner, node.dataManagePost, node.dataManage, node.dataStewardPost, node.dataSteward, node.itEngineerPost, node.itEngineer, node.dataEngineerPost, node.dataEngineer, node.dataDevelopPost, node.dataDeveloper,
                node.securitySortId, node.securityLevelId, node.range, node.threshold, node.synchronizationFrequency, node.tagFactor, node.businessCalculationFormula, node.tagFactorCalculationFormula, node.calculationUnit, node.calculationPrecision, node.dataPrecision, node.analysisMethod, node.statisticalCycle,
                node.analysisDimension, node.relatedBusinessObject, node.dataSource, node.valueField, node.dataEfficacy, node.maintainer, node.status, node.dimensionRange, node.fillUnit, node.sourceDataDesc, node.companyLevel, node.tagUnit, node.tagDepartment, node.tagManagePost, node.tagConsumeDepartment, node.tagProcessLogic,
                node.tagFillDepartment, node.tagFillPost, node.createBy, node.createTime, node.updateBy, node.updateTime,node.indicatorType,node.indicatorData,node.monitor,node.monitorPersonnel,node.submissionTime,node.businessType,node.dataLength,node.isEnum,node.isSupport,node.physicalTable,node.systemDescribe,node.indicatorMethod $$)
            as tag (id BIGINT, pid BIGINT, code TEXT, name TEXT, dataStatus TEXT, dataStatusLabel TEXT, dataResourceId BIGINT, registerStatus TEXT,
                registerStatusLabel TEXT, governStatus TEXT, governStatusLabel TEXT, priority TEXT, priorityLabel TEXT, dataType TEXT, dataTypeLabel TEXT, tagAlias TEXT, scope TEXT, isInformation TEXT, informationLabel TEXT,systemDataRetrieval TEXT, dataOwnerPost TEXT, dataOwner TEXT, dataManagePost TEXT,
                dataManage TEXT, dataStewardPost TEXT, dataSteward TEXT, itEngineerPost TEXT, itEngineer TEXT, dataEngineerPost TEXT, dataEngineer TEXT, dataDevelopPost TEXT, dataDeveloper TEXT,securitySortId BIGINT,securityLevelId BIGINT, range TEXT, threshold TEXT, synchronizationFrequency TEXT, tagFactor TEXT,
                businessCalculationFormula TEXT, tagFactorCalculationFormula TEXT, calculationUnit TEXT, calculationPrecision TEXT, dataPrecision TEXT, analysisMethod TEXT, statisticalCycle TEXT,analysisDimension TEXT, relatedBusinessObject TEXT, dataSource TEXT, valueField TEXT, dataEfficacy TEXT, maintainer TEXT,
                status BOOLEAN, dimensionRange TEXT, fillUnit TEXT, sourceDataDesc TEXT, companyLevel TEXT, tagUnit TEXT, tagDepartment TEXT, tagManagePost TEXT, tagConsumeDepartment TEXT, tagProcessLogic TEXT, tagFillDepartment TEXT, tagFillPost TEXT, createBy TEXT, createTime TEXT, updateBy TEXT, updateTime TEXT,
                indicatorType TEXT,indicatorData TEXT,monitor TEXT,monitorPersonnel TEXT,submissionTime TEXT,businessType TEXT,dataLength TEXT,isEnum TEXT,isSupport TEXT,physicalTable TEXT,systemDescribe TEXT,indicatorMethod TEXT
            )
            left join ag_catalog.cypher('zjdata_graph', $$ MATCH (sort:d_g_data_security_classification) RETURN sort.id,sort.code,sort.name $$) as sort (id BIGINT,code TEXT,name TEXT) on tag.securitySortId = sort.id
            left join ag_catalog.cypher('zjdata_graph', $$ MATCH (level:d_g_data_security_level) RETURN level.id,level.code,level.name $$) as level (id BIGINT,code TEXT,name TEXT) on tag.securityLevelId = level.id
            left join ag_catalog.cypher('zjdata_graph', $$ MATCH (resouces:d_g_data_resources) RETURN resouces.id,resouces.name $$) as resouces (id BIGINT,name TEXT) on tag.dataResourceId = resouces.id
        )
        select * from overview ORDER BY updateTime is null,updateTime ${sort}
    </select>

    <select id="getExportList" resultType="com.datalink.fdop.govern.api.domain.DataTagExport">
        WITH overview as (
        SELECT tag.*, sort.code as securitySortCode, sort.name as securitySortName, level.code as securityLevelCode, level.name as securityLevelName, resouces.name as dataResourceName
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_tag)
        <where>
            <if test="pid != -1">AND node.pid = ${pid}</if>
            <if test="searchVo != null ">
                AND (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
            </if>
        </where>
        RETURN node.id,node.pid, node.code, node.name, node.dataStatus,node.dataStatusLabel, node.dataResourceId, node.registerStatus, node.registerStatusLabel, node.governStatus, node.governStatusLabel, node.priority,node.priorityLabel, node.dataType, node.dataTypeLabel, node.tagAlias, node.scope, node.isInformation ,node.informationLabel,
        node.systemDataRetrieval, node.dataOwnerPost, node.dataOwner, node.dataManagePost, node.dataManage, node.dataStewardPost, node.dataSteward, node.itEngineerPost, node.itEngineer, node.dataEngineerPost, node.dataEngineer, node.dataDevelopPost, node.dataDeveloper,
        node.securitySortId, node.securityLevelId, node.range, node.threshold, node.synchronizationFrequency, node.tagFactor, node.businessCalculationFormula, node.tagFactorCalculationFormula, node.calculationUnit, node.calculationPrecision, node.dataPrecision, node.analysisMethod, node.statisticalCycle,
        node.analysisDimension, node.relatedBusinessObject, node.dataSource, node.valueField, node.dataEfficacy, node.maintainer, node.status, node.dimensionRange, node.fillUnit, node.sourceDataDesc, node.companyLevel, node.tagUnit, node.tagDepartment, node.tagManagePost, node.tagConsumeDepartment, node.tagProcessLogic,
        node.tagFillDepartment, node.tagFillPost, node.createBy, node.createTime, node.updateBy, node.updateTime,node.indicatorType,node.indicatorData,node.monitor,node.monitorPersonnel,node.submissionTime,node.businessType,node.dataLength,node.isEnum,node.isSupport,node.physicalTable,node.systemDescribe,node.indicatorMethod $$)
            as tag (id BIGINT, pid BIGINT, code TEXT, name TEXT, dataStatus TEXT, dataStatusLabel TEXT, dataResourceId BIGINT, registerStatus TEXT,
        registerStatusLabel TEXT, governStatus TEXT, governStatusLabel TEXT, priority TEXT, priorityLabel TEXT, dataType TEXT, dataTypeLabel TEXT, tagAlias TEXT, scope TEXT, isInformation TEXT, informationLabel TEXT,systemDataRetrieval TEXT, dataOwnerPost TEXT, dataOwner TEXT, dataManagePost TEXT,
        dataManage TEXT, dataStewardPost TEXT, dataSteward TEXT, itEngineerPost TEXT, itEngineer TEXT, dataEngineerPost TEXT, dataEngineer TEXT, dataDevelopPost TEXT, dataDeveloper TEXT,securitySortId BIGINT,securityLevelId BIGINT, range TEXT, threshold TEXT, synchronizationFrequency TEXT, tagFactor TEXT,
        businessCalculationFormula TEXT, tagFactorCalculationFormula TEXT, calculationUnit TEXT, calculationPrecision TEXT, dataPrecision TEXT, analysisMethod TEXT, statisticalCycle TEXT,analysisDimension TEXT, relatedBusinessObject TEXT, dataSource TEXT, valueField TEXT, dataEfficacy TEXT, maintainer TEXT,
        status BOOLEAN, dimensionRange TEXT, fillUnit TEXT, sourceDataDesc TEXT, companyLevel TEXT, tagUnit TEXT, tagDepartment TEXT, tagManagePost TEXT, tagConsumeDepartment TEXT, tagProcessLogic TEXT, tagFillDepartment TEXT, tagFillPost TEXT, createBy TEXT, createTime TEXT, updateBy TEXT, updateTime TEXT,
        indicatorType TEXT,indicatorData TEXT,monitor TEXT,monitorPersonnel TEXT,submissionTime TEXT,businessType TEXT,dataLength TEXT,isEnum TEXT,isSupport TEXT,physicalTable TEXT,systemDescribe TEXT,indicatorMethod TEXT
        )
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (sort:d_g_data_security_classification) RETURN sort.id,sort.code,sort.name $$) as sort (id BIGINT,code TEXT,name TEXT) on tag.securitySortId = sort.id
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (level:d_g_data_security_level) RETURN level.id,level.code,level.name $$) as level (id BIGINT,code TEXT,name TEXT) on tag.securityLevelId = level.id
        left join ag_catalog.cypher('zjdata_graph', $$ MATCH (resouces:d_g_data_resources) RETURN resouces.id,resouces.name $$) as resouces (id BIGINT,name TEXT) on tag.dataResourceId = resouces.id
        )
        select * from overview ORDER BY updateTime is null,updateTime ${sort}
    </select>

    <select id="selectList" parameterType="com.datalink.fdop.govern.api.domain.DataTag" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (tag:d_g_data_tag)
        <where>
            <if test="tag.id != null">AND tag.id = ${tag.id}</if>
            <if test="tag.code != null and tag.code != ''">AND tag.code =~'.*${tag.code}.*'
            </if>
            <if test="tag.name != null and tag.name != ''">AND tag.name =~'.*${tag.name}.*'
            </if>
            <if test="tag.description != null and tag.description != ''">AND tag.description
                =~'.*${tag.description}.*'
            </if>
            <if test="tag.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("tag",tag.searchCondition)})
            </if>
        </where>
        WITH tag
        ORDER BY tag.createTime DESC
        RETURN id(tag), properties(tag)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectListAll" parameterType="com.datalink.fdop.govern.api.domain.DataTag" resultType="com.datalink.fdop.common.core.domain.SelectVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (tag:d_g_data_tag)
        <where>
            <if test="tag.id != null">AND tag.id = ${tag.id}</if>
            <if test="tag.code != null and tag.code != ''">AND tag.code =~'.*${tag.code}.*'
            </if>
            <if test="tag.name != null and tag.name != ''">AND tag.name =~'.*${tag.name}.*'
            </if>
            <if test="tag.description != null and tag.description != ''">AND tag.description
                =~'.*${tag.description}.*'
            </if>
            <if test="tag.searchCondition != null ">AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("tag",tag.searchCondition)})
            </if>
        </where>
        WITH tag
        ORDER BY tag.createTime DESC
        RETURN  tag.code as key,
        tag.name as value
        $$) as (key TEXT,value TEXT)
    </select>

    <select id="selectTree" resultType="com.datalink.fdop.govern.api.domain.DataProcessFrameworkTree">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (tag:d_g_data_tag)
        <where>
            <if test="code != null and code != ''">tag.code =~'.*${code}.*'</if>
            <if test="name != null and name != ''">AND tag.name =~'.*${name}.*'</if>
        </where>
        RETURN tag.id, tag.pid, tag.code, tag.name, 'L3' as level, 'NODE' as menuType
        ORDER BY tag.code ${sort}
        $$) as (id BIGINT,pid BIGINT, code TEXT,name TEXT,level TEXT, menuType TEXT)
    </select>

</mapper>