<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.DataSourceSynchronizationMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.DataSourceSynchronization"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="vlabelIteField" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.drive.api.domain.dto.Field"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <resultMap id="vlabelItemQuality" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.quality.api.domain.DataQuality"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="vlabelItemDataSource" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.drive.api.domain.DataSource"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>

    <select id="delSyncDatabase" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH  (ss:d_g_synchronization_database)
        WHERE ss.dataSourceId=${dataSourceId} and
         ss.id  in [
        <foreach collection="databaseIdList" separator="," item="id" >
            ${id}
        </foreach>

        ]
        DETACH DELETE ss RETURN id(ss), properties(ss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delSyncSchema" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_schema)
            WHERE ss.dataSourceId=${dataSourceId}  and
          ss.id  in [
        <foreach collection="scheamIdList" separator="," item="id" >
            ${id}
        </foreach>

        ]

            DETACH DELETE ss RETURN id(ss), properties(ss)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delSyncTable" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_table)
            WHERE ss.dataSourceId=${dataSourceId}
            <if test="tableIdList != null and tableIdList.size() > 0">
                AND
            <foreach collection="tableIdList" item="tableIds" index="index" open="(" separator="OR" close=")">
                ss.id IN
                <foreach collection="tableIds" item="id" index="" open="[" separator="," close="]">
                    ${id}
                </foreach>
            </foreach>
            </if>

            DETACH DELETE ss RETURN id(ss), properties(ss)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delSyncView" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_view)
            WHERE ss.dataSourceId=${dataSourceId}
        <if test="viewIdList != null and viewIdList.size() > 0">
            AND
            <foreach collection="viewIdList" item="viewIds" index="index" open="(" separator="OR" close=")">
                ss.id IN
                <foreach collection="viewIds" item="id" index="" open="[" separator="," close="]">
                    ${id}
                </foreach>
            </foreach>
        </if>

            DETACH DELETE ss RETURN id(ss), properties(ss)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delSyncField" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_field)
            WHERE ss.dataSourceId=${dataSourceId}
            <if test="fieldIds != null and fieldIds.size() > 0">
                AND
                <foreach collection="fieldIds" item="fieldId" index="index" open="(" separator="OR" close=")">
                    ss.id IN
                    <foreach collection="fieldId" item="id" index="" open="[" separator="," close="]">
                        ${id}
                    </foreach>
                </foreach>
            </if>
            and ss.dataSourceId=${dataSourceId}
            DETACH DELETE ss RETURN id(ss), properties(ss)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="del" resultType="int" >
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH  (d:datasource)-[r:d_g_datasource_source_synchronization_edge]->(ss:d_g_data_source_synchronization)
        WHERE d.id   IN
        <foreach collection="dataSourceIds" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>

        DETACH DELETE ss RETURN id(ss), properties(ss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="updateById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (node:d_g_data_source_synchronization)
            WHERE node.id = ${id}
            <set>
                <if test="dataSourceId != null">node.dataSourceId = ${dataSourceId},</if>
                <if test="startTime != null">node.startTime = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime(startTime)}',</if>
                <if test="endTime != null">node.endTime = '${@com.datalink.fdop.common.core.utils.DateUtils@getTime(endTime)}',</if>
                <if test="timezoneId != null and timezoneId != ''">node.timezoneId = '${timezoneId}',</if>
                node.crontab = '${crontab}',
                <if test="enabled != null">node.enabled = ${enabled},</if>
                <if test="schemaJson != null and schemaJson != ''">node.schemaJson = '${schemaJson}',</if>
                <if test="type != null and type != ''">node.type = '${type}',</if>
            </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>
    <select id="create" resultType="java.lang.Integer" parameterType="com.datalink.fdop.govern.api.domain.DataSourceSynchronization">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_data_source_synchronization ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getDataSourceSynchronizationAgeStr(dataSourceSynchronization)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createAadDataSourceRelation" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (ss:d_g_data_source_synchronization), (d:datasource)
        WHERE ss.id = ${id}
        AND d.id = ${dataSourceId}
        CREATE (d)-[dss:d_g_datasource_source_synchronization_edge
        {startTable:'datasource',endTable:'d_g_data_source_synchronization'}]->(ss)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>
    <select id="getSyncByDataSourceId" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH  (d:datasource)-[r:d_g_datasource_source_synchronization_edge]->(ss:d_g_data_source_synchronization)
        WHERE d.id  =${dataSourceId}
        RETURN id(ss), properties(ss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectAllDataSource" resultMap="vlabelItemDataSource">
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:datasource)
        WHERE 1=1
        <if test="searchVo != null">
            AND
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSearchCondition("node",searchVo)})
        </if>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>
    <select id="createSyncDatabase" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_synchronization_database ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getSynchronizationDatabaseAgeStr(syncDatabase)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>
    <select id="createDataSourceSyncDatabase" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ss:d_g_synchronization_database), (d:datasource)
            WHERE ss.id = ${databaseId}
            AND d.id = ${dataSourceId}
            CREATE (d)-[dss:d_g_sync_datasource_database_edge
            {startTable:'datasource',endTable:'d_g_synchronization_database'}]->(ss)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncSchema" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_synchronization_schema ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getSynchronizationSchemaAgeStr(syncSchema)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncDatabaseSyncSchemaEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ss:d_g_synchronization_database), (d:d_g_synchronization_schema)
                                   WHERE ss.id = ${databaseId}
                                   AND d.id = ${schemaId}
                                   CREATE (ss)-[dss:d_g_sync_database_schema_edge
                                   {startTable:'d_g_synchronization_database',endTable:'d_g_synchronization_schema'}]->(d)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncTable" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_synchronization_table ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getSynchronizationTableAgeStr(syncTable)}) RETURN id(node),
        properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncSchemaSyncTableEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ss:d_g_synchronization_schema), (d:d_g_synchronization_table)
                                   WHERE ss.id = ${schemaId}
                                   AND d.id = ${tableId}
                                   CREATE (ss)-[dss:d_g_sync_schema_table_edge
                                   {startTable:'d_g_synchronization_schema',endTable:'d_g_synchronization_table'}]->(d)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncField" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_synchronization_field ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getSynchronizationFieldAgeStr(field)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncTableSyncFieldEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ss:d_g_synchronization_table), (d:d_g_synchronization_field)
                                   WHERE ss.id = ${tableId}
                                   AND d.id = ${fieldId}
                                   CREATE (ss)-[dss:d_g_sync_table_field_edge
                                   {startTable:'d_g_synchronization_table',endTable:'d_g_synchronization_field'}]->(d)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncView" resultType="java.lang.Integer">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (node:d_g_synchronization_view ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getSynchronizationViewAgeStr(syncView)}) RETURN id(node),
                               properties(node)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncSchemaSyncViewEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ss:d_g_synchronization_schema), (d:d_g_synchronization_view)
                                   WHERE ss.id = ${schemaId}
                                   AND d.id = ${viewId}
                                   CREATE (ss)-[dss:d_g_sync_schema_view_edge
                                   {startTable:'d_g_synchronization_schema',endTable:'d_g_synchronization_view'}]->(d)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createSyncViewSyncFieldEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ss:d_g_synchronization_view), (d:d_g_synchronization_field)
                                   WHERE ss.id = ${viewId}
                                   AND d.id = ${fieldId}
                                   CREATE (ss)-[dss:d_g_sync_view_field_edge
                                   {startTable:'d_g_synchronization_view',endTable:'d_g_synchronization_field'}]->(d)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createDataSourceSyncSchemaEdge" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ss:d_g_synchronization_schema), (d:datasource)
                                   WHERE ss.id = ${schemaId}
                                   AND d.id = ${dataSourceId}
                                   CREATE (d)-[dss:d_g_sync_datasource_schema_edge
                                   {startTable:'datasource',endTable:'d_g_synchronization_schema'}]->(ss)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="querySyncDatabase" resultType="com.datalink.fdop.govern.api.domain.SynchronizationDatabase">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_database)
            WHERE  ss.code=${databaseName}

            RETURN ss.id,ss.code,ss.name,ss.description,ss.property,ss.dataSourceId
                                   $$) as (id bigint,code text,name text,description text,property text,dataSourceId bigint)
    </select>
    <select id="getFieldByDataSourceId" resultMap="vlabelIteField">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ds:datasource)-[]->(d:d_g_synchronization_database)-[:d_g_sync_database_schema_edge]-(s:d_g_synchronization_schema)-[:d_g_sync_schema_table_edge]->(t:d_g_synchronization_table)-[:d_g_sync_table_field_edge]->(f:d_g_synchronization_field)
            where ds.id=${dataSourceId}
            RETURN id(f), properties(f)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (ds:datasource)-[]->(d:d_g_synchronization_database)-[:d_g_sync_database_schema_edge]-(s:d_g_synchronization_schema)-[]->(v:d_g_synchronization_view)-[]->(f:d_g_synchronization_field)
            where ds.id=${dataSourceId}
            RETURN id(f), properties(f)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ds:datasource)-[]->(s:d_g_synchronization_schema)-[:d_g_sync_schema_table_edge]->(t:d_g_synchronization_table)-[:d_g_sync_table_field_edge]->(f:d_g_synchronization_field)
            where ds.id=${dataSourceId}
            RETURN id(f), properties(f)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ds:datasource)-[]->(s:d_g_synchronization_schema)-[]->(v:d_g_synchronization_view)-[]->(f:d_g_synchronization_field)
            where ds.id=${dataSourceId}
            RETURN id(f), properties(f)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>
    <select id="createSyncFieldHistory" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:d_g_synchronization_field), (hd:d_g_synchronization_field)
                                   WHERE d.id = ${id}
                                   AND hd.id = ${hisId}
                                   CREATE (d)-[dss:d_g_sync_field_history_field_edge
                                   {startTable:'d_g_synchronization_field',endTable:'d_g_synchronization_field'}]->(hd)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="getDatabaseByDataSourceId" resultType="com.datalink.fdop.govern.api.model.vo.DatabaseVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (d:datasource)-[r:d_g_sync_datasource_database_edge]->(ss:d_g_synchronization_database)
            WHERE  d.id=${id}

            RETURN ss.id,ss.code as databaseName,ss.description,ss.property
            ORDER BY ss.code ${sort}
            $$) as (id bigint,databaseName text,description text,property text)

    </select>
    <select id="getSchemaByDataSourceId" resultType="com.datalink.fdop.govern.api.model.vo.SchemaVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (d:datasource)-[r:d_g_sync_datasource_schema_edge]->(ss:d_g_synchronization_schema)
            WHERE  d.id=${id}

            RETURN ss.id,ss.code as  schemaName,ss.description,ss.property
            ORDER BY ss.code ${sort}
                                   $$) as (id bigint,schemaName text,description text,property text)
    </select>
    <select id="getSchemaByDatabaseId" resultType="com.datalink.fdop.govern.api.model.vo.SchemaVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (d:d_g_synchronization_database)-[r:d_g_sync_database_schema_edge]->(ss:d_g_synchronization_schema)
            WHERE  d.id=${id}

            RETURN ss.id,ss.code as schemaName,ss.description,ss.property
            ORDER BY ss.code ${sort}
                                   $$) as (id bigint,schemaName text,description text,property text)
    </select>
    <select id="getTableBySchemaId" resultType="com.datalink.fdop.govern.api.model.vo.TableVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (d:d_g_synchronization_schema)-[r:d_g_sync_schema_table_edge]->(ss:d_g_synchronization_table)
            WHERE  d.id=${id}
            <if test="tableName !=null and tableName !=''">
                and ss.code=~'.*${tableName}.*'
            </if>

            RETURN ss.id,ss.code as tableName
             ORDER BY ss.code ${sort}
                                   $$) as (id bigint,tableName text)
    </select>
    <select id="getViewBySchemaId" resultType="com.datalink.fdop.govern.api.model.vo.ViewVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (d:d_g_synchronization_schema)-[r:d_g_sync_schema_view_edge]->(ss:d_g_synchronization_view)
            WHERE  d.id=${id}
                <if test="tableName !=null and tableName !=''">
                    and ss.code=~'.*${tableName}.*'
                </if>
            RETURN ss.id,ss.code as viewName
        ORDER BY ss.code ${sort}
                                   $$) as (id bigint,viewName  text)
    </select>
    <select id="getFieldByTableId" resultType="com.datalink.fdop.drive.api.domain.dto.Field">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (t:d_g_synchronization_table)-[r:d_g_sync_table_field_edge]-(f:d_g_synchronization_field)
                                   WHERE t.id = ${id}
        <include refid="selectField">
            <property name="alias" value="f"/>
        </include>
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (v:d_g_synchronization_view)-[r:d_g_sync_view_field_edge]-(f:d_g_synchronization_field)
            WHERE v.id = ${id}
        <include refid="selectField">
            <property name="alias" value="f"/>
        </include>
    </select>


    <select id="getFieldByFieldId" resultType="com.datalink.fdop.drive.api.domain.dto.Field">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (f:d_g_synchronization_field)
            WHERE f.id = ${id}
        <include refid="selectField">
            <property name="alias" value="f"/>
        </include>
    </select>
    <select id="createFieldStandardEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:d_g_synchronization_field), (s:d_g_data_standard)
                                   WHERE d.id = ${fieldId}
                                   AND s.id = ${standardId}
                                   CREATE (d)-[dss:d_g_sync_field_standard_edge
                                   {startTable:'d_g_synchronization_field',endTable:'d_g_data_standard'}]->(s)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createFieldQualityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:d_g_synchronization_field), (s:d_q_data_quality)
                                   WHERE d.id = ${fieldId}
                                   AND s.id = ${qualityId}
                                   CREATE (d)-[dss:d_g_sync_field_quality_edge
                                   {startTable:'d_g_synchronization_field',endTable:'d_q_data_quality'}]->(s)
        RETURN id(dss), properties(dss)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delFieldStandardEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:d_g_synchronization_field)-[r:d_g_sync_field_standard_edge]->()
            WHERE d.id = ${fieldId}
            DELETE r RETURN id(r), properties(r)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delFieldQualityEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:d_g_synchronization_field)-[r:d_g_sync_field_quality_edge]->()
            WHERE d.id = ${fieldId}
            DELETE r RETURN id(r), properties(r)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="selectAll" resultType="com.datalink.fdop.govern.api.model.vo.DataSourceInfoShowVo">
        WITH zjdata_graph_table as (
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (node:datasource)
            RETURN node.id,node.code,node.name,'数据源' as type,'' as metadata
                                   $$) as (id bigint,code text,name text,type text,metadata text)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH  (node:d_g_synchronization_database)
        RETURN node.id,node.code,node.name,'库' as type,node.metadata
        $$) as (id bigint,code text,name text,type text,metadata text)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH  (node:d_g_synchronization_schema)
        RETURN node.id,node.code,node.name,'模式' as type,node.metadata
        $$) as (id bigint,code text,name text,type text,metadata text)
        union

        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH  (node:d_g_synchronization_table)
        RETURN node.id,node.code,node.name,'表' as type,node.metadata
        $$) as (id bigint,code text,name text,type text,metadata text)
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH  (node:d_g_synchronization_view)
        RETURN node.id,node.code,node.name,'视图' as type,node.metadata
        $$) as (id bigint,code text,name text,type text,metadata text)
        ) select * from zjdata_graph_table  where 1=1
        <if test="searchCondition != null" >
            and
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("zjdata_graph_table",searchCondition)})
        </if>
    </select>
    <select id="getQualityByField" resultMap="vlabelItemQuality">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (d:d_g_synchronization_field)-[r:d_g_sync_field_quality_edge]->(s:d_q_data_quality)
            WHERE d.id = ${fieldId}
            RETURN id(s), properties(s)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="getMetadata" resultType="java.lang.String">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (metadata:${table})-[r]->(node)
            WHERE node.id = ${id}
            RETURN metadata.code
            $$) as (code text)
        <if test="table2 !=null and table2!=''">
            union
            SELECT *
            FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (metadata:${table2})-[r]->(node)
            WHERE node.id = ${id}
            RETURN metadata.code
            $$) as (code text)
        </if>
    </select>

    <sql id="selectField">
        RETURN ${alias}.id, ${alias}.isPk,
            ${alias}.isNull,
            ${alias}.length,
            ${alias}.metadata,
            ${alias}.viewName,
            ${alias}.fieldDesc,
            ${alias}.fieldName,
            ${alias}.fieldType,
            ${alias}.tableName,
            ${alias}.dataSourceId,
            ${alias}.fieldDefault,
            ${alias}.baseFieldType,
            ${alias}.decimalLength,
            ${alias}.flinkFieldType,
            ${alias}.hisField
            $$) as (id BIGINT, isPk BOOLEAN,
            "isNull" BOOLEAN,
            length BIGINT,
            metadata TEXT,
            viewName TEXT,
            fieldDesc TEXT,
            fieldName TEXT,
            fieldType TEXT,
            tableName TEXT,
            dataSourceId BIGINT,
            fieldDefault TEXT,
            baseFieldType TEXT,
            decimalLength BIGINT,
            flinkFieldType TEXT,
            hisField TEXT)
    </sql>

    <select id="selectFieldById" resultType="com.datalink.fdop.drive.api.domain.dto.Field">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (d:d_g_synchronization_field) WHERE d.id = ${fId}
        <include refid="selectField">
            <property name="alias" value="d"/>
        </include>
    </select>

    <select id="selectMonitorTableInfoList" resultType="com.datalink.fdop.govern.api.domain.SynchronizationTable">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality_monitor) -[edge:d_q_data_quality_monitor_table_edge]->(table :d_g_synchronization_table)
            WHERE quality.id = ${monitorId}
            RETURN table.id, table.code
                                   $$) as (id BIGINT,code TEXT)
    </select>

    <select id="selectMonitorViewInfoList" resultType="com.datalink.fdop.govern.api.domain.SynchronizationView">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (quality:d_q_data_quality_monitor) -[edge:d_q_data_quality_monitor_view_edge]->(view :d_g_synchronization_view)
            WHERE quality.id = ${monitorId}
            RETURN view.id, view.code
                                   $$) as (id BIGINT,code TEXT)
    </select>
    <select id="findDatabaseByCode" resultType="com.datalink.fdop.govern.api.model.vo.DatabaseVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_database)
            WHERE  ss.code='${databaseName}' and ss.dataSourceId=${dataSourceId}
            RETURN ss.id,ss.code as databaseName,ss.description,ss.property
                                   $$) as (id bigint,databaseName text,description text,property text)
    </select>
    <select id="findSchemaByCode" resultType="com.datalink.fdop.govern.api.domain.SynchronizationSchema">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_schema)
            WHERE  ss.code='${schemaName}' and ss.dataSourceId=${dataSourceId}

            RETURN ss.id,ss.code,ss.name,ss.description,ss.property,ss.dataSourceId
                                   $$) as (id bigint,code text,name text,description text,property text,dataSourceId bigint)
    </select>
    <select id="findTableByCode" resultType="com.datalink.fdop.govern.api.domain.SynchronizationTable">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (s:d_g_synchronization_schema)-[r:d_g_sync_schema_table_edge]->(table :d_g_synchronization_table)
            WHERE table.code ='${tableName}' and s.id=${schemaId}
            RETURN table.id, table.code,table.name,table.description,table.dataSourceId
                                   $$) as (id BIGINT,code TEXT,name text,description text,dataSourceId bigint)
    </select>
    <select id="findViewByCode" resultType="com.datalink.fdop.govern.api.domain.SynchronizationView">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (s:d_g_synchronization_schema)-[r:d_g_sync_schema_view_edge]->(view :d_g_synchronization_view)
            WHERE view.code ='${viewName}' and s.id=${schemaId}
            RETURN view.id, view.code,view.name,view.description,view.dataSourceId
                                   $$) as (id BIGINT,code TEXT,name text,description text,dataSourceId bigint)
    </select>
    <select id="getFieldByName" resultType="com.datalink.fdop.drive.api.domain.dto.Field">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (t:d_g_synchronization_table)-[r:d_g_sync_table_field_edge]-(f:d_g_synchronization_field)
        WHERE t.id = ${id} and f.fieldName ='${fieldName}'
        <include refid="selectField">
            <property name="alias" value="f"/>
        </include>
        union
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (v:d_g_synchronization_view)-[r:d_g_sync_view_field_edge]-(f:d_g_synchronization_field)
        WHERE v.id = ${id} and f.fieldName ='${fieldName}'
        <include refid="selectField">
            <property name="alias" value="f"/>
        </include>
    </select>


    <select id="findDatabaseByDataSourceId" resultType="com.datalink.fdop.govern.api.model.vo.DatabaseVo">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_database)
            WHERE  ss.dataSourceId= ${dataSourceId}
            RETURN ss.id,ss.code as databaseName,ss.description,ss.property
                                   $$) as (id bigint,databaseName text,description text,property text)
    </select>
    <select id="findSchemaByDataSourceId" resultType="com.datalink.fdop.govern.api.domain.SynchronizationSchema">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (ss:d_g_synchronization_schema)
            WHERE  ss.dataSourceId= ${dataSourceId}

            RETURN ss.id,ss.code,ss.name,ss.description,ss.property,ss.dataSourceId
                                   $$) as (id bigint,code text,name text,description text,property text,dataSourceId bigint)
    </select>
    <select id="findTableByDataSourceId" resultType="com.datalink.fdop.govern.api.domain.SynchronizationTable">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (table :d_g_synchronization_table)
            WHERE table.dataSourceId = ${dataSourceId}
            RETURN table.id, table.code,table.name,table.description,table.dataSourceId
                                   $$) as (id BIGINT,code TEXT,name text,description text,dataSourceId bigint)
    </select>
    <select id="findViewByDataSourceId" resultType="com.datalink.fdop.govern.api.domain.SynchronizationView">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH  (view :d_g_synchronization_view)
            WHERE view.dataSourceId = ${dataSourceId}
            RETURN view.id, view.code,view.name,view.description,view.dataSourceId
                                   $$) as (id BIGINT,code TEXT,name text,description text,dataSourceId bigint)
    </select>
    <select id="findFieldByDataSourceId" resultType="com.datalink.fdop.drive.api.domain.dto.Field">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (d:d_g_synchronization_field) WHERE d.dataSourceId = ${dataSourceId}
        <include refid="selectField">
            <property name="alias" value="d"/>
        </include>
    </select>


    <select id="updateField" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_synchronization_field)
        WHERE node.id = ${id}
        <set>
            <if test="dataSourceId != null ">node.dataSourceId = ${dataSourceId},</if>
            <if test="fieldName != null and fieldName != ''">node.fieldName = '${fieldName}',</if>
            <if test="fieldType != null">node.fieldType = '${fieldType}',</if>
            <if test="baseFieldType != null ">node.baseFieldType = '${baseFieldType}',</if>
            <if test="length != null ">node.length = ${length},</if>
            <if test="decimalLength != null ">node.decimalLength = ${decimalLength},</if>
            <if test="isPk != null ">node.isPk = ${isPk},</if>
            <if test="isNull != null ">node.isNull = ${isNull},</if>
            <if test="fieldDesc != null and fieldDesc != ''">node.fieldDesc = '${fieldDesc}',</if>
            <if test="fieldDefault != null and fieldDefault != ''">node.fieldDefault = '${fieldDefault}',</if>
            <if test="tableName != null and tableName != ''">node.tableName = '${tableName}',</if>
            <if test="viewName != null and viewName != ''">node.viewName = '${viewName}',</if>
            <if test="hisField != null and hisField != ''">node.hisField = '${hisField}',</if>
            node.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            node.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

</mapper>