<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.govern.mapper.MetadataMenuMapper">

    <resultMap id="vlabelItem" type="com.datalink.fdop.common.mybatis.model.VlabelItem">
        <id column="id" property="id"/>
        <result column="properties" property="properties" javaType="com.datalink.fdop.govern.api.domain.MetadataMenu"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>



    <resultMap id="elabelItem" type="com.datalink.fdop.common.mybatis.model.ElabelItem">
        <id column="id" property="id"/>
        <id column="start_id" property="startId"/>
        <id column="end_id" property="endId"/>
        <result column="properties" property="properties" javaType="java.util.Map"
                typeHandler="com.datalink.fdop.common.mybatis.handler.JsonTypeHandler"/>
    </resultMap>
    <select id="delNode"   resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_g_data_metadata_menu)-[r:d_g_metadata_menu_edge]-(node)
            WHERE node.id=${nodeId}
            DELETE r RETURN id(r),properties(r) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="delMenuNode" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu)-[r:d_g_metadata_menu_edge]-(node)
        WHERE menu.id =${menuId} and node.id=${nodeId} and r.code='${code}'
        DELETE r RETURN id(r),properties(r) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>

    <select id="selectById"  resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_g_data_metadata_menu) WHERE menu.id = ${id}
            RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="selectByCode"  resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_g_data_metadata_menu) WHERE menu.code = '${code}'
            RETURN id(menu), properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>


    <select id="selectIdsByPid" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menuS:d_g_data_metadata_menu) -[nme:menu_menu_edge]->(nodeE:d_g_data_metadata_menu)
            WHERE nodeE.pid = ${pid}
            RETURN DISTINCT (nodeE.id)
            $$) as (id ag_catalog.agtype)
    </select>


    <select id="bacthUpdatePidById" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (node:d_g_data_metadata_menu)
        WHERE node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        SET node.pid = ${pid}
        RETURN id(node), properties(node)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createMetadataMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu), (node:d_g_data_metadata_menu)
        WHERE menu.id = ${pid}
        AND node.pid = ${pid} AND node.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        CREATE (menu)-[nme:menu_menu_edge
        {startTable:'d_g_data_metadata_menu',endTable:'d_g_data_metadata_menu'}]->(node)-[:menu_menu_edge
        {startTable:'d_g_data_metadata_menu',endTable:'d_g_data_metadata_menu'}]->(menu)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="checkCodeIsExists" resultMap="vlabelItem">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
            MATCH (menu:d_g_data_metadata_menu) WHERE menu.code = '${code}' AND menu.id &lt;&gt; ${id} RETURN id(menu),
                               properties(menu) LIMIT 1
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="updateById" parameterType="com.datalink.fdop.govern.api.domain.MetadataMenu" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu)
        WHERE menu.id = ${id}
        <set>
            <if test="pid != null">menu.pid = ${pid},</if>
            <if test="code != null and code != ''">menu.code = '${code}',</if>
            <if test="name != null and name != ''">menu.name = '${name}',</if>
            <if test="description != null and description != ''">menu.description = '${description}',</if>
            menu.updateBy = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
            menu.updateTime ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        </set>
        RETURN id(menu), properties(menu)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="insertMetadataMenu" parameterType="com.datalink.fdop.govern.api.domain.MetadataMenu" resultType="int">
        select COUNT(1)
        from ag_catalog.cypher('zjdata_graph',
                               $$ CREATE (menu:d_g_data_metadata_menu ${@com.datalink.fdop.govern.utils.DomainAgeUtils@getMetadataMenuAgeStr(metadataMenu)}) RETURN id(menu),
                               properties(menu)
                                   $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="deleteBatchIds" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu)
        WHERE menu.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        DETACH DELETE menu RETURN id(menu),properties(menu) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>

    <select id="deleteMetadataMenuEdge" resultType="int">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS) -[mme1:menu_menu_edge]->(menu)-[mme2:menu_menu_edge]->(menuS)
        WHERE menuS.id IN
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        AND menu.id = ${pid}
        DELETE mme1, mme2 RETURN id(mme1),
        properties(mme1) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
    </select>
    <select id="createMenuNode" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu), (node:${table})
        WHERE menu.id = ${menuId}
        AND node.id = ${nodeId}
        CREATE (menu)-[nme:d_g_metadata_menu_edge
        {startTable:'d_g_data_metadata_menu',endTable:'${table}',code:'${code}',pid:menu.id,menuType:'${menuType}',serial:${serial}}]->(node)
        RETURN id(nme), properties(nme)
        $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)

    </select>
    <select id="selectNodeTree" resultType="com.datalink.fdop.govern.api.domain.MetadataTree">
        WITH zjdata_graph_table as (
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu) -[r]->(node)
        <where>
            <if test="code != null and code != ''">node.code =~'.*${code}.*'</if>
        </where>
        RETURN node.id, r.code as code, node.name, node.description, r.menuType,menu.code as menuName,r.serial,r.pid
        $$) as (id ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType
        TEXT,menuName TEXT,serial ag_catalog.agtype,pid ag_catalog.agtype)
        ) select * from zjdata_graph_table  where 1=1
        ORDER BY zjdata_graph_table.serial ${sort}
    </select>
    <select id="selectMenuTree" resultType="com.datalink.fdop.govern.api.domain.MetadataTree">
        SELECT *
        FROM (
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu) WHERE menu.pid = -1
        WITH DISTINCT (menu)
        RETURN menu.id, menu.pid, menu.code, menu.name, menu.description, 'MENU' as menuType
        $$) as (id ag_catalog.agtype,pid ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_g_data_metadata_menu) -[]->(menuE:d_g_data_metadata_menu)
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType
        $$) as (id ag_catalog.agtype, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT)
        UNION
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_g_data_metadata_menu) -[]->(nodeE)
        <where>
            <if test="code != null and code != ''">nodeE.code =~'.*${code}.*'</if>
        </where>
        WITH DISTINCT (menuS)
        RETURN menuS.id, menuS.pid, menuS.code, menuS.name, menuS.description, 'MENU' as menuType
        $$) as (id ag_catalog.agtype, pid ag_catalog.agtype, code TEXT, name TEXT, description TEXT, menuType TEXT)
        ) as zjdata_graph_table
        ORDER BY zjdata_graph_table.code ${sort}
    </select>

    <select id="selectByPids" resultType="Long">
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu) -[r:d_g_metadata_menu_edge]->(node)
        WHERE menu.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN DISTINCT (menu.id)
        $$) as (id ag_catalog.agtype)
        UNION
        SELECT *
        FROM ag_catalog.cypher('zjdata_graph', $$
        MATCH (menuS:d_g_data_metadata_menu) -[mme:menu_menu_edge]->(menuE:d_g_data_metadata_menu)
        WHERE menuE.pid in
        <foreach collection="ids" item="id" open="[" separator="," close="]">
            ${id}
        </foreach>
        RETURN DISTINCT (menuE.id)
        $$) as (id ag_catalog.agtype)
    </select>
    <select id="selectAll" resultType="com.datalink.fdop.govern.api.model.vo.MetadataOverviewVo">
        WITH zjdata_graph_table as (
        SELECT *
        FROM
        ag_catalog.cypher('zjdata_graph', $$
        MATCH (menu:d_g_data_metadata_menu) -[r]->(node)
        <if test="menuIds != null and menuIds.size()!=0">WHERE  menu.id in [
            <foreach collection="menuIds" separator="," item="id">
                ${id}
            </foreach>
        ]</if>
        RETURN node.id, r.code, node.name, node.description, r.menuType,menu.code as menuName,menu.id as menuId,r.serial
        $$) as (id ag_catalog.agtype,code TEXT,name TEXT,description TEXT,menuType
        TEXT,menuName TEXT,menuId ag_catalog.agtype,serial ag_catalog.agtype)
        ) select * from zjdata_graph_table  where 1=1
        <if test="searchVo != null" >
            and
            (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("zjdata_graph_table",searchVo)})
        </if>
        ORDER BY zjdata_graph_table.serial ${sort}
    </select>
</mapper>