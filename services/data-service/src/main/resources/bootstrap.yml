nacos:
  server:
    addr: @nacos_server@
    group: @nacos_server_group@
  config:
    addr: @nacos_server@
    group: @nacos_config_group@

# Tomcat
server:
  port: 9529

# Spring
spring:
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  application:
    # 应用名称
    name: data-service
  profiles:
  # 环境配置
    active: @profiles.active@
  cloud:
    nacos:
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      discovery:
        # 服务注册地址
        server-addr: ${nacos.server.addr}
        # 服务分组
        group: ${nacos.server.group}
      config:
        # 配置组
        group: ${nacos.config.group}
        # 配置中心地址
        server-addr: ${nacos.server.addr}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.config.group}
          - data-id: application-db.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.config.group}
          - data-id: application-redis.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.config.group}
          - data-id: application-flink.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.config.group}
          - data-id: application-stream.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.config.group}

plugin:
  mainPackage: com.datalink.fdop.drive
  runMode: prod
  uploadTempPath: ~/plugins/drive-center/tmp
  pluginPath: ~/plugins/drive-center/2.0.0

#JimuReport[minidao配置]
minidao :
  base-package: org.jeecg.modules.jmreport.desreport.dao*
  db-type: postgresql
#JimuReport[上传配置]
jeecg :
  jmreport:
    # 自动保存
    autoSave: true
    # 单位毫秒 默认5*60*1000
    interval: 10000
  # local|minio|alioss
  uploadType: local
  # local
  path :
    #文件路径
    upload: D:\images
  # alioss
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessKey:
    secretKey:
    staticDomain:
    bucketName:
  # minio
  minio:
    minio_url: http://minio.jeecg.com
    minio_name:
    minio_pass:
    bucketName:

#kubernetes:
#  address: https://*********:6443
#  token: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

kafka:
  bootstrap_servers: *********:9092

debezium:
  image: debezium/connect

k8s:
  url: http://*********:8083

#输出sql日志
logging:
  level:
    org.jeecg.modules.jmreport : info

# swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 标题
  title: '数据管理模块接口文档'
  # 描述
  description: '数据管理模块接口文档'
  # 版本
  version: @docker.imagesName.version@
