/opt/soft/flink/bin/flink run-application \
--target kubernetes-application \
-c org.java.model.FlinkSqlK8s \
-Dkubernetes.namespace=flink \
-Dkubernetes.service-account=flink \
-Dkubernetes.cluster-id=$taskName \
-Dkubernetes.rest-service.exposed.type=NodePort \
-Dkubernetes.jobmanager.cpu=1 \
-Dkubernetes.taskmanager.cpu=1 \
-Dkubernetes.rest-service.exposed.node-port-address-type=InternalIP \
-Dtaskmanager.numberOfTaskSlots=$slot \
-Dparallelism.default=$parallelism \
-Djobmanager.memory.process.size=$jobManagerMemory \
-Dtaskmanager.memory.process.size=$taskManagerMemory \
-Dclassloader.resolve-order=parent-first \
-Dheartbeat.timeout=180000 \
-Dkubernetes.container.image.pull-policy=Always \
-Dkubernetes.container.image=harbor.semi-tech.com/fsp/fdop/flink-k8s:1.14.4-scala_2.11 \
-Dkubernetes.pod-template-file=/opt/flink-pod-template.yaml \
local:///opt/flink-1.0-SNAPSHOT.jar \
-schema $taskName \
-sql $sql \
-runtimeMode $runtimeMode \