package com.datalink.fdop.test;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sap.conn.jco.*;
import com.sap.conn.jco.ext.Environment;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

public class SapRfcDemo {

    public static void main(String[] args) {
        Map<String, Properties> destMap = new HashMap<>();
        Properties logonProperties = new Properties();
        logonProperties.put("as-host", "**************"); //系统的IP地址
        logonProperties.put("client", "300");           //要登录的客户端
        logonProperties.put("r3name", "SDX");
        logonProperties.put("language", "ZH");
        logonProperties.put("sysnr", "10");             //系统编号
        logonProperties.put("user", "0487");         //登录用户名
        logonProperties.put("password", "dwms2025");      //用户登录口令
        destMap.put("ABAP_AS_POOLED ", logonProperties);
        // 注册
        Environment.registerDestinationDataProvider(new JCODestinationDataProvider(destMap));

        try {
            JCoDestination destination = JCoDestinationManager.getDestination("ABAP_AS_POOLED ");
            //destination.ping();
            System.out.println("已成功建立sap的连接");
            String funName = "RFC_READ_TABLE";
            // 获取RFC函数
            JCoFunction function = destination.getRepository().getFunction(funName);
            // 获取传入参数接口，具体赋值方法变化较多可以参考JCO包中提供的官方API文档
            JCoParameterList pImlist = function.getImportParameterList();
            JCoParameterList rEmlist = function.getExportParameterList();
            JCoParameterList pTmlist = function.getTableParameterList();
            JCoParameterList pCmlist = function.getChangingParameterList();
            JCoParameterFieldIterator importParameterFieldIterator = pImlist.getParameterFieldIterator();
            while (importParameterFieldIterator.hasNextField()) {
                JCoField field = importParameterFieldIterator.nextField();
                System.out.println("执行完毕，传入参数[" + field.getName() + "]为：" + field.getValue());
            }
            pImlist.setValue("QUERY_TABLE", "T001");
            pImlist.setValue("DELIMITER", "|");
            //pImlist.setValue("ROWCOUNT", "2");
            // 调用RFC函数
            function.execute(destination);
            JCoParameterFieldIterator tableParameterFieldIterator = pTmlist.getParameterFieldIterator();
            JCoTable t001 = pTmlist.getTable("DATA");
            JCoTable fieldTable = pTmlist.getTable("FIELDS");
            // FIELDNAME  OFFSET  LENGTH  TYPE FIELDTEXT
            JSONArray data = new JSONArray();
            System.out.println("数据量：" + t001.getNumRows());
            // 解析rfc字段列
            List<RfcField> rfcFields = new ArrayList<RfcField>();
            // 解析rfc字段值
            do {
                rfcFields.add(new RfcField(fieldTable.getString("FIELDNAME"), fieldTable.getInt("OFFSET"),
                        fieldTable.getInt("LENGTH"), fieldTable.getString("FIELDTEXT")));
            } while (fieldTable.nextRow());
            if (t001.getNumRows() > 0) {
                do {
                    JSONObject row = new JSONObject();
                    JCoFieldIterator fieldIterator = t001.getFieldIterator();
                    while (fieldIterator.hasNextField()) {
                        JCoField field = fieldIterator.nextField();
                        //String[] rowSplitArray = field.getString().split("\\|");
                        System.out.println("长度：" + field.getString().length());
                        String value = field.getString();
                        for (int i = 0; i < rfcFields.size(); i++) {
                            // 计算结束索引
                            int end = Math.min(rfcFields.get(i).getOffset() + rfcFields.get(i).getLength(), value.length());
                            // 截取数据位置
                            row.put(rfcFields.get(i).getFieldName(), value.substring(rfcFields.get(i).getOffset(), end));
                        }
                        data.add(row);
                    }
                    //System.out.println("------------------------------------------------------------");
                } while (t001.nextRow());
            }
            System.out.println("[retData Length]：" + data.size());
            System.out.println("[retData]：" + data.toJSONString());
            while (tableParameterFieldIterator.hasNextField()) {
                JCoField field = tableParameterFieldIterator.nextField();
                System.out.println("执行完毕，表输出参数[" + field.getName() + "]");
                //System.out.println("执行完毕，输出参数[" + field.getName() + "]为：" + field.getValue());
            }
//            System.out.println("执行完毕，传入参数[import]为：" + JSON.toString(pImlist));
//            System.out.println("执行完毕，输入参数[export]为：" + JSON.toString(rEmlist));
//            System.out.println("执行完毕，输入参数[table]为：" + JSON.toString(pTmlist));
//            System.out.println("执行完毕，输入参数[changing]为：" + JSON.toString(pCmlist));
            // 调用RFC函数
            //function.execute(destination);
            // 获取返回结果接口，具体取值方法变化较多可以参考JCO包中提供的官方API文档
            //JCoParameterList rEmlist = function.getExportParameterList();
            //JCoParameterList rTmlist = function.getTableParameterList();
            //JCoParameterList rCmlist = function.getChangingParameterList();
        } catch (JCoException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建SAP接口属性文件。
     *
     * @param name       ABAP管道名称
     * @param suffix     属性文件后缀
     * @param properties 属性文件内容
     */
    private static void createDataFile(String name, String suffix, Properties properties) {
        File cfg = new File(name + "." + suffix);
        if (cfg.exists()) {
            cfg.deleteOnExit();
        }
        try {
            FileOutputStream fos = new FileOutputStream(cfg, false);
            properties.store(fos, "for tests only !");
            fos.close();
        } catch (Exception e) {
            System.out.println("Create Data file fault, error msg: " + e.toString());
            throw new RuntimeException("Unable to create the destination file " + cfg.getName(), e);
        }
    }


}
