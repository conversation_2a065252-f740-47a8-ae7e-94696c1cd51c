package com.datalink.fdop.test;

/**
 *  Rfc属性列描述
 */
public class RfcField {

    private String fieldName;

    private int offset;

    private int length;

    private String fieldDesc;

    public RfcField() {
    }

    public RfcField(String fieldName, int offset, int length, String fieldDesc) {
        this.fieldName = fieldName;
        this.offset = offset;
        this.length = length;
        this.fieldDesc = fieldDesc;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public String getFieldDesc() {
        return fieldDesc;
    }

    public void setFieldDesc(String fieldDesc) {
        this.fieldDesc = fieldDesc;
    }
}
