package com.datalink.fdop.test;

import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultRedirectStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class DorisStreamLoadDemo {

    private final static String DORIS_HOST = "*************";

    /**
     * Doris目标数据库
     */
    private final static String DORIS_DB = "ods_eda";
    /**
     * Doris目标表
     */
    private final static String DORIS_TABLE = "cp_aoi_classification_doris";
    private final static String DORIS_USER = "root";
    private final static String DORIS_PASSWORD = "";
    private final static int DORIS_HTTP_PORT = 31083;

    public static void main(String[] args) {
        System.setProperty("java.net.preferIPv4Stack" , "true");
        String jdbcUrl = "******************************************";
        String username = "postgres";
        String password = "123456";
        try (Connection conn = DriverManager.getConnection(jdbcUrl, username, password)) {
            String sql = "SELECT * FROM public.cp_aoi_classification_postgresql";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(sql)) {
                List<JSONObject> jsonList = new ArrayList<>();
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                while (rs.next()) {
                    JSONObject obj = new JSONObject();
                    for (int i = 1; i <= columnCount; i++) {
                        obj.put(metaData.getColumnName(i), rs.getObject(i));
                    }
                    jsonList.add(obj);
                }
                JSONArray jsonArray = new JSONArray(jsonList);
                String jsonData = jsonArray.toString();
                sendData(jsonData);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * Sink data to Doris
     *
     * @param content
     * @throws Exception
     */
    private static void sendData(String content) throws Exception {
        // Stream Load 更多参数，参考 https://doris.apache.org/zh-CN/docs/3.0/data-operate/import/import-way/stream-load-manual#load-configuration-parameters
        final String loadUrl = String.format("http://%s:%s/api/%s/%s/_stream_load",
                DORIS_HOST,
                DORIS_HTTP_PORT,
                DORIS_DB,
                DORIS_TABLE);

        final HttpClientBuilder httpClientBuilder = HttpClients
                .custom()
                .setRedirectStrategy(new DefaultRedirectStrategy() {
                    @Override
                    protected boolean isRedirectable(String method) {
                        return true;
                    }
                });

        try (CloseableHttpClient client = httpClientBuilder.build()) {
            HttpPut put = new HttpPut(loadUrl);
            StringEntity entity = new StringEntity(content, "UTF-8");
            put.setHeader(HttpHeaders.EXPECT, "100-continue");
            put.setHeader(HttpHeaders.AUTHORIZATION, basicAuthHeader(DORIS_USER, DORIS_PASSWORD));
            // the label header is optional, not necessary
            // use label header can ensure at most once semantics
            // 用于指定 Doris 该次导入的标签，标签相同的数据无法多次导入。如果不指定 label，Doris 会自动生成一个标签。
            // 用户可以通过指定 label 的方式来避免一份数据重复导入的问题。Doris 默认保留三天内的导入作业标签，可以 label_keep_max_second 调整保留时长。
            // 例如，指定本次导入 label 为 123，需要指定命令 -H "label:123"。label 的使用，可以防止用户重复导入相同的数据。
            // 强烈推荐用户同一批次数据使用相同的 label。这样同一批次数据的重复请求只会被接受一次，保证了 At-Most-Once 当 label 对应的导入作业状态为 CANCELLED 时，该 label 可以再次被使用。
            put.setHeader("label", "39c25a5c-7000-496e-a98e-348a264c81de");
            put.setEntity(entity);

            try (CloseableHttpResponse response = client.execute(put)) {
                String loadResult = "";
                if (response.getEntity() != null) {
                    loadResult = EntityUtils.toString(response.getEntity());
                }
                final int statusCode = response.getStatusLine().getStatusCode();
                // statusCode 200 just indicates that doris be service is ok, not stream load
                // you should see the output content to find whether stream load is success
                if (statusCode != 200) {
                    throw new IOException(
                            String.format("Stream load failed, statusCode=%s load result=%s", statusCode, loadResult));
                }

                System.out.println(loadResult);
            }
        }
    }

    /**
     * 权限认证
     *
     * @param username
     * @param password
     * @return
     */
    private static String basicAuthHeader(String username, String password) {
        final String tobeEncode = username + ":" + password;
        byte[] encoded = Base64.encodeBase64(tobeEncode.getBytes(StandardCharsets.UTF_8));
        return "Basic " + new String(encoded);
    }
}