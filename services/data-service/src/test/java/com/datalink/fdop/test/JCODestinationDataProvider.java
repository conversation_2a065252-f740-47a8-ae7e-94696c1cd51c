package com.datalink.fdop.test;

import com.datalink.fdop.common.core.utils.StringUtils;
import com.sap.conn.jco.ext.DataProviderException;
import com.sap.conn.jco.ext.DestinationDataEventListener;
import com.sap.conn.jco.ext.DestinationDataProvider;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class JCODestinationDataProvider implements DestinationDataProvider {
    // 目标连接属性
    Map<String, Properties> destMap = new HashMap<>();

    public JCODestinationDataProvider(Map<String, Properties> destMap) {
        this.destMap = destMap;
    }

    @Override
    public Properties getDestinationProperties(String destinationName) throws DataProviderException {
        Properties properties = destMap.get(destinationName);
        if (properties != null) {
            return toProperties(properties);
        }
        throw new DataProviderException(DataProviderException.Reason.INTERNAL_ERROR, "Destinantion is not avaliable: " + destinationName, null);
    }

    private Properties toProperties(Properties properties) {
        Properties connProps = new Properties();
        // 两种连接模式 消息服务器/应用服务器
        if (StringUtils.isNotBlank(properties.getProperty("ms-host"))) {
            // 消息服务器地址
            connProps.setProperty(JCO_MSHOST, properties.getProperty("ms-host"));
            // 服务器组
            connProps.setProperty(JCO_GROUP, properties.getProperty("client-group"));
        } else {
            // 应用服务器地址
            connProps.setProperty(JCO_ASHOST, properties.getProperty("as-host"));

        }
        // R3名称
        connProps.setProperty(JCO_R3NAME, properties.getProperty("r3name"));
        // 实例编号
        connProps.setProperty(JCO_SYSNR, properties.getProperty("sysnr"));
        // 用户名
        connProps.setProperty(JCO_USER, properties.getProperty("user"));
        // 密码
        connProps.setProperty(JCO_PASSWD, properties.getProperty("password"));
        // 客户端
        connProps.setProperty(JCO_CLIENT, properties.getProperty("client"));
        // 语言
        connProps.setProperty(JCO_LANG, properties.getProperty("language"));
        // 最大活动连接数
        connProps.setProperty(JCO_PEAK_LIMIT, "10");
        // 最大空闲连接数
        connProps.setProperty(JCO_POOL_CAPACITY, "3");
        return connProps;
    }

    @Override
    public boolean supportsEvents() {
        return false;
    }

    @Override
    public void setDestinationDataEventListener(DestinationDataEventListener destinationDataEventListener) {
        throw new UnsupportedOperationException();
    }

}