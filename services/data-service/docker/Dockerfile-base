# 第一阶段：构建基础环境镜像（可单独构建）
# 保存为 Dockerfile.base
FROM openjdk:8-jre-slim


# 使用清华源
RUN echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main non-free contrib" > /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main non-free contrib" >> /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main non-free contrib" >> /etc/apt/sources.list \
    && echo "Acquire::Check-Valid-Until false;" >> /etc/apt/apt.conf.d/10-no-check-valid

# 安装 Python 3.10 及常用工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        python3 \
        python3-pip \
        python3-venv \
        curl \
        ca-certificates \
        gnupg \
        tzdata \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && python3 --version

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && dpkg-reconfigure -f noninteractive tzdata

# 验证环境
RUN python3 --version