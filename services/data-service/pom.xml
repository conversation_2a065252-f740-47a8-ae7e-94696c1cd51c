<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.datalink-info</groupId>
        <artifactId>services</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>data-service</artifactId>

    <description>
        数据管理服务
    </description>

    <dependencies>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- Common Swagger -->
        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-swagger</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Common Security-->
        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>system-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>data-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-1.2-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.dolphinscheduler</groupId>
            <artifactId>dolphinscheduler-fdop-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-datascope</artifactId>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>datart</groupId>
            <artifactId>datart-core</artifactId>
            <version>${datart.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xml-apis</artifactId>
                    <groupId>xml-apis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>datart</groupId>
            <artifactId>datart-data-provider-base</artifactId>
            <version>${datart.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>datart</groupId>
            <artifactId>datart-jdbc-data-provider</artifactId>
            <version>${datart.version}</version>
        </dependency>

        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>19.3.0.0</version>
        </dependency>

        <dependency>
            <groupId>datart</groupId>
            <artifactId>datart-http-data-provider</artifactId>
            <version>${datart.version}</version>
        </dependency>

        <dependency>
            <groupId>datart</groupId>
            <artifactId>datart-file-data-provider</artifactId>
            <version>${datart.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.43</version>
        </dependency>

        <!-- minio oss-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
            <optional>true</optional>
        </dependency>

        <!-- JimuReport -->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-spring-boot-starter</artifactId>
            <version>${jimu.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- flink 1.8.0 特有 begin -->

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-api-java-uber</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-planner-loader</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-runtime</artifactId>
            <version>${flink.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>flink-shaded-guava</artifactId>
                    <groupId>org.apache.flink</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-jdbc</artifactId>
            <version>3.1.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-csv</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-json</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.flink/flink-sql-connector-kafka -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-sql-connector-kafka</artifactId>
            <version>3.0.2-1.18</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.flink/flink-connector-base -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-base</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.kafka/kafka-clients -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>3.6.0</version>
        </dependency>


        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>sqljdbc4</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.iceberg</groupId>
            <artifactId>iceberg-flink-runtime-1.17</artifactId>
            <version>${iceberg.version}</version>
        </dependency>

        <!--<dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-sql-connector-hive-${hive.version}_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>-->

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.github.jeff-zou</groupId>
            <artifactId>flink-connector-redis</artifactId>
            <version>1.0.11</version>
        </dependency>

        <!-- flink-doris-connector -->
        <dependency>
            <groupId>org.apache.doris</groupId>
            <artifactId>flink-doris-connector-1.14_${scala.binary.version}</artifactId>
            <version>${flink.doris.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- flink 1.8.0 特有 end -->

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.monitorjbl</groupId>
            <artifactId>xlsx-streamer</artifactId>
            <version>2.2.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 数据源依赖  begin -->

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.22</version>
        </dependency>

        <dependency>
            <groupId>io.trino</groupId>
            <artifactId>trino-jdbc</artifactId>
            <version>390</version>
        </dependency>

        <dependency>
            <groupId>com.sap.cloud.db.jdbc</groupId>
            <artifactId>ngdbc</artifactId>
            <version>2.16.14</version>
        </dependency>

        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>19.3.0.0</version>
        </dependency>

        <!-- 主程序 -->
        <dependency>
            <groupId>com.gitee.starblues</groupId>
            <artifactId>spring-brick</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springdoc</groupId>
                    <artifactId>springdoc-openapi-ui</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>sqljdbc4</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.2.141</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.phoenix/phoenix-client-hbase-2.4 -->
        <dependency>
            <groupId>org.apache.phoenix</groupId>
            <artifactId>phoenix-client-hbase-2.4</artifactId>
            <version>5.1.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 数据源依赖  end -->

        <!-- hadoop shade 依赖 begin -->
        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>data-platform-hadoop3-3.1.4-uber</artifactId>
            <classifier>optional</classifier>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.datalink-info</groupId>
            <artifactId>data-platform-hadoop3-aws-uber</artifactId>
            <classifier>optional</classifier>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>jdk.tools</groupId>
                    <artifactId>jdk.tools</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- hadoop shade 依赖 end -->

        <!-- flink kubernetes begin-->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-kubernetes</artifactId>
            <version>1.15.2</version>
        </dependency>

        <dependency>
            <groupId>io.fabric8</groupId>
            <artifactId>kubernetes-client</artifactId>
            <version>4.13.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- flink kubernetes end-->

        <!-- https://mvnrepository.com/artifact/com.sap.jco/sapjco -->
        <dependency>
            <groupId>com.sap.jco3</groupId>
            <artifactId>sapjco</artifactId>
            <version>3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/sapjco3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>4.6</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.mvel/mvel2 -->
        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
            <version>2.5.2.Final</version>
        </dependency>

        <!-- Kafka客户端 -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>3.6.0</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>

        <plugins>

            <!-- 打包主程序插件 -->
            <plugin>
                <groupId>com.gitee.starblues</groupId>
                <artifactId>spring-brick-maven-packager</artifactId>
                <version>${spring-brick.version}</version>
                <configuration>
                    <mode>main</mode>
                    <mainConfig>
                        <mainClass>com.datalink.fdop.DataCenterApplication</mainClass>
                        <packageType>jar</packageType>
                        <!--<outputDirectory>${project.basedir}/plugins/drive</outputDirectory>-->
                    </mainConfig>
                    <excludes>
                        <exclude>
                            <groupId>com.gitee.starblues</groupId>
                            <artifactId>spring-brick-bootstrap</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>docker-mkdir</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <skip>${docker.build.opportunity}</skip>
                            <executable>mkdir</executable>
                            <workingDirectory>${project.build.directory}</workingDirectory>
                            <arguments>
                                <argument>plugins</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>docker-cp</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <skip>${docker.build.opportunity}</skip>
                            <executable>cp</executable>
                            <workingDirectory>${project.parent.parent.basedir}</workingDirectory>
                            <arguments>
                                <argument>-r</argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-doris-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-iceberg-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-mysql-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-postgresql-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-risingwave-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-sqlserver-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-trino-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-hana-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-oracle-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-hive2-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-hive3-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-phoenix-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-ftp-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-sftp-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-dm-2.0.0-repackage.jar
                                </argument>
                                <argument>
                                    ${project.parent.parent.basedir}/plugins/drive-center/2.0.0/fdop-datasource-kafka-2.0.0-repackage.jar
                                </argument>
                                <argument>${project.build.directory}/plugins</argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>
