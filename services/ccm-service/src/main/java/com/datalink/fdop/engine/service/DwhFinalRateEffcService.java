package com.datalink.fdop.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalMps;
import com.datalink.fdop.engine.api.domain.DwhFinalRateEffc;

import java.util.List;

public interface DwhFinalRateEffcService extends IService<DwhFinalRateEffc> {

    PageDataInfo<DwhFinalRateEffc> overview(
            String verId, String controlAreaId, String yearMonthFrom,String yearMonthTo,
            String sort, SearchVo searchVo);

    List<DwhFinalRateEffc> selectNoPage(String verId,
                                   String controlAreaId,
                                   String yearMonthFrom,
                                   String yearMonthTo,
                                   String sort,
                                   SearchVo searchVo);
}
