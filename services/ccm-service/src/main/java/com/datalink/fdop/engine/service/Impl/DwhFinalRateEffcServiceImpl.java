package com.datalink.fdop.engine.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.engine.api.domain.DwhFinalMps;
import com.datalink.fdop.engine.api.domain.DwhFinalRateEffc;
import com.datalink.fdop.engine.mapper.DwhFinalMpsMapper;
import com.datalink.fdop.engine.mapper.DwhFinalRateEffcMapper;
import com.datalink.fdop.engine.service.DwhFinalMpsService;
import com.datalink.fdop.engine.service.DwhFinalRateEffcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DwhFinalRateEffcServiceImpl extends ServiceImpl<DwhFinalRateEffcMapper, DwhFinalRateEffc> implements DwhFinalRateEffcService {

    @Autowired
    private DwhFinalRateEffcMapper dwhFinalRateEffcMapper;

    @Override
    public PageDataInfo<DwhFinalRateEffc> overview(
            String verId, String controlAreaId, String yearMonthFrom,String yearMonthTo,
            String sort, SearchVo searchVo) {
        Page<DwhFinalRateEffc> page = PageUtils.getPage(DwhFinalRateEffc.class);
        IPage<DwhFinalRateEffc> iPage = dwhFinalRateEffcMapper.selectAll(verId, controlAreaId, yearMonthFrom, yearMonthTo,page, sort, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public List<DwhFinalRateEffc> selectNoPage(String verId, String controlAreaId, String yearMonthFrom, String yearMonthTo, String sort, SearchVo searchVo) {
        return dwhFinalRateEffcMapper.selectNoPage(verId, controlAreaId, yearMonthFrom, yearMonthTo, sort, searchVo);
    }


}
