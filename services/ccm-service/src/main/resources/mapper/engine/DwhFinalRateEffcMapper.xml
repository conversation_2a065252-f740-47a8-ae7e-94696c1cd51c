<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.engine.mapper.DwhFinalRateEffcMapper">

    <select id="selectAll" resultType="com.datalink.fdop.engine.api.domain.DwhFinalRateEffc">
        SELECT *
        FROM dwh.dwh_final_rate_effc node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id = #{verId}
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and control_area_id = #{controlAreaId}
            </if>
            <if test=" yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and node.year_month between #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.engine.api.domain.DwhFinalRateEffc">
        SELECT *
        FROM dwh.dwh_final_rate_effc node
        <where>
            <if test="searchVo != null">
                AND
                (${@com.datalink.fdop.common.core.utils.search.SearchUtils@parseSqlSearchCondition("node",searchVo)})
            </if>
            <if test="verId != null and verId != ''">
                and ver_id = #{verId}
            </if>
            <if test="controlAreaId != null and controlAreaId != ''">
                and control_area_id = #{controlAreaId}
            </if>
            <if test=" yearMonthFrom != null and yearMonthFrom != '' and yearMonthTo != null and yearMonthTo != ''">
                and node.year_month between #{yearMonthFrom} and #{yearMonthTo}
            </if>
        </where>
    </select>

</mapper>

