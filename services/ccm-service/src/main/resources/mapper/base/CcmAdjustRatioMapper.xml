<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.base.mapper.CcmAdjustRatioMapper">

    <delete id="deleteBykey">
        delete from zjdata.ccm_adjust_ratio
        WHERE ver_id = #{verId}
          and control_area = #{controlArea}
          and cost_center_id = #{costCenterId}
          and cost_structure_id = #{costStructureId}
    </delete>

    <select id="checkIdUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM zjdata.ccm_adjust_ratio
        WHERE ver_id = #{verId}
          and control_area = #{controlArea}
          and cost_center_id = #{costCenterId}
          and cost_structure_id = #{costStructureId}
        limit 1
    </select>
    
    <update id="updateByKey" parameterType="com.datalink.fdop.base.api.domain.CcmAdjustRatio">
        update zjdata.ccm_adjust_ratio
        set
        <if test="equipGroupId != null and equipGroupId != '' ">equip_group_id = #{equipGroupId},</if>
        <if test="costStructureId != null and costStructureId!='' ">cost_structure_id = #{costStructureId},</if>
        <if test="adjustRatio != null">adjust_ratio = #{adjustRatio},</if>
        <if test="comments != null and comments != '' ">comments = #{comments},</if>
        <if test="enable != null">enable = #{enable},</if>
        update_by = '${@com.datalink.fdop.common.security.utils.SecurityUtils@getUsername()}',
        update_time ='${@com.datalink.fdop.common.core.utils.DateUtils@getTime()}'
        where ver_id = #{verId}
        and control_area = #{controlArea}
        and cost_center_id = #{costCenterId}
        and cost_structure_id = #{costStructureId}
    </update>

    <select id="selectAll" resultType="com.datalink.fdop.base.api.domain.CcmAdjustRatio">
        SELECT a.*, b.cost_center_desc as cost_center_desc,b.cost_center_type as cost_center_type,
        c.cost_structure_desc as cost_structure_desc,c.cost_structure_type as cost_structure_type,
        d.equip_group_desc as equip_group_desc
        FROM zjdata.ccm_adjust_ratio a
        left join zjdata.ccm_cost_center b on a.cost_center_id = b.cost_center_id
        left join zjdata.ccm_cost_structure_head c on a.cost_structure_id = c.cost_structure_id
        left join zjdata.ccm_capacity d on a.equip_group_id = d.equip_group_id
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
        </where>
        ORDER BY a.ver_id,a.control_area,a.cost_center_id,a.cost_structure_id
        ${sort}
    </select>

    <select id="selectNoPage" resultType="com.datalink.fdop.base.api.domain.CcmAdjustRatio">
        SELECT a.*, b.cost_center_desc as cost_center_desc,b.cost_center_type as cost_center_type,
        c.cost_structure_desc as cost_structure_desc,c.cost_structure_type as cost_structure_type,
        d.equip_group_desc as equip_group_desc
        FROM zjdata.ccm_adjust_ratio a
        left join zjdata.ccm_cost_center b on a.cost_center_id = b.cost_center_id
        left join zjdata.ccm_cost_structure_head c on a.cost_structure_id = c.cost_structure_id
        left join zjdata.ccm_capacity d on a.equip_group_id = d.equip_group_id
        <where>
            <if test="searchVo != null and searchVo!= ''">
                and ${searchVo}
            </if>
        </where>
        ORDER BY a.ver_id,a.control_area,a.cost_center_id,a.cost_structure_id
        ${sort}
    </select>

    <update id="updateBatchStatus">
        UPDATE zjdata.ccm_adjust_ratio
        SET enable = #{enable}
        where (ver_id,control_area,cost_center_id,cost_structure_id) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.verId},#{item.controlArea},#{item.costCenterId}},#{item.costStructureId})
        </foreach>
    </update>

</mapper>

